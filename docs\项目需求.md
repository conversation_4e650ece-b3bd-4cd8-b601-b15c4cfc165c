# 多功能测试板项目需求

## 1. 项目背景
本项目旨在开发一款多功能测试板，用于研发团队内部测试嵌入式产品。该测试板集成多种常用接口和功能，可以灵活配置，通过串口与上位机通信，实现各种测试场景。

## 2. 硬件平台
- MCU: STM32L431RCT6
- 外部主晶振: 8MHz
- RTC时钟外部晶振: 32.768KHz

## 3. 功能需求

### 3.1 按键检测功能
- 4路按键输入: PA1(KEY1), PA0(KEY2), PC6(KEY3), PC7(KEY4)
- 输入特性: 按下为低电平
- 可配置按键消抖时间
- 支持按键状态检测和上报

### 3.2 外部输入IO检测
- 2路输入检测: PB4(IL_DI1_IN_H), PB3(IL_DI1_IN_H)
- 输入特性: 硬件上拉电阻
- 可配置检测延时
- 支持输入状态检测和上报

### 3.3 继电器控制输出
- 2路继电器控制: PB5(OL_REALY1_NC), PB8(OL_REALY1_NC)
- 输出特性: 输出为1时继电器为NO，输出为0时继电器为NC
- 支持定时切换功能
- 支持远程控制

### 3.4 热电偶检测接口
- 采用MAX31856MUD芯片，2路SPI接口
- SPI信号: PA6(SPI_MISO), PA7(SPI_MOSI), PA5(SPI_CLK)
- 片选信号: PB0(SPI_CS1), PB2(SPI_CS2)
- 状态信号: PC4(MAX_FAULT1), PA3(MAX_FAULT2), PC5(MAX_DRDY1), PB1(MAX_DRDY2)
- 支持温度数据采集和上报
- 支持故障检测和处理

### 3.5 DS18B20数字温度传感器接口
- 2路单总线接口: PC8(18B20_DATA1), PC9(18B20_DATA2)
- 支持温度数据采集和上报
- 支持多个DS18B20设备挂载(如需要)

### 3.6 ADS1115外部ADC接口
- I2C接口: PB6(I2C_SCL), PB7(I2C_SDA)
- 状态信号: PD2(ADC_RDY)
- I2C地址: 0x48 (0b1001000)
- 4通道ADC，需要轮询查询
- 采集到的电压值需乘以2后输出

### 3.7 INA226电流监测接口
- I2C接口: PB6(I2C_SCL), PB7(I2C_SDA)
- I2C地址: 0x40 (0b1000000)
- 支持电流、电压、功率监测
- 支持采样率配置

### 3.8 UCC27517DBVR动态负载测试
- 控制信号: PA12(O_PWM_LOAD_TEST)
- 支持PWM输出控制
- 支持占空比调节

### 3.9 内部ADC采集
- 2路模拟输入: PC3(ADC_IN1), PC2(ADC_IN2)
- 参考电压: 3.3V (外部基准源)
- 采集数据需乘以2进行显示
- 支持采样速率和采样间隔配置

### 3.10 OLED显示
- I2C接口: PB10(I2C2_SCL), PB11(I2C2_SDA)
- 启动完成后显示"运行中"
- 支持显示关键系统状态和参数

### 3.11 串口通信
- UART接口: PC1(UART_TX), PC0(UART_RX)
- 波特率: 115200
- 支持自定义通信协议
- 用于配置和数据上报

### 3.12 指示灯
- 系统运行指示灯: PC13(SYS_LED)，低电平点亮，系统启动后1Hz闪烁
- 调试指示灯: PB9(DEBUG_LED)，低电平点亮，用于调试和状态指示

## 4. 性能需求
- 系统响应时间: 按键响应时间不超过10ms
- 数据采集精度: 符合各传感器芯片规格要求
- 采样频率: 可根据需求配置，最高支持各芯片允许的最大采样率
- 通信可靠性: 串口通信支持错误检测和处理

## 5. 接口需求
- 硬件接口: 按照硬件设计规格实现各功能模块接口
- 软件接口: 实现串口通信协议，支持配置和数据上报
- 用户接口: 通过串口与上位机通信，支持远程配置和监控

## 6. 其他需求
- 可靠性: 系统稳定运行，支持异常处理和恢复
- 可维护性: 代码模块化设计，便于维护和扩展
- 可测试性: 支持自检功能和测试模式

## 7. 约束条件
- 开发环境: STM32CubeMX + HAL库
- 硬件平台: 已定义的硬件电路和接口
- 开发周期: 待定 