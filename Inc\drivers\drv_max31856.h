/**
 * @file    drv_max31856.h
 * @brief   MAX31856热电偶芯片驱动头文件
 * <AUTHOR>
 * @date    2025-01-28
 */

#ifndef __DRV_MAX31856_H
#define __DRV_MAX31856_H

#include "main.h"

/* MAX31856通道定义 */
typedef enum {
    MAX31856_CHANNEL1 = 0,    // CS1 - PB0
    MAX31856_CHANNEL2 = 1     // CS2 - PB2
} MAX31856_Channel_TypeDef;

/* MAX31856寄存器地址定义 */
#define MAX31856_REG_CR0        0x00    // 配置寄存器0
#define MAX31856_REG_CR1        0x01    // 配置寄存器1
#define MAX31856_REG_MASK       0x02    // 故障屏蔽寄存器
#define MAX31856_REG_CJHF       0x03    // 冷端补偿高故障阈值
#define MAX31856_REG_CJLF       0x04    // 冷端补偿低故障阈值
#define MAX31856_REG_LTHFTH     0x05    // 线性化温度高故障阈值高字节
#define MAX31856_REG_LTHFTL     0x06    // 线性化温度高故障阈值低字节
#define MAX31856_REG_LTLFTH     0x07    // 线性化温度低故障阈值高字节
#define MAX31856_REG_LTLFTL     0x08    // 线性化温度低故障阈值低字节
#define MAX31856_REG_CJTO       0x09    // 冷端温度偏移
#define MAX31856_REG_CJTH       0x0A    // 冷端温度高字节
#define MAX31856_REG_CJTL       0x0B    // 冷端温度低字节
#define MAX31856_REG_LTCBH      0x0C    // 线性化TC温度高字节
#define MAX31856_REG_LTCBM      0x0D    // 线性化TC温度中字节
#define MAX31856_REG_LTCBL      0x0E    // 线性化TC温度低字节
#define MAX31856_REG_SR         0x0F    // 故障状态寄存器

/* 故障状态位定义 */
#define MAX31856_FAULT_CJRANGE  0x80    // 冷端超出范围
#define MAX31856_FAULT_TCRANGE  0x40    // 热电偶超出范围
#define MAX31856_FAULT_CJHIGH   0x20    // 冷端高温故障
#define MAX31856_FAULT_CJLOW    0x10    // 冷端低温故障
#define MAX31856_FAULT_TCHIGH   0x08    // 热电偶高温故障
#define MAX31856_FAULT_TCLOW    0x04    // 热电偶低温故障
#define MAX31856_FAULT_OVUV     0x02    // 过压/欠压故障
#define MAX31856_FAULT_OPEN     0x01    // 开路故障

/* 热电偶类型定义 */
typedef enum {
    MAX31856_TC_TYPE_B = 0x00,
    MAX31856_TC_TYPE_E = 0x01,
    MAX31856_TC_TYPE_J = 0x02,
    MAX31856_TC_TYPE_K = 0x03,
    MAX31856_TC_TYPE_N = 0x04,
    MAX31856_TC_TYPE_R = 0x05,
    MAX31856_TC_TYPE_S = 0x06,
    MAX31856_TC_TYPE_T = 0x07
} MAX31856_TCType_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef DRV_MAX31856_Init(SPI_HandleTypeDef *hspi);
HAL_StatusTypeDef DRV_MAX31856_Config(MAX31856_Channel_TypeDef channel, MAX31856_TCType_TypeDef tc_type);
HAL_StatusTypeDef DRV_MAX31856_ReadTemp(MAX31856_Channel_TypeDef channel, float *temperature);
HAL_StatusTypeDef DRV_MAX31856_ReadColdJunctionTemp(MAX31856_Channel_TypeDef channel, float *temperature);
uint8_t DRV_MAX31856_ReadFault(MAX31856_Channel_TypeDef channel);
HAL_StatusTypeDef DRV_MAX31856_ClearFault(MAX31856_Channel_TypeDef channel);

#endif /* __DRV_MAX31856_H */

