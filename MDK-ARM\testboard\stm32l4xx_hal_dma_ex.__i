--c99 -c --cpu Cortex-M4.fp.sp -g -O2 --apcs=interwork --split_sections -I ../Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc -I ../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I ../Drivers/CMSIS/Include -I ..\Inc\app -I ..\Inc\bsp -I ..\Inc\drivers -I ..\Inc\service
-I.\RTE\_testboard
-Id:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include
-Id:\Keil_v5\Packs\Keil\STM32L4xx_DFP\2.2.0\Drivers\CMSIS\Device\ST\STM32L4xx\Include
-D__UVISION_VERSION="535" -D_RTE_ -DSTM32L431xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32L431xx
-o testboard\stm32l4xx_hal_dma_ex.o --omf_browse testboard\stm32l4xx_hal_dma_ex.crf --depend testboard\stm32l4xx_hal_dma_ex.d "../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma_ex.c"