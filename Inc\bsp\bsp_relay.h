/**
 * @file    bsp_relay.h
 * @brief   继电器控制模块头文件
 * <AUTHOR>
 * @date    2025-01-28
 */

#ifndef __BSP_RELAY_H__
#define __BSP_RELAY_H__

#include "stm32l4xx_hal.h"

/* 继电器通道定义 */
typedef enum {
    RELAY_CHANNEL1 = 0,
    RELAY_CHANNEL2
} RELAY_Channel_TypeDef;

/* 继电器状态定义 */
typedef enum {
    RELAY_OFF = 0,
    RELAY_ON
} RELAY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_RELAY_Init(void);
void BSP_RELAY_On(RELAY_Channel_TypeDef channel);
void BSP_RELAY_Off(RELAY_Channel_TypeDef channel);
void BSP_RELAY_Toggle(RELAY_Channel_TypeDef channel);
RELAY_State_TypeDef BSP_RELAY_GetState(RELAY_Channel_TypeDef channel);
void BSP_RELAY_Process(void);
void BSP_RELAY_SetState(RELAY_Channel_TypeDef channel, RELAY_State_TypeDef state);
void BSP_RELAY_SetToggleMode(RELAY_Channel_TypeDef channel, uint16_t period);
void BSP_RELAY_SetPulseMode(RELAY_Channel_TypeDef channel, uint16_t pulse_width);

#endif /* __BSP_RELAY_H__ */

