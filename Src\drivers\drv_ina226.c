/**
 * @file    drv_ina226.c
 * @brief   INA226电流监测芯片驱动实现
 * <AUTHOR>
 * @date    2025-01-29
 */

#include "drivers/drv_ina226.h"

/* I2C句柄 */
static I2C_HandleTypeDef *ina226_hi2c;

/* 校准参数 */
static float current_lsb = 0.0f;
static float power_lsb = 0.0f;

/**
 * @brief  写寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 写入数据（16位）
 * @retval HAL状态
 */
static HAL_StatusTypeDef INA226_WriteRegister(uint8_t reg_addr, uint16_t data)
{
    uint8_t tx_data[3];
    
    tx_data[0] = reg_addr;
    tx_data[1] = (data >> 8) & 0xFF;  // 高字节
    tx_data[2] = data & 0xFF;         // 低字节
    
    return HAL_I2C_Master_Transmit(ina226_hi2c, INA226_I2C_ADDR << 1, tx_data, 3, 100);
}

/**
 * @brief  读寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 读取数据指针（16位）
 * @retval HAL状态
 */
static HAL_StatusTypeDef INA226_ReadRegister(uint8_t reg_addr, uint16_t *data)
{
    uint8_t rx_data[2];
    HAL_StatusTypeDef status;
    
    /* 发送寄存器地址 */
    status = HAL_I2C_Master_Transmit(ina226_hi2c, INA226_I2C_ADDR << 1, &reg_addr, 1, 100);
    if(status != HAL_OK) return status;
    
    /* 读取数据 */
    status = HAL_I2C_Master_Receive(ina226_hi2c, INA226_I2C_ADDR << 1, rx_data, 2, 100);
    if(status != HAL_OK) return status;
    
    *data = (rx_data[0] << 8) | rx_data[1];
    
    return HAL_OK;
}

/**
 * @brief  INA226初始化
 * @param  hi2c: I2C句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_INA226_Init(I2C_HandleTypeDef *hi2c)
{
    uint16_t manuf_id, die_id;
    uint16_t config_reg;
    HAL_StatusTypeDef status;
    
    ina226_hi2c = hi2c;
    
    /* 读取制造商ID和芯片ID验证通信 */
    status = INA226_ReadRegister(INA226_REG_MANUF_ID, &manuf_id);
    if(status != HAL_OK) return status;
    
    status = INA226_ReadRegister(INA226_REG_DIE_ID, &die_id);
    if(status != HAL_OK) return status;
    
    /* 验证ID */
    if(manuf_id != 0x5449 || die_id != 0x2260) {
        return HAL_ERROR;  // ID不匹配
    }
    
    /* 软件复位 */
    status = INA226_WriteRegister(INA226_REG_CONFIG, INA226_CONFIG_RESET);
    if(status != HAL_OK) return status;
    
    HAL_Delay(10);  // 等待复位完成
    
    /* 配置寄存器 */
    config_reg = INA226_CONFIG_AVG_16 |           // 平均16次
                 INA226_CONFIG_VBUSCT_1100US |    // 总线电压转换时间1.1ms
                 INA226_CONFIG_VSHCT_1100US |     // 分流电压转换时间1.1ms
                 INA226_CONFIG_MODE_SHUNT_BUS_CONT; // 连续测量模式
    
    status = INA226_WriteRegister(INA226_REG_CONFIG, config_reg);
    if(status != HAL_OK) return status;
    
    /* 设置默认校准（0.1Ω分流电阻，最大电流1A） */
    status = DRV_INA226_SetCalibration(0.1f, 1.0f);
    
    return status;
}

/**
 * @brief  设置校准参数
 * @param  shunt_resistance: 分流电阻值（Ω）
 * @param  max_current: 最大电流（A）
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_INA226_SetCalibration(float shunt_resistance, float max_current)
{
    uint16_t calibration_value;
    
    /* 计算电流LSB */
    current_lsb = max_current / 32768.0f;
    
    /* 计算功率LSB */
    power_lsb = current_lsb * 25.0f;
    
    /* 计算校准值 */
    calibration_value = (uint16_t)(0.00512f / (current_lsb * shunt_resistance));
    
    /* 写入校准寄存器 */
    return INA226_WriteRegister(INA226_REG_CALIBRATION, calibration_value);
}

/**
 * @brief  读取总线电压
 * @param  voltage: 电压值指针（单位：V）
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_INA226_ReadVoltage(float *voltage)
{
    uint16_t voltage_reg;
    HAL_StatusTypeDef status;
    
    status = INA226_ReadRegister(INA226_REG_BUS_V, &voltage_reg);
    if(status != HAL_OK) return status;
    
    /* 转换为电压值，LSB = 1.25mV */
    *voltage = (float)voltage_reg * 0.00125f;
    
    return HAL_OK;
}

/**
 * @brief  读取电流
 * @param  current: 电流值指针（单位：A）
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_INA226_ReadCurrent(float *current)
{
    uint16_t current_reg;
    HAL_StatusTypeDef status;
    
    status = INA226_ReadRegister(INA226_REG_CURRENT, &current_reg);
    if(status != HAL_OK) return status;
    
    /* 转换为电流值 */
    *current = (float)((int16_t)current_reg) * current_lsb;
    
    return HAL_OK;
}

/**
 * @brief  读取功率
 * @param  power: 功率值指针（单位：W）
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_INA226_ReadPower(float *power)
{
    uint16_t power_reg;
    HAL_StatusTypeDef status;
    
    status = INA226_ReadRegister(INA226_REG_POWER, &power_reg);
    if(status != HAL_OK) return status;
    
    /* 转换为功率值 */
    *power = (float)power_reg * power_lsb;
    
    return HAL_OK;
}

/**
 * @brief  读取分流电压
 * @param  shunt_voltage: 分流电压值指针（单位：V）
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_INA226_ReadShuntVoltage(float *shunt_voltage)
{
    uint16_t shunt_reg;
    HAL_StatusTypeDef status;
    
    status = INA226_ReadRegister(INA226_REG_SHUNT_V, &shunt_reg);
    if(status != HAL_OK) return status;
    
    /* 转换为分流电压值，LSB = 2.5μV */
    *shunt_voltage = (float)((int16_t)shunt_reg) * 0.0000025f;
    
    return HAL_OK;
}

/**
 * @brief  检查转换是否完成
 * @param  None
 * @retval 1-完成，0-未完成
 */
uint8_t DRV_INA226_IsReady(void)
{
    uint16_t mask_reg;
    
    if(INA226_ReadRegister(INA226_REG_MASK_ENABLE, &mask_reg) == HAL_OK) {
        return (mask_reg & 0x0008) ? 1 : 0;  // 检查转换就绪位
    }
    
    return 0;
}