# 多功能测试板通信协议

## 1. 通信基础参数
- 通信接口: UART
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 流控制: 无

## 2. 协议格式

### 2.1 基本帧格式

| 字段     | 长度(字节) | 描述                                 |
|---------|-----------|-------------------------------------|
| 协议头   | 1         | 固定值: 0x55                         |
| 类型     | 2         | 功能类型，按位表示不同功能             |
| 端口号   | 1         | 对应功能的端口号                      |
| 控制信息 | 4         | 控制参数，根据功能类型有不同含义        |
| 协议尾   | 1         | 固定值: 0xAA                         |

总帧长度: 9字节

### 2.2 功能类型定义

功能类型字段为2字节，按位表示不同功能，1为开启，0为关闭：

| 位  | 功能                      | 对应IO配置功能 |
|-----|--------------------------|--------------|
| 0   | 全部功能打开              | -            |
| 1   | 检测外部按键              | 1            |
| 2   | 检测外部输入              | 2            |
| 3   | 继电器控制输出            | 3            |
| 4   | 热电偶检测接口            | 4            |
| 5   | DS18B20数字输入接口       | 5            |
| 6   | ADS1115的数字接口         | 6            |
| 7   | INA226AIDGSR的数字接口    | 7            |
| 8   | UCC27517DBVR动态负载测试  | 8            |
| 9   | CPU的ADC采集             | 9            |
| 10-15 | 保留                    | -            |

示例:
- 0x0001: 仅开启检测外部按键功能
- 0x0002: 仅开启检测外部输入功能
- 0x0003: 开启检测外部按键和检测外部输入功能
- 0x0100: 仅开启CPU的ADC采集功能

### 2.3 端口号定义

端口号字段为1字节，根据不同功能类型有不同含义：

#### 2.3.1 检测外部按键(功能类型位1)
- 0x1: 仅检测KEY1
- 0x2: 仅检测KEY2
- 0x3: 检测KEY1和KEY2
- 0xF: 检测所有按键(KEY1, KEY2, KEY3, KEY4)

#### 2.3.2 检测外部输入(功能类型位2)
- 0x1: 仅检测PB4(IL_DI1_IN_H)
- 0x2: 仅检测PB3(IL_DI1_IN_H)
- 0x3: 检测所有外部输入

#### 2.3.3 热电偶检测接口(功能类型位4)
- 0x1: 仅采集SPI_CS1片选的设备
- 0x2: 仅采集SPI_CS2片选的设备
- 0x3: 采集所有热电偶设备

其他功能类型的端口号定义类似，按照位表示对应通道。

### 2.4 控制信息定义

控制信息字段为4字节(32位)，根据不同功能类型有不同含义：

#### 2.4.1 检测外部按键(功能类型位1)
每个字节对应一个按键，按顺序为: BYTE0:KEY1, BYTE1:KEY2, BYTE2:KEY3, BYTE3:KEY4
每个字节的值表示按键消抖时间，单位为10ms。

示例:
- 0x00000002: KEY1需要按下20ms才认为有效
- 0x00001002: KEY1需要按下20ms，KEY2需要按下160ms才认为有效
- 0x01010101: 所有按键默认设置，均为10ms消抖时间

#### 2.4.2 检测外部输入(功能类型位2)
参考按键检测，但只有低2字节有效，高2字节保留。

#### 2.4.3 继电器控制输出(功能类型位3)
- 低16位控制PB5
- 高16位控制PB8
- 每个16位中，高8位表示输出1的时间(秒)，低8位表示输出0的时间(秒)

示例:
- 0x03201002: PB5输出0的时间为2秒，输出1的时间为16秒；PB8输出0的时间为32秒，输出1的时间为3秒

#### 2.4.4 热电偶检测接口(功能类型位4)
- 低16位配置PB0:SPI_CS1
- 高16位配置PB2:SPI_CS2
- 具体配置参数待定

#### 2.4.5 DS18B20数字输入接口(功能类型位5)
- 低16位配置PC9
- 高16位配置PC8
- 具体配置参数待定

#### 2.4.6 ADS1115的数字接口(功能类型位6)
四个通道的配置:
- AIN0: bit0-7
- AIN1: bit8-15
- AIN2: bit16-23
- AIN3: bit24-31

每个通道8位配置可用于设置增益、采样率等参数。

#### 2.4.7 INA226AIDGSR的数字接口(功能类型位7)
可配置采样率、平均次数等参数。

#### 2.4.8 UCC27517DBVR动态负载测试(功能类型位8)
可配置PWM频率、占空比等参数。

#### 2.4.9 CPU的ADC采集(功能类型位9)
- 低16位配置PC3
- 高16位配置PC2
- 每个16位可配置采样速率和采样间隔

## 3. 数据上报协议

### 3.1 上报数据帧格式

| 字段     | 长度(字节) | 描述                                 |
|---------|-----------|-------------------------------------|
| 协议头   | 1         | 固定值: 0x55                         |
| 类型     | 2         | 功能类型，与配置协议相同               |
| 端口号   | 1         | 对应功能的端口号                      |
| 数据长度 | 1         | 后续数据字段的长度                    |
| 数据     | N         | 根据功能类型不同，数据格式不同         |
| 协议尾   | 1         | 固定值: 0xAA                         |

### 3.2 各功能数据格式

#### 3.2.1 按键状态上报
- 数据长度: 1字节
- 数据格式: 按位表示各按键状态，1表示按下，0表示释放
  - bit0: KEY1
  - bit1: KEY2
  - bit2: KEY3
  - bit3: KEY4

#### 3.2.2 外部输入状态上报
- 数据长度: 1字节
- 数据格式: 按位表示各输入状态
  - bit0: PB4(IL_DI1_IN_H)
  - bit1: PB3(IL_DI1_IN_H)

#### 3.2.3 热电偶温度上报
- 数据长度: 8字节
- 数据格式: 
  - 字节0-3: SPI_CS1设备温度，浮点数
  - 字节4-7: SPI_CS2设备温度，浮点数

#### 3.2.4 DS18B20温度上报
- 数据长度: 8字节
- 数据格式: 
  - 字节0-3: PC8设备温度，浮点数
  - 字节4-7: PC9设备温度，浮点数

#### 3.2.5 ADS1115数据上报
- 数据长度: 16字节
- 数据格式: 
  - 字节0-3: AIN0通道电压值，浮点数
  - 字节4-7: AIN1通道电压值，浮点数
  - 字节8-11: AIN2通道电压值，浮点数
  - 字节12-15: AIN3通道电压值，浮点数

#### 3.2.6 INA226数据上报
- 数据长度: 12字节
- 数据格式: 
  - 字节0-3: 电压值，浮点数
  - 字节4-7: 电流值，浮点数
  - 字节8-11: 功率值，浮点数

#### 3.2.7 内部ADC数据上报
- 数据长度: 8字节
- 数据格式: 
  - 字节0-3: PC3通道电压值，浮点数
  - 字节4-7: PC2通道电压值，浮点数

## 4. 错误处理

### 4.1 错误帧格式

| 字段     | 长度(字节) | 描述                                 |
|---------|-----------|-------------------------------------|
| 协议头   | 1         | 固定值: 0x55                         |
| 类型     | 2         | 固定值: 0xFFFF，表示错误帧            |
| 错误码   | 1         | 错误类型                             |
| 错误信息 | N         | 错误详细信息，可选                    |
| 协议尾   | 1         | 固定值: 0xAA                         |

### 4.2 错误码定义

| 错误码 | 描述                   |
|-------|------------------------|
| 0x01  | 协议格式错误           |
| 0x02  | 功能类型不支持         |
| 0x03  | 端口号错误             |
| 0x04  | 控制信息错误           |
| 0x05  | 设备未就绪             |
| 0x06  | 设备通信错误           |
| 0x07  | 传感器故障             |
| 0xFF  | 其他未定义错误         |

## 5. 示例

### 5.1 配置按键检测
```
55 00 01 0F 01 01 01 01 AA
```
- 协议头: 0x55
- 类型: 0x0001 (检测外部按键)
- 端口号: 0x0F (检测所有按键)
- 控制信息: 0x01010101 (所有按键消抖时间为10ms)
- 协议尾: 0xAA

### 5.2 配置继电器控制
```
55 00 04 03 03 20 10 02 AA
```
- 协议头: 0x55
- 类型: 0x0004 (继电器控制输出)
- 端口号: 0x03 (控制两路继电器)
- 控制信息: 0x03201002 (PB5输出0的时间为2秒，输出1的时间为16秒；PB8输出0的时间为32秒，输出1的时间为3秒)
- 协议尾: 0xAA

## 6. 注意事项
1. 所有多字节数据均采用小端格式(低字节在前)
2. 浮点数采用IEEE-754标准
3. 协议配置后立即生效
4. 数据上报频率根据各功能特性决定 