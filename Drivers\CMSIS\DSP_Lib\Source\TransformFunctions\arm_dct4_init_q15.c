/* ----------------------------------------------------------------------    
* Copyright (C) 2010-2014 ARM Limited. All rights reserved.    
*    
* $Date:        19. March 2015 
* $Revision: 	V.1.4.5  
*    
* Project: 	    CMSIS DSP Library    
* Title:	    arm_dct4_init_q15.c    
*    
* Description:	Initialization function of DCT-4 & IDCT4 Q15    
*    
* Target Processor: Cortex-M4/Cortex-M3/Cortex-M0
*  
* Redistribution and use in source and binary forms, with or without 
* modification, are permitted provided that the following conditions
* are met:
*   - Redistributions of source code must retain the above copyright
*     notice, this list of conditions and the following disclaimer.
*   - Redistributions in binary form must reproduce the above copyright
*     notice, this list of conditions and the following disclaimer in
*     the documentation and/or other materials provided with the 
*     distribution.
*   - Neither the name of ARM LIMITED nor the names of its contributors
*     may be used to endorse or promote products derived from this
*     software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
* "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE 
* COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
* BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
* LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
* CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
* LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
* ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.    
* -------------------------------------------------------------------- */


#include "arm_math.h"

/**    
 * @ingroup groupTransforms    
 */

/**    
 * @addtogroup DCT4_IDCT4    
 * @{    
 */

/*    
* @brief  Weights Table    
*/

/**    
* \par    
* Weights tables are generated using the formula : <pre>weights[n] = e^(-j*n*pi/(2*N))</pre>    
* \par    
* C command to generate the table    
* <pre>    
* for(i = 0; i< N; i++)    
* {    
*   weights[2*i]= cos(i*c);    
*   weights[(2*i)+1]= -sin(i * c);    
* } </pre>    
* \par    
* where <code>N</code> is the Number of weights to be calculated and <code>c</code> is <code>pi/(2*N)</code>    
* \par    
* Converted the output to q15 format by multiplying with 2^31 and saturated if required.    
* \par    
* In the tables below the real and imaginary values are placed alternatively, hence the    
* array length is <code>2*N</code>.    
*/

static const q15_t ALIGN4 WeightsQ15_128[256] = {
  0x7fff, 0x0, 0x7ffd, 0xfe6e, 0x7ff6, 0xfcdc, 0x7fe9, 0xfb4a,
  0x7fd8, 0xf9b9, 0x7fc2, 0xf827, 0x7fa7, 0xf696, 0x7f87, 0xf505,
  0x7f62, 0xf375, 0x7f38, 0xf1e5, 0x7f09, 0xf055, 0x7ed5, 0xeec7,
  0x7e9d, 0xed38, 0x7e5f, 0xebab, 0x7e1d, 0xea1e, 0x7dd6, 0xe893,
  0x7d8a, 0xe708, 0x7d39, 0xe57e, 0x7ce3, 0xe3f5, 0x7c89, 0xe26d,
  0x7c29, 0xe0e7, 0x7bc5, 0xdf61, 0x7b5d, 0xdddd, 0x7aef, 0xdc5a,
  0x7a7d, 0xdad8, 0x7a05, 0xd958, 0x798a, 0xd7da, 0x7909, 0xd65d,
  0x7884, 0xd4e1, 0x77fa, 0xd368, 0x776c, 0xd1ef, 0x76d9, 0xd079,
  0x7641, 0xcf05, 0x75a5, 0xcd92, 0x7504, 0xcc22, 0x745f, 0xcab3,
  0x73b5, 0xc946, 0x7307, 0xc7dc, 0x7255, 0xc674, 0x719e, 0xc50e,
  0x70e2, 0xc3aa, 0x7023, 0xc248, 0x6f5f, 0xc0e9, 0x6e96, 0xbf8d,
  0x6dca, 0xbe32, 0x6cf9, 0xbcdb, 0x6c24, 0xbb86, 0x6b4a, 0xba33,
  0x6a6d, 0xb8e4, 0x698c, 0xb797, 0x68a6, 0xb64c, 0x67bd, 0xb505,
  0x66cf, 0xb3c1, 0x65dd, 0xb27f, 0x64e8, 0xb141, 0x63ef, 0xb005,
  0x62f2, 0xaecd, 0x61f1, 0xad97, 0x60ec, 0xac65, 0x5fe3, 0xab36,
  0x5ed7, 0xaa0b, 0x5dc7, 0xa8e3, 0x5cb4, 0xa7be, 0x5b9d, 0xa69c,
  0x5a82, 0xa57e, 0x5964, 0xa463, 0x5842, 0xa34c, 0x571d, 0xa239,
  0x55f5, 0xa129, 0x54ca, 0xa01d, 0x539b, 0x9f14, 0x5269, 0x9e0f,
  0x5133, 0x9d0e, 0x4ffb, 0x9c11, 0x4ebf, 0x9b18, 0x4d81, 0x9a23,
  0x4c3f, 0x9931, 0x4afb, 0x9843, 0x49b4, 0x975a, 0x4869, 0x9674,
  0x471c, 0x9593, 0x45cd, 0x94b6, 0x447a, 0x93dc, 0x4325, 0x9307,
  0x41ce, 0x9236, 0x4073, 0x916a, 0x3f17, 0x90a1, 0x3db8, 0x8fdd,
  0x3c56, 0x8f1e, 0x3af2, 0x8e62, 0x398c, 0x8dab, 0x3824, 0x8cf9,
  0x36ba, 0x8c4b, 0x354d, 0x8ba1, 0x33de, 0x8afc, 0x326e, 0x8a5b,
  0x30fb, 0x89bf, 0x2f87, 0x8927, 0x2e11, 0x8894, 0x2c98, 0x8806,
  0x2b1f, 0x877c, 0x29a3, 0x86f7, 0x2826, 0x8676, 0x26a8, 0x85fb,
  0x2528, 0x8583, 0x23a6, 0x8511, 0x2223, 0x84a3, 0x209f, 0x843b,
  0x1f19, 0x83d7, 0x1d93, 0x8377, 0x1c0b, 0x831d, 0x1a82, 0x82c7,
  0x18f8, 0x8276, 0x176d, 0x822a, 0x15e2, 0x81e3, 0x1455, 0x81a1,
  0x12c8, 0x8163, 0x1139, 0x812b, 0xfab, 0x80f7, 0xe1b, 0x80c8,
  0xc8b, 0x809e, 0xafb, 0x8079, 0x96a, 0x8059, 0x7d9, 0x803e,
  0x647, 0x8028, 0x4b6, 0x8017, 0x324, 0x800a, 0x192, 0x8003,
};

static const q15_t ALIGN4 WeightsQ15_512[1024] = {
  0x7fff, 0x0, 0x7fff, 0xff9c, 0x7fff, 0xff37, 0x7ffe, 0xfed3,
  0x7ffd, 0xfe6e, 0x7ffc, 0xfe0a, 0x7ffa, 0xfda5, 0x7ff8, 0xfd41,
  0x7ff6, 0xfcdc, 0x7ff3, 0xfc78, 0x7ff0, 0xfc13, 0x7fed, 0xfbaf,
  0x7fe9, 0xfb4a, 0x7fe5, 0xfae6, 0x7fe1, 0xfa81, 0x7fdd, 0xfa1d,
  0x7fd8, 0xf9b9, 0x7fd3, 0xf954, 0x7fce, 0xf8f0, 0x7fc8, 0xf88b,
  0x7fc2, 0xf827, 0x7fbc, 0xf7c3, 0x7fb5, 0xf75e, 0x7fae, 0xf6fa,
  0x7fa7, 0xf696, 0x7f9f, 0xf632, 0x7f97, 0xf5cd, 0x7f8f, 0xf569,
  0x7f87, 0xf505, 0x7f7e, 0xf4a1, 0x7f75, 0xf43d, 0x7f6b, 0xf3d9,
  0x7f62, 0xf375, 0x7f58, 0xf311, 0x7f4d, 0xf2ad, 0x7f43, 0xf249,
  0x7f38, 0xf1e5, 0x7f2d, 0xf181, 0x7f21, 0xf11d, 0x7f15, 0xf0b9,
  0x7f09, 0xf055, 0x7efd, 0xeff2, 0x7ef0, 0xef8e, 0x7ee3, 0xef2a,
  0x7ed5, 0xeec7, 0x7ec8, 0xee63, 0x7eba, 0xedff, 0x7eab, 0xed9c,
  0x7e9d, 0xed38, 0x7e8e, 0xecd5, 0x7e7f, 0xec72, 0x7e6f, 0xec0e,
  0x7e5f, 0xebab, 0x7e4f, 0xeb48, 0x7e3f, 0xeae5, 0x7e2e, 0xea81,
  0x7e1d, 0xea1e, 0x7e0c, 0xe9bb, 0x7dfa, 0xe958, 0x7de8, 0xe8f6,
  0x7dd6, 0xe893, 0x7dc3, 0xe830, 0x7db0, 0xe7cd, 0x7d9d, 0xe76a,
  0x7d8a, 0xe708, 0x7d76, 0xe6a5, 0x7d62, 0xe643, 0x7d4e, 0xe5e0,
  0x7d39, 0xe57e, 0x7d24, 0xe51c, 0x7d0f, 0xe4b9, 0x7cf9, 0xe457,
  0x7ce3, 0xe3f5, 0x7ccd, 0xe393, 0x7cb7, 0xe331, 0x7ca0, 0xe2cf,
  0x7c89, 0xe26d, 0x7c71, 0xe20b, 0x7c5a, 0xe1aa, 0x7c42, 0xe148,
  0x7c29, 0xe0e7, 0x7c11, 0xe085, 0x7bf8, 0xe024, 0x7bdf, 0xdfc2,
  0x7bc5, 0xdf61, 0x7bac, 0xdf00, 0x7b92, 0xde9f, 0x7b77, 0xde3e,
  0x7b5d, 0xdddd, 0x7b42, 0xdd7c, 0x7b26, 0xdd1b, 0x7b0b, 0xdcbb,
  0x7aef, 0xdc5a, 0x7ad3, 0xdbf9, 0x7ab6, 0xdb99, 0x7a9a, 0xdb39,
  0x7a7d, 0xdad8, 0x7a5f, 0xda78, 0x7a42, 0xda18, 0x7a24, 0xd9b8,
  0x7a05, 0xd958, 0x79e7, 0xd8f9, 0x79c8, 0xd899, 0x79a9, 0xd839,
  0x798a, 0xd7da, 0x796a, 0xd77a, 0x794a, 0xd71b, 0x792a, 0xd6bc,
  0x7909, 0xd65d, 0x78e8, 0xd5fe, 0x78c7, 0xd59f, 0x78a6, 0xd540,
  0x7884, 0xd4e1, 0x7862, 0xd483, 0x7840, 0xd424, 0x781d, 0xd3c6,
  0x77fa, 0xd368, 0x77d7, 0xd309, 0x77b4, 0xd2ab, 0x7790, 0xd24d,
  0x776c, 0xd1ef, 0x7747, 0xd192, 0x7723, 0xd134, 0x76fe, 0xd0d7,
  0x76d9, 0xd079, 0x76b3, 0xd01c, 0x768e, 0xcfbf, 0x7668, 0xcf62,
  0x7641, 0xcf05, 0x761b, 0xcea8, 0x75f4, 0xce4b, 0x75cc, 0xcdef,
  0x75a5, 0xcd92, 0x757d, 0xcd36, 0x7555, 0xccda, 0x752d, 0xcc7e,
  0x7504, 0xcc22, 0x74db, 0xcbc6, 0x74b2, 0xcb6a, 0x7489, 0xcb0e,
  0x745f, 0xcab3, 0x7435, 0xca58, 0x740b, 0xc9fc, 0x73e0, 0xc9a1,
  0x73b5, 0xc946, 0x738a, 0xc8ec, 0x735f, 0xc891, 0x7333, 0xc836,
  0x7307, 0xc7dc, 0x72db, 0xc782, 0x72af, 0xc728, 0x7282, 0xc6ce,
  0x7255, 0xc674, 0x7227, 0xc61a, 0x71fa, 0xc5c0, 0x71cc, 0xc567,
  0x719e, 0xc50e, 0x716f, 0xc4b4, 0x7141, 0xc45b, 0x7112, 0xc403,
  0x70e2, 0xc3aa, 0x70b3, 0xc351, 0x7083, 0xc2f9, 0x7053, 0xc2a0,
  0x7023, 0xc248, 0x6ff2, 0xc1f0, 0x6fc1, 0xc198, 0x6f90, 0xc141,
  0x6f5f, 0xc0e9, 0x6f2d, 0xc092, 0x6efb, 0xc03b, 0x6ec9, 0xbfe3,
  0x6e96, 0xbf8d, 0x6e63, 0xbf36, 0x6e30, 0xbedf, 0x6dfd, 0xbe89,
  0x6dca, 0xbe32, 0x6d96, 0xbddc, 0x6d62, 0xbd86, 0x6d2d, 0xbd30,
  0x6cf9, 0xbcdb, 0x6cc4, 0xbc85, 0x6c8f, 0xbc30, 0x6c59, 0xbbdb,
  0x6c24, 0xbb86, 0x6bee, 0xbb31, 0x6bb8, 0xbadc, 0x6b81, 0xba88,
  0x6b4a, 0xba33, 0x6b13, 0xb9df, 0x6adc, 0xb98b, 0x6aa5, 0xb937,
  0x6a6d, 0xb8e4, 0x6a35, 0xb890, 0x69fd, 0xb83d, 0x69c4, 0xb7ea,
  0x698c, 0xb797, 0x6953, 0xb744, 0x6919, 0xb6f1, 0x68e0, 0xb69f,
  0x68a6, 0xb64c, 0x686c, 0xb5fa, 0x6832, 0xb5a8, 0x67f7, 0xb557,
  0x67bd, 0xb505, 0x6782, 0xb4b4, 0x6746, 0xb462, 0x670b, 0xb411,
  0x66cf, 0xb3c1, 0x6693, 0xb370, 0x6657, 0xb31f, 0x661a, 0xb2cf,
  0x65dd, 0xb27f, 0x65a0, 0xb22f, 0x6563, 0xb1df, 0x6526, 0xb190,
  0x64e8, 0xb141, 0x64aa, 0xb0f1, 0x646c, 0xb0a2, 0x642d, 0xb054,
  0x63ef, 0xb005, 0x63b0, 0xafb7, 0x6371, 0xaf69, 0x6331, 0xaf1b,
  0x62f2, 0xaecd, 0x62b2, 0xae7f, 0x6271, 0xae32, 0x6231, 0xade4,
  0x61f1, 0xad97, 0x61b0, 0xad4b, 0x616f, 0xacfe, 0x612d, 0xacb2,
  0x60ec, 0xac65, 0x60aa, 0xac19, 0x6068, 0xabcd, 0x6026, 0xab82,
  0x5fe3, 0xab36, 0x5fa0, 0xaaeb, 0x5f5e, 0xaaa0, 0x5f1a, 0xaa55,
  0x5ed7, 0xaa0b, 0x5e93, 0xa9c0, 0x5e50, 0xa976, 0x5e0b, 0xa92c,
  0x5dc7, 0xa8e3, 0x5d83, 0xa899, 0x5d3e, 0xa850, 0x5cf9, 0xa807,
  0x5cb4, 0xa7be, 0x5c6e, 0xa775, 0x5c29, 0xa72c, 0x5be3, 0xa6e4,
  0x5b9d, 0xa69c, 0x5b56, 0xa654, 0x5b10, 0xa60d, 0x5ac9, 0xa5c5,
  0x5a82, 0xa57e, 0x5a3b, 0xa537, 0x59f3, 0xa4f0, 0x59ac, 0xa4aa,
  0x5964, 0xa463, 0x591c, 0xa41d, 0x58d4, 0xa3d7, 0x588b, 0xa392,
  0x5842, 0xa34c, 0x57f9, 0xa307, 0x57b0, 0xa2c2, 0x5767, 0xa27d,
  0x571d, 0xa239, 0x56d4, 0xa1f5, 0x568a, 0xa1b0, 0x5640, 0xa16d,
  0x55f5, 0xa129, 0x55ab, 0xa0e6, 0x5560, 0xa0a2, 0x5515, 0xa060,
  0x54ca, 0xa01d, 0x547e, 0x9fda, 0x5433, 0x9f98, 0x53e7, 0x9f56,
  0x539b, 0x9f14, 0x534e, 0x9ed3, 0x5302, 0x9e91, 0x52b5, 0x9e50,
  0x5269, 0x9e0f, 0x521c, 0x9dcf, 0x51ce, 0x9d8f, 0x5181, 0x9d4e,
  0x5133, 0x9d0e, 0x50e5, 0x9ccf, 0x5097, 0x9c8f, 0x5049, 0x9c50,
  0x4ffb, 0x9c11, 0x4fac, 0x9bd3, 0x4f5e, 0x9b94, 0x4f0f, 0x9b56,
  0x4ebf, 0x9b18, 0x4e70, 0x9ada, 0x4e21, 0x9a9d, 0x4dd1, 0x9a60,
  0x4d81, 0x9a23, 0x4d31, 0x99e6, 0x4ce1, 0x99a9, 0x4c90, 0x996d,
  0x4c3f, 0x9931, 0x4bef, 0x98f5, 0x4b9e, 0x98ba, 0x4b4c, 0x987e,
  0x4afb, 0x9843, 0x4aa9, 0x9809, 0x4a58, 0x97ce, 0x4a06, 0x9794,
  0x49b4, 0x975a, 0x4961, 0x9720, 0x490f, 0x96e7, 0x48bc, 0x96ad,
  0x4869, 0x9674, 0x4816, 0x963c, 0x47c3, 0x9603, 0x4770, 0x95cb,
  0x471c, 0x9593, 0x46c9, 0x955b, 0x4675, 0x9524, 0x4621, 0x94ed,
  0x45cd, 0x94b6, 0x4578, 0x947f, 0x4524, 0x9448, 0x44cf, 0x9412,
  0x447a, 0x93dc, 0x4425, 0x93a7, 0x43d0, 0x9371, 0x437b, 0x933c,
  0x4325, 0x9307, 0x42d0, 0x92d3, 0x427a, 0x929e, 0x4224, 0x926a,
  0x41ce, 0x9236, 0x4177, 0x9203, 0x4121, 0x91d0, 0x40ca, 0x919d,
  0x4073, 0x916a, 0x401d, 0x9137, 0x3fc5, 0x9105, 0x3f6e, 0x90d3,
  0x3f17, 0x90a1, 0x3ebf, 0x9070, 0x3e68, 0x903f, 0x3e10, 0x900e,
  0x3db8, 0x8fdd, 0x3d60, 0x8fad, 0x3d07, 0x8f7d, 0x3caf, 0x8f4d,
  0x3c56, 0x8f1e, 0x3bfd, 0x8eee, 0x3ba5, 0x8ebf, 0x3b4c, 0x8e91,
  0x3af2, 0x8e62, 0x3a99, 0x8e34, 0x3a40, 0x8e06, 0x39e6, 0x8dd9,
  0x398c, 0x8dab, 0x3932, 0x8d7e, 0x38d8, 0x8d51, 0x387e, 0x8d25,
  0x3824, 0x8cf9, 0x37ca, 0x8ccd, 0x376f, 0x8ca1, 0x3714, 0x8c76,
  0x36ba, 0x8c4b, 0x365f, 0x8c20, 0x3604, 0x8bf5, 0x35a8, 0x8bcb,
  0x354d, 0x8ba1, 0x34f2, 0x8b77, 0x3496, 0x8b4e, 0x343a, 0x8b25,
  0x33de, 0x8afc, 0x3382, 0x8ad3, 0x3326, 0x8aab, 0x32ca, 0x8a83,
  0x326e, 0x8a5b, 0x3211, 0x8a34, 0x31b5, 0x8a0c, 0x3158, 0x89e5,
  0x30fb, 0x89bf, 0x309e, 0x8998, 0x3041, 0x8972, 0x2fe4, 0x894d,
  0x2f87, 0x8927, 0x2f29, 0x8902, 0x2ecc, 0x88dd, 0x2e6e, 0x88b9,
  0x2e11, 0x8894, 0x2db3, 0x8870, 0x2d55, 0x884c, 0x2cf7, 0x8829,
  0x2c98, 0x8806, 0x2c3a, 0x87e3, 0x2bdc, 0x87c0, 0x2b7d, 0x879e,
  0x2b1f, 0x877c, 0x2ac0, 0x875a, 0x2a61, 0x8739, 0x2a02, 0x8718,
  0x29a3, 0x86f7, 0x2944, 0x86d6, 0x28e5, 0x86b6, 0x2886, 0x8696,
  0x2826, 0x8676, 0x27c7, 0x8657, 0x2767, 0x8638, 0x2707, 0x8619,
  0x26a8, 0x85fb, 0x2648, 0x85dc, 0x25e8, 0x85be, 0x2588, 0x85a1,
  0x2528, 0x8583, 0x24c7, 0x8566, 0x2467, 0x854a, 0x2407, 0x852d,
  0x23a6, 0x8511, 0x2345, 0x84f5, 0x22e5, 0x84da, 0x2284, 0x84be,
  0x2223, 0x84a3, 0x21c2, 0x8489, 0x2161, 0x846e, 0x2100, 0x8454,
  0x209f, 0x843b, 0x203e, 0x8421, 0x1fdc, 0x8408, 0x1f7b, 0x83ef,
  0x1f19, 0x83d7, 0x1eb8, 0x83be, 0x1e56, 0x83a6, 0x1df5, 0x838f,
  0x1d93, 0x8377, 0x1d31, 0x8360, 0x1ccf, 0x8349, 0x1c6d, 0x8333,
  0x1c0b, 0x831d, 0x1ba9, 0x8307, 0x1b47, 0x82f1, 0x1ae4, 0x82dc,
  0x1a82, 0x82c7, 0x1a20, 0x82b2, 0x19bd, 0x829e, 0x195b, 0x828a,
  0x18f8, 0x8276, 0x1896, 0x8263, 0x1833, 0x8250, 0x17d0, 0x823d,
  0x176d, 0x822a, 0x170a, 0x8218, 0x16a8, 0x8206, 0x1645, 0x81f4,
  0x15e2, 0x81e3, 0x157f, 0x81d2, 0x151b, 0x81c1, 0x14b8, 0x81b1,
  0x1455, 0x81a1, 0x13f2, 0x8191, 0x138e, 0x8181, 0x132b, 0x8172,
  0x12c8, 0x8163, 0x1264, 0x8155, 0x1201, 0x8146, 0x119d, 0x8138,
  0x1139, 0x812b, 0x10d6, 0x811d, 0x1072, 0x8110, 0x100e, 0x8103,
  0xfab, 0x80f7, 0xf47, 0x80eb, 0xee3, 0x80df, 0xe7f, 0x80d3,
  0xe1b, 0x80c8, 0xdb7, 0x80bd, 0xd53, 0x80b3, 0xcef, 0x80a8,
  0xc8b, 0x809e, 0xc27, 0x8095, 0xbc3, 0x808b, 0xb5f, 0x8082,
  0xafb, 0x8079, 0xa97, 0x8071, 0xa33, 0x8069, 0x9ce, 0x8061,
  0x96a, 0x8059, 0x906, 0x8052, 0x8a2, 0x804b, 0x83d, 0x8044,
  0x7d9, 0x803e, 0x775, 0x8038, 0x710, 0x8032, 0x6ac, 0x802d,
  0x647, 0x8028, 0x5e3, 0x8023, 0x57f, 0x801f, 0x51a, 0x801b,
  0x4b6, 0x8017, 0x451, 0x8013, 0x3ed, 0x8010, 0x388, 0x800d,
  0x324, 0x800a, 0x2bf, 0x8008, 0x25b, 0x8006, 0x1f6, 0x8004,
  0x192, 0x8003, 0x12d, 0x8002, 0xc9, 0x8001, 0x64, 0x8001,
};

static const q15_t ALIGN4 WeightsQ15_2048[4096] = {
  0x7fff, 0x0, 0x7fff, 0xffe7, 0x7fff, 0xffce, 0x7fff, 0xffb5,
  0x7fff, 0xff9c, 0x7fff, 0xff83, 0x7fff, 0xff6a, 0x7fff, 0xff51,
  0x7fff, 0xff37, 0x7fff, 0xff1e, 0x7fff, 0xff05, 0x7ffe, 0xfeec,
  0x7ffe, 0xfed3, 0x7ffe, 0xfeba, 0x7ffe, 0xfea1, 0x7ffd, 0xfe88,
  0x7ffd, 0xfe6e, 0x7ffd, 0xfe55, 0x7ffc, 0xfe3c, 0x7ffc, 0xfe23,
  0x7ffc, 0xfe0a, 0x7ffb, 0xfdf1, 0x7ffb, 0xfdd8, 0x7ffa, 0xfdbe,
  0x7ffa, 0xfda5, 0x7ff9, 0xfd8c, 0x7ff9, 0xfd73, 0x7ff8, 0xfd5a,
  0x7ff8, 0xfd41, 0x7ff7, 0xfd28, 0x7ff7, 0xfd0f, 0x7ff6, 0xfcf5,
  0x7ff6, 0xfcdc, 0x7ff5, 0xfcc3, 0x7ff4, 0xfcaa, 0x7ff4, 0xfc91,
  0x7ff3, 0xfc78, 0x7ff2, 0xfc5f, 0x7ff2, 0xfc46, 0x7ff1, 0xfc2c,
  0x7ff0, 0xfc13, 0x7fef, 0xfbfa, 0x7fee, 0xfbe1, 0x7fee, 0xfbc8,
  0x7fed, 0xfbaf, 0x7fec, 0xfb96, 0x7feb, 0xfb7d, 0x7fea, 0xfb64,
  0x7fe9, 0xfb4a, 0x7fe8, 0xfb31, 0x7fe7, 0xfb18, 0x7fe6, 0xfaff,
  0x7fe5, 0xfae6, 0x7fe4, 0xfacd, 0x7fe3, 0xfab4, 0x7fe2, 0xfa9b,
  0x7fe1, 0xfa81, 0x7fe0, 0xfa68, 0x7fdf, 0xfa4f, 0x7fde, 0xfa36,
  0x7fdd, 0xfa1d, 0x7fdc, 0xfa04, 0x7fda, 0xf9eb, 0x7fd9, 0xf9d2,
  0x7fd8, 0xf9b9, 0x7fd7, 0xf9a0, 0x7fd6, 0xf986, 0x7fd4, 0xf96d,
  0x7fd3, 0xf954, 0x7fd2, 0xf93b, 0x7fd0, 0xf922, 0x7fcf, 0xf909,
  0x7fce, 0xf8f0, 0x7fcc, 0xf8d7, 0x7fcb, 0xf8be, 0x7fc9, 0xf8a5,
  0x7fc8, 0xf88b, 0x7fc6, 0xf872, 0x7fc5, 0xf859, 0x7fc3, 0xf840,
  0x7fc2, 0xf827, 0x7fc0, 0xf80e, 0x7fbf, 0xf7f5, 0x7fbd, 0xf7dc,
  0x7fbc, 0xf7c3, 0x7fba, 0xf7aa, 0x7fb8, 0xf791, 0x7fb7, 0xf778,
  0x7fb5, 0xf75e, 0x7fb3, 0xf745, 0x7fb1, 0xf72c, 0x7fb0, 0xf713,
  0x7fae, 0xf6fa, 0x7fac, 0xf6e1, 0x7faa, 0xf6c8, 0x7fa9, 0xf6af,
  0x7fa7, 0xf696, 0x7fa5, 0xf67d, 0x7fa3, 0xf664, 0x7fa1, 0xf64b,
  0x7f9f, 0xf632, 0x7f9d, 0xf619, 0x7f9b, 0xf600, 0x7f99, 0xf5e7,
  0x7f97, 0xf5cd, 0x7f95, 0xf5b4, 0x7f93, 0xf59b, 0x7f91, 0xf582,
  0x7f8f, 0xf569, 0x7f8d, 0xf550, 0x7f8b, 0xf537, 0x7f89, 0xf51e,
  0x7f87, 0xf505, 0x7f85, 0xf4ec, 0x7f82, 0xf4d3, 0x7f80, 0xf4ba,
  0x7f7e, 0xf4a1, 0x7f7c, 0xf488, 0x7f79, 0xf46f, 0x7f77, 0xf456,
  0x7f75, 0xf43d, 0x7f72, 0xf424, 0x7f70, 0xf40b, 0x7f6e, 0xf3f2,
  0x7f6b, 0xf3d9, 0x7f69, 0xf3c0, 0x7f67, 0xf3a7, 0x7f64, 0xf38e,
  0x7f62, 0xf375, 0x7f5f, 0xf35c, 0x7f5d, 0xf343, 0x7f5a, 0xf32a,
  0x7f58, 0xf311, 0x7f55, 0xf2f8, 0x7f53, 0xf2df, 0x7f50, 0xf2c6,
  0x7f4d, 0xf2ad, 0x7f4b, 0xf294, 0x7f48, 0xf27b, 0x7f45, 0xf262,
  0x7f43, 0xf249, 0x7f40, 0xf230, 0x7f3d, 0xf217, 0x7f3b, 0xf1fe,
  0x7f38, 0xf1e5, 0x7f35, 0xf1cc, 0x7f32, 0xf1b3, 0x7f2f, 0xf19a,
  0x7f2d, 0xf181, 0x7f2a, 0xf168, 0x7f27, 0xf14f, 0x7f24, 0xf136,
  0x7f21, 0xf11d, 0x7f1e, 0xf104, 0x7f1b, 0xf0eb, 0x7f18, 0xf0d2,
  0x7f15, 0xf0b9, 0x7f12, 0xf0a0, 0x7f0f, 0xf087, 0x7f0c, 0xf06e,
  0x7f09, 0xf055, 0x7f06, 0xf03c, 0x7f03, 0xf023, 0x7f00, 0xf00b,
  0x7efd, 0xeff2, 0x7ef9, 0xefd9, 0x7ef6, 0xefc0, 0x7ef3, 0xefa7,
  0x7ef0, 0xef8e, 0x7eed, 0xef75, 0x7ee9, 0xef5c, 0x7ee6, 0xef43,
  0x7ee3, 0xef2a, 0x7edf, 0xef11, 0x7edc, 0xeef8, 0x7ed9, 0xeedf,
  0x7ed5, 0xeec7, 0x7ed2, 0xeeae, 0x7ecf, 0xee95, 0x7ecb, 0xee7c,
  0x7ec8, 0xee63, 0x7ec4, 0xee4a, 0x7ec1, 0xee31, 0x7ebd, 0xee18,
  0x7eba, 0xedff, 0x7eb6, 0xede7, 0x7eb3, 0xedce, 0x7eaf, 0xedb5,
  0x7eab, 0xed9c, 0x7ea8, 0xed83, 0x7ea4, 0xed6a, 0x7ea1, 0xed51,
  0x7e9d, 0xed38, 0x7e99, 0xed20, 0x7e95, 0xed07, 0x7e92, 0xecee,
  0x7e8e, 0xecd5, 0x7e8a, 0xecbc, 0x7e86, 0xeca3, 0x7e83, 0xec8a,
  0x7e7f, 0xec72, 0x7e7b, 0xec59, 0x7e77, 0xec40, 0x7e73, 0xec27,
  0x7e6f, 0xec0e, 0x7e6b, 0xebf5, 0x7e67, 0xebdd, 0x7e63, 0xebc4,
  0x7e5f, 0xebab, 0x7e5b, 0xeb92, 0x7e57, 0xeb79, 0x7e53, 0xeb61,
  0x7e4f, 0xeb48, 0x7e4b, 0xeb2f, 0x7e47, 0xeb16, 0x7e43, 0xeafd,
  0x7e3f, 0xeae5, 0x7e3b, 0xeacc, 0x7e37, 0xeab3, 0x7e32, 0xea9a,
  0x7e2e, 0xea81, 0x7e2a, 0xea69, 0x7e26, 0xea50, 0x7e21, 0xea37,
  0x7e1d, 0xea1e, 0x7e19, 0xea06, 0x7e14, 0xe9ed, 0x7e10, 0xe9d4,
  0x7e0c, 0xe9bb, 0x7e07, 0xe9a3, 0x7e03, 0xe98a, 0x7dff, 0xe971,
  0x7dfa, 0xe958, 0x7df6, 0xe940, 0x7df1, 0xe927, 0x7ded, 0xe90e,
  0x7de8, 0xe8f6, 0x7de4, 0xe8dd, 0x7ddf, 0xe8c4, 0x7dda, 0xe8ab,
  0x7dd6, 0xe893, 0x7dd1, 0xe87a, 0x7dcd, 0xe861, 0x7dc8, 0xe849,
  0x7dc3, 0xe830, 0x7dbf, 0xe817, 0x7dba, 0xe7fe, 0x7db5, 0xe7e6,
  0x7db0, 0xe7cd, 0x7dac, 0xe7b4, 0x7da7, 0xe79c, 0x7da2, 0xe783,
  0x7d9d, 0xe76a, 0x7d98, 0xe752, 0x7d94, 0xe739, 0x7d8f, 0xe720,
  0x7d8a, 0xe708, 0x7d85, 0xe6ef, 0x7d80, 0xe6d6, 0x7d7b, 0xe6be,
  0x7d76, 0xe6a5, 0x7d71, 0xe68d, 0x7d6c, 0xe674, 0x7d67, 0xe65b,
  0x7d62, 0xe643, 0x7d5d, 0xe62a, 0x7d58, 0xe611, 0x7d53, 0xe5f9,
  0x7d4e, 0xe5e0, 0x7d49, 0xe5c8, 0x7d43, 0xe5af, 0x7d3e, 0xe596,
  0x7d39, 0xe57e, 0x7d34, 0xe565, 0x7d2f, 0xe54d, 0x7d29, 0xe534,
  0x7d24, 0xe51c, 0x7d1f, 0xe503, 0x7d19, 0xe4ea, 0x7d14, 0xe4d2,
  0x7d0f, 0xe4b9, 0x7d09, 0xe4a1, 0x7d04, 0xe488, 0x7cff, 0xe470,
  0x7cf9, 0xe457, 0x7cf4, 0xe43f, 0x7cee, 0xe426, 0x7ce9, 0xe40e,
  0x7ce3, 0xe3f5, 0x7cde, 0xe3dc, 0x7cd8, 0xe3c4, 0x7cd3, 0xe3ab,
  0x7ccd, 0xe393, 0x7cc8, 0xe37a, 0x7cc2, 0xe362, 0x7cbc, 0xe349,
  0x7cb7, 0xe331, 0x7cb1, 0xe318, 0x7cab, 0xe300, 0x7ca6, 0xe2e8,
  0x7ca0, 0xe2cf, 0x7c9a, 0xe2b7, 0x7c94, 0xe29e, 0x7c8f, 0xe286,
  0x7c89, 0xe26d, 0x7c83, 0xe255, 0x7c7d, 0xe23c, 0x7c77, 0xe224,
  0x7c71, 0xe20b, 0x7c6c, 0xe1f3, 0x7c66, 0xe1db, 0x7c60, 0xe1c2,
  0x7c5a, 0xe1aa, 0x7c54, 0xe191, 0x7c4e, 0xe179, 0x7c48, 0xe160,
  0x7c42, 0xe148, 0x7c3c, 0xe130, 0x7c36, 0xe117, 0x7c30, 0xe0ff,
  0x7c29, 0xe0e7, 0x7c23, 0xe0ce, 0x7c1d, 0xe0b6, 0x7c17, 0xe09d,
  0x7c11, 0xe085, 0x7c0b, 0xe06d, 0x7c05, 0xe054, 0x7bfe, 0xe03c,
  0x7bf8, 0xe024, 0x7bf2, 0xe00b, 0x7beb, 0xdff3, 0x7be5, 0xdfdb,
  0x7bdf, 0xdfc2, 0x7bd9, 0xdfaa, 0x7bd2, 0xdf92, 0x7bcc, 0xdf79,
  0x7bc5, 0xdf61, 0x7bbf, 0xdf49, 0x7bb9, 0xdf30, 0x7bb2, 0xdf18,
  0x7bac, 0xdf00, 0x7ba5, 0xdee8, 0x7b9f, 0xdecf, 0x7b98, 0xdeb7,
  0x7b92, 0xde9f, 0x7b8b, 0xde87, 0x7b84, 0xde6e, 0x7b7e, 0xde56,
  0x7b77, 0xde3e, 0x7b71, 0xde26, 0x7b6a, 0xde0d, 0x7b63, 0xddf5,
  0x7b5d, 0xdddd, 0x7b56, 0xddc5, 0x7b4f, 0xddac, 0x7b48, 0xdd94,
  0x7b42, 0xdd7c, 0x7b3b, 0xdd64, 0x7b34, 0xdd4c, 0x7b2d, 0xdd33,
  0x7b26, 0xdd1b, 0x7b1f, 0xdd03, 0x7b19, 0xdceb, 0x7b12, 0xdcd3,
  0x7b0b, 0xdcbb, 0x7b04, 0xdca2, 0x7afd, 0xdc8a, 0x7af6, 0xdc72,
  0x7aef, 0xdc5a, 0x7ae8, 0xdc42, 0x7ae1, 0xdc2a, 0x7ada, 0xdc12,
  0x7ad3, 0xdbf9, 0x7acc, 0xdbe1, 0x7ac5, 0xdbc9, 0x7abd, 0xdbb1,
  0x7ab6, 0xdb99, 0x7aaf, 0xdb81, 0x7aa8, 0xdb69, 0x7aa1, 0xdb51,
  0x7a9a, 0xdb39, 0x7a92, 0xdb21, 0x7a8b, 0xdb09, 0x7a84, 0xdaf1,
  0x7a7d, 0xdad8, 0x7a75, 0xdac0, 0x7a6e, 0xdaa8, 0x7a67, 0xda90,
  0x7a5f, 0xda78, 0x7a58, 0xda60, 0x7a50, 0xda48, 0x7a49, 0xda30,
  0x7a42, 0xda18, 0x7a3a, 0xda00, 0x7a33, 0xd9e8, 0x7a2b, 0xd9d0,
  0x7a24, 0xd9b8, 0x7a1c, 0xd9a0, 0x7a15, 0xd988, 0x7a0d, 0xd970,
  0x7a05, 0xd958, 0x79fe, 0xd940, 0x79f6, 0xd928, 0x79ef, 0xd911,
  0x79e7, 0xd8f9, 0x79df, 0xd8e1, 0x79d8, 0xd8c9, 0x79d0, 0xd8b1,
  0x79c8, 0xd899, 0x79c0, 0xd881, 0x79b9, 0xd869, 0x79b1, 0xd851,
  0x79a9, 0xd839, 0x79a1, 0xd821, 0x7999, 0xd80a, 0x7992, 0xd7f2,
  0x798a, 0xd7da, 0x7982, 0xd7c2, 0x797a, 0xd7aa, 0x7972, 0xd792,
  0x796a, 0xd77a, 0x7962, 0xd763, 0x795a, 0xd74b, 0x7952, 0xd733,
  0x794a, 0xd71b, 0x7942, 0xd703, 0x793a, 0xd6eb, 0x7932, 0xd6d4,
  0x792a, 0xd6bc, 0x7922, 0xd6a4, 0x7919, 0xd68c, 0x7911, 0xd675,
  0x7909, 0xd65d, 0x7901, 0xd645, 0x78f9, 0xd62d, 0x78f1, 0xd615,
  0x78e8, 0xd5fe, 0x78e0, 0xd5e6, 0x78d8, 0xd5ce, 0x78cf, 0xd5b7,
  0x78c7, 0xd59f, 0x78bf, 0xd587, 0x78b6, 0xd56f, 0x78ae, 0xd558,
  0x78a6, 0xd540, 0x789d, 0xd528, 0x7895, 0xd511, 0x788c, 0xd4f9,
  0x7884, 0xd4e1, 0x787c, 0xd4ca, 0x7873, 0xd4b2, 0x786b, 0xd49a,
  0x7862, 0xd483, 0x7859, 0xd46b, 0x7851, 0xd453, 0x7848, 0xd43c,
  0x7840, 0xd424, 0x7837, 0xd40d, 0x782e, 0xd3f5, 0x7826, 0xd3dd,
  0x781d, 0xd3c6, 0x7814, 0xd3ae, 0x780c, 0xd397, 0x7803, 0xd37f,
  0x77fa, 0xd368, 0x77f1, 0xd350, 0x77e9, 0xd338, 0x77e0, 0xd321,
  0x77d7, 0xd309, 0x77ce, 0xd2f2, 0x77c5, 0xd2da, 0x77bc, 0xd2c3,
  0x77b4, 0xd2ab, 0x77ab, 0xd294, 0x77a2, 0xd27c, 0x7799, 0xd265,
  0x7790, 0xd24d, 0x7787, 0xd236, 0x777e, 0xd21e, 0x7775, 0xd207,
  0x776c, 0xd1ef, 0x7763, 0xd1d8, 0x775a, 0xd1c1, 0x7751, 0xd1a9,
  0x7747, 0xd192, 0x773e, 0xd17a, 0x7735, 0xd163, 0x772c, 0xd14b,
  0x7723, 0xd134, 0x771a, 0xd11d, 0x7710, 0xd105, 0x7707, 0xd0ee,
  0x76fe, 0xd0d7, 0x76f5, 0xd0bf, 0x76eb, 0xd0a8, 0x76e2, 0xd091,
  0x76d9, 0xd079, 0x76cf, 0xd062, 0x76c6, 0xd04b, 0x76bd, 0xd033,
  0x76b3, 0xd01c, 0x76aa, 0xd005, 0x76a0, 0xcfed, 0x7697, 0xcfd6,
  0x768e, 0xcfbf, 0x7684, 0xcfa7, 0x767b, 0xcf90, 0x7671, 0xcf79,
  0x7668, 0xcf62, 0x765e, 0xcf4a, 0x7654, 0xcf33, 0x764b, 0xcf1c,
  0x7641, 0xcf05, 0x7638, 0xceee, 0x762e, 0xced6, 0x7624, 0xcebf,
  0x761b, 0xcea8, 0x7611, 0xce91, 0x7607, 0xce7a, 0x75fd, 0xce62,
  0x75f4, 0xce4b, 0x75ea, 0xce34, 0x75e0, 0xce1d, 0x75d6, 0xce06,
  0x75cc, 0xcdef, 0x75c3, 0xcdd8, 0x75b9, 0xcdc0, 0x75af, 0xcda9,
  0x75a5, 0xcd92, 0x759b, 0xcd7b, 0x7591, 0xcd64, 0x7587, 0xcd4d,
  0x757d, 0xcd36, 0x7573, 0xcd1f, 0x7569, 0xcd08, 0x755f, 0xccf1,
  0x7555, 0xccda, 0x754b, 0xccc3, 0x7541, 0xccac, 0x7537, 0xcc95,
  0x752d, 0xcc7e, 0x7523, 0xcc67, 0x7519, 0xcc50, 0x750f, 0xcc39,
  0x7504, 0xcc22, 0x74fa, 0xcc0b, 0x74f0, 0xcbf4, 0x74e6, 0xcbdd,
  0x74db, 0xcbc6, 0x74d1, 0xcbaf, 0x74c7, 0xcb98, 0x74bd, 0xcb81,
  0x74b2, 0xcb6a, 0x74a8, 0xcb53, 0x749e, 0xcb3c, 0x7493, 0xcb25,
  0x7489, 0xcb0e, 0x747e, 0xcaf8, 0x7474, 0xcae1, 0x746a, 0xcaca,
  0x745f, 0xcab3, 0x7455, 0xca9c, 0x744a, 0xca85, 0x7440, 0xca6e,
  0x7435, 0xca58, 0x742b, 0xca41, 0x7420, 0xca2a, 0x7415, 0xca13,
  0x740b, 0xc9fc, 0x7400, 0xc9e6, 0x73f6, 0xc9cf, 0x73eb, 0xc9b8,
  0x73e0, 0xc9a1, 0x73d6, 0xc98b, 0x73cb, 0xc974, 0x73c0, 0xc95d,
  0x73b5, 0xc946, 0x73ab, 0xc930, 0x73a0, 0xc919, 0x7395, 0xc902,
  0x738a, 0xc8ec, 0x737f, 0xc8d5, 0x7375, 0xc8be, 0x736a, 0xc8a8,
  0x735f, 0xc891, 0x7354, 0xc87a, 0x7349, 0xc864, 0x733e, 0xc84d,
  0x7333, 0xc836, 0x7328, 0xc820, 0x731d, 0xc809, 0x7312, 0xc7f3,
  0x7307, 0xc7dc, 0x72fc, 0xc7c5, 0x72f1, 0xc7af, 0x72e6, 0xc798,
  0x72db, 0xc782, 0x72d0, 0xc76b, 0x72c5, 0xc755, 0x72ba, 0xc73e,
  0x72af, 0xc728, 0x72a3, 0xc711, 0x7298, 0xc6fa, 0x728d, 0xc6e4,
  0x7282, 0xc6ce, 0x7276, 0xc6b7, 0x726b, 0xc6a1, 0x7260, 0xc68a,
  0x7255, 0xc674, 0x7249, 0xc65d, 0x723e, 0xc647, 0x7233, 0xc630,
  0x7227, 0xc61a, 0x721c, 0xc603, 0x7211, 0xc5ed, 0x7205, 0xc5d7,
  0x71fa, 0xc5c0, 0x71ee, 0xc5aa, 0x71e3, 0xc594, 0x71d7, 0xc57d,
  0x71cc, 0xc567, 0x71c0, 0xc551, 0x71b5, 0xc53a, 0x71a9, 0xc524,
  0x719e, 0xc50e, 0x7192, 0xc4f7, 0x7186, 0xc4e1, 0x717b, 0xc4cb,
  0x716f, 0xc4b4, 0x7164, 0xc49e, 0x7158, 0xc488, 0x714c, 0xc472,
  0x7141, 0xc45b, 0x7135, 0xc445, 0x7129, 0xc42f, 0x711d, 0xc419,
  0x7112, 0xc403, 0x7106, 0xc3ec, 0x70fa, 0xc3d6, 0x70ee, 0xc3c0,
  0x70e2, 0xc3aa, 0x70d6, 0xc394, 0x70cb, 0xc37d, 0x70bf, 0xc367,
  0x70b3, 0xc351, 0x70a7, 0xc33b, 0x709b, 0xc325, 0x708f, 0xc30f,
  0x7083, 0xc2f9, 0x7077, 0xc2e3, 0x706b, 0xc2cd, 0x705f, 0xc2b7,
  0x7053, 0xc2a0, 0x7047, 0xc28a, 0x703b, 0xc274, 0x702f, 0xc25e,
  0x7023, 0xc248, 0x7016, 0xc232, 0x700a, 0xc21c, 0x6ffe, 0xc206,
  0x6ff2, 0xc1f0, 0x6fe6, 0xc1da, 0x6fda, 0xc1c4, 0x6fcd, 0xc1ae,
  0x6fc1, 0xc198, 0x6fb5, 0xc183, 0x6fa9, 0xc16d, 0x6f9c, 0xc157,
  0x6f90, 0xc141, 0x6f84, 0xc12b, 0x6f77, 0xc115, 0x6f6b, 0xc0ff,
  0x6f5f, 0xc0e9, 0x6f52, 0xc0d3, 0x6f46, 0xc0bd, 0x6f39, 0xc0a8,
  0x6f2d, 0xc092, 0x6f20, 0xc07c, 0x6f14, 0xc066, 0x6f07, 0xc050,
  0x6efb, 0xc03b, 0x6eee, 0xc025, 0x6ee2, 0xc00f, 0x6ed5, 0xbff9,
  0x6ec9, 0xbfe3, 0x6ebc, 0xbfce, 0x6eaf, 0xbfb8, 0x6ea3, 0xbfa2,
  0x6e96, 0xbf8d, 0x6e89, 0xbf77, 0x6e7d, 0xbf61, 0x6e70, 0xbf4b,
  0x6e63, 0xbf36, 0x6e57, 0xbf20, 0x6e4a, 0xbf0a, 0x6e3d, 0xbef5,
  0x6e30, 0xbedf, 0x6e24, 0xbeca, 0x6e17, 0xbeb4, 0x6e0a, 0xbe9e,
  0x6dfd, 0xbe89, 0x6df0, 0xbe73, 0x6de3, 0xbe5e, 0x6dd6, 0xbe48,
  0x6dca, 0xbe32, 0x6dbd, 0xbe1d, 0x6db0, 0xbe07, 0x6da3, 0xbdf2,
  0x6d96, 0xbddc, 0x6d89, 0xbdc7, 0x6d7c, 0xbdb1, 0x6d6f, 0xbd9c,
  0x6d62, 0xbd86, 0x6d55, 0xbd71, 0x6d48, 0xbd5b, 0x6d3a, 0xbd46,
  0x6d2d, 0xbd30, 0x6d20, 0xbd1b, 0x6d13, 0xbd06, 0x6d06, 0xbcf0,
  0x6cf9, 0xbcdb, 0x6cec, 0xbcc5, 0x6cde, 0xbcb0, 0x6cd1, 0xbc9b,
  0x6cc4, 0xbc85, 0x6cb7, 0xbc70, 0x6ca9, 0xbc5b, 0x6c9c, 0xbc45,
  0x6c8f, 0xbc30, 0x6c81, 0xbc1b, 0x6c74, 0xbc05, 0x6c67, 0xbbf0,
  0x6c59, 0xbbdb, 0x6c4c, 0xbbc5, 0x6c3f, 0xbbb0, 0x6c31, 0xbb9b,
  0x6c24, 0xbb86, 0x6c16, 0xbb70, 0x6c09, 0xbb5b, 0x6bfb, 0xbb46,
  0x6bee, 0xbb31, 0x6be0, 0xbb1c, 0x6bd3, 0xbb06, 0x6bc5, 0xbaf1,
  0x6bb8, 0xbadc, 0x6baa, 0xbac7, 0x6b9c, 0xbab2, 0x6b8f, 0xba9d,
  0x6b81, 0xba88, 0x6b73, 0xba73, 0x6b66, 0xba5d, 0x6b58, 0xba48,
  0x6b4a, 0xba33, 0x6b3d, 0xba1e, 0x6b2f, 0xba09, 0x6b21, 0xb9f4,
  0x6b13, 0xb9df, 0x6b06, 0xb9ca, 0x6af8, 0xb9b5, 0x6aea, 0xb9a0,
  0x6adc, 0xb98b, 0x6ace, 0xb976, 0x6ac1, 0xb961, 0x6ab3, 0xb94c,
  0x6aa5, 0xb937, 0x6a97, 0xb922, 0x6a89, 0xb90d, 0x6a7b, 0xb8f8,
  0x6a6d, 0xb8e4, 0x6a5f, 0xb8cf, 0x6a51, 0xb8ba, 0x6a43, 0xb8a5,
  0x6a35, 0xb890, 0x6a27, 0xb87b, 0x6a19, 0xb866, 0x6a0b, 0xb852,
  0x69fd, 0xb83d, 0x69ef, 0xb828, 0x69e1, 0xb813, 0x69d3, 0xb7fe,
  0x69c4, 0xb7ea, 0x69b6, 0xb7d5, 0x69a8, 0xb7c0, 0x699a, 0xb7ab,
  0x698c, 0xb797, 0x697d, 0xb782, 0x696f, 0xb76d, 0x6961, 0xb758,
  0x6953, 0xb744, 0x6944, 0xb72f, 0x6936, 0xb71a, 0x6928, 0xb706,
  0x6919, 0xb6f1, 0x690b, 0xb6dd, 0x68fd, 0xb6c8, 0x68ee, 0xb6b3,
  0x68e0, 0xb69f, 0x68d1, 0xb68a, 0x68c3, 0xb676, 0x68b5, 0xb661,
  0x68a6, 0xb64c, 0x6898, 0xb638, 0x6889, 0xb623, 0x687b, 0xb60f,
  0x686c, 0xb5fa, 0x685e, 0xb5e6, 0x684f, 0xb5d1, 0x6840, 0xb5bd,
  0x6832, 0xb5a8, 0x6823, 0xb594, 0x6815, 0xb57f, 0x6806, 0xb56b,
  0x67f7, 0xb557, 0x67e9, 0xb542, 0x67da, 0xb52e, 0x67cb, 0xb519,
  0x67bd, 0xb505, 0x67ae, 0xb4f1, 0x679f, 0xb4dc, 0x6790, 0xb4c8,
  0x6782, 0xb4b4, 0x6773, 0xb49f, 0x6764, 0xb48b, 0x6755, 0xb477,
  0x6746, 0xb462, 0x6737, 0xb44e, 0x6729, 0xb43a, 0x671a, 0xb426,
  0x670b, 0xb411, 0x66fc, 0xb3fd, 0x66ed, 0xb3e9, 0x66de, 0xb3d5,
  0x66cf, 0xb3c1, 0x66c0, 0xb3ac, 0x66b1, 0xb398, 0x66a2, 0xb384,
  0x6693, 0xb370, 0x6684, 0xb35c, 0x6675, 0xb348, 0x6666, 0xb334,
  0x6657, 0xb31f, 0x6648, 0xb30b, 0x6639, 0xb2f7, 0x6629, 0xb2e3,
  0x661a, 0xb2cf, 0x660b, 0xb2bb, 0x65fc, 0xb2a7, 0x65ed, 0xb293,
  0x65dd, 0xb27f, 0x65ce, 0xb26b, 0x65bf, 0xb257, 0x65b0, 0xb243,
  0x65a0, 0xb22f, 0x6591, 0xb21b, 0x6582, 0xb207, 0x6573, 0xb1f3,
  0x6563, 0xb1df, 0x6554, 0xb1cc, 0x6545, 0xb1b8, 0x6535, 0xb1a4,
  0x6526, 0xb190, 0x6516, 0xb17c, 0x6507, 0xb168, 0x64f7, 0xb154,
  0x64e8, 0xb141, 0x64d9, 0xb12d, 0x64c9, 0xb119, 0x64ba, 0xb105,
  0x64aa, 0xb0f1, 0x649b, 0xb0de, 0x648b, 0xb0ca, 0x647b, 0xb0b6,
  0x646c, 0xb0a2, 0x645c, 0xb08f, 0x644d, 0xb07b, 0x643d, 0xb067,
  0x642d, 0xb054, 0x641e, 0xb040, 0x640e, 0xb02c, 0x63fe, 0xb019,
  0x63ef, 0xb005, 0x63df, 0xaff1, 0x63cf, 0xafde, 0x63c0, 0xafca,
  0x63b0, 0xafb7, 0x63a0, 0xafa3, 0x6390, 0xaf90, 0x6380, 0xaf7c,
  0x6371, 0xaf69, 0x6361, 0xaf55, 0x6351, 0xaf41, 0x6341, 0xaf2e,
  0x6331, 0xaf1b, 0x6321, 0xaf07, 0x6311, 0xaef4, 0x6301, 0xaee0,
  0x62f2, 0xaecd, 0x62e2, 0xaeb9, 0x62d2, 0xaea6, 0x62c2, 0xae92,
  0x62b2, 0xae7f, 0x62a2, 0xae6c, 0x6292, 0xae58, 0x6282, 0xae45,
  0x6271, 0xae32, 0x6261, 0xae1e, 0x6251, 0xae0b, 0x6241, 0xadf8,
  0x6231, 0xade4, 0x6221, 0xadd1, 0x6211, 0xadbe, 0x6201, 0xadab,
  0x61f1, 0xad97, 0x61e0, 0xad84, 0x61d0, 0xad71, 0x61c0, 0xad5e,
  0x61b0, 0xad4b, 0x619f, 0xad37, 0x618f, 0xad24, 0x617f, 0xad11,
  0x616f, 0xacfe, 0x615e, 0xaceb, 0x614e, 0xacd8, 0x613e, 0xacc5,
  0x612d, 0xacb2, 0x611d, 0xac9e, 0x610d, 0xac8b, 0x60fc, 0xac78,
  0x60ec, 0xac65, 0x60db, 0xac52, 0x60cb, 0xac3f, 0x60ba, 0xac2c,
  0x60aa, 0xac19, 0x6099, 0xac06, 0x6089, 0xabf3, 0x6078, 0xabe0,
  0x6068, 0xabcd, 0x6057, 0xabbb, 0x6047, 0xaba8, 0x6036, 0xab95,
  0x6026, 0xab82, 0x6015, 0xab6f, 0x6004, 0xab5c, 0x5ff4, 0xab49,
  0x5fe3, 0xab36, 0x5fd3, 0xab24, 0x5fc2, 0xab11, 0x5fb1, 0xaafe,
  0x5fa0, 0xaaeb, 0x5f90, 0xaad8, 0x5f7f, 0xaac6, 0x5f6e, 0xaab3,
  0x5f5e, 0xaaa0, 0x5f4d, 0xaa8e, 0x5f3c, 0xaa7b, 0x5f2b, 0xaa68,
  0x5f1a, 0xaa55, 0x5f0a, 0xaa43, 0x5ef9, 0xaa30, 0x5ee8, 0xaa1d,
  0x5ed7, 0xaa0b, 0x5ec6, 0xa9f8, 0x5eb5, 0xa9e6, 0x5ea4, 0xa9d3,
  0x5e93, 0xa9c0, 0x5e82, 0xa9ae, 0x5e71, 0xa99b, 0x5e60, 0xa989,
  0x5e50, 0xa976, 0x5e3f, 0xa964, 0x5e2d, 0xa951, 0x5e1c, 0xa93f,
  0x5e0b, 0xa92c, 0x5dfa, 0xa91a, 0x5de9, 0xa907, 0x5dd8, 0xa8f5,
  0x5dc7, 0xa8e3, 0x5db6, 0xa8d0, 0x5da5, 0xa8be, 0x5d94, 0xa8ab,
  0x5d83, 0xa899, 0x5d71, 0xa887, 0x5d60, 0xa874, 0x5d4f, 0xa862,
  0x5d3e, 0xa850, 0x5d2d, 0xa83d, 0x5d1b, 0xa82b, 0x5d0a, 0xa819,
  0x5cf9, 0xa807, 0x5ce8, 0xa7f4, 0x5cd6, 0xa7e2, 0x5cc5, 0xa7d0,
  0x5cb4, 0xa7be, 0x5ca2, 0xa7ab, 0x5c91, 0xa799, 0x5c80, 0xa787,
  0x5c6e, 0xa775, 0x5c5d, 0xa763, 0x5c4b, 0xa751, 0x5c3a, 0xa73f,
  0x5c29, 0xa72c, 0x5c17, 0xa71a, 0x5c06, 0xa708, 0x5bf4, 0xa6f6,
  0x5be3, 0xa6e4, 0x5bd1, 0xa6d2, 0x5bc0, 0xa6c0, 0x5bae, 0xa6ae,
  0x5b9d, 0xa69c, 0x5b8b, 0xa68a, 0x5b79, 0xa678, 0x5b68, 0xa666,
  0x5b56, 0xa654, 0x5b45, 0xa642, 0x5b33, 0xa630, 0x5b21, 0xa61f,
  0x5b10, 0xa60d, 0x5afe, 0xa5fb, 0x5aec, 0xa5e9, 0x5adb, 0xa5d7,
  0x5ac9, 0xa5c5, 0x5ab7, 0xa5b3, 0x5aa5, 0xa5a2, 0x5a94, 0xa590,
  0x5a82, 0xa57e, 0x5a70, 0xa56c, 0x5a5e, 0xa55b, 0x5a4d, 0xa549,
  0x5a3b, 0xa537, 0x5a29, 0xa525, 0x5a17, 0xa514, 0x5a05, 0xa502,
  0x59f3, 0xa4f0, 0x59e1, 0xa4df, 0x59d0, 0xa4cd, 0x59be, 0xa4bb,
  0x59ac, 0xa4aa, 0x599a, 0xa498, 0x5988, 0xa487, 0x5976, 0xa475,
  0x5964, 0xa463, 0x5952, 0xa452, 0x5940, 0xa440, 0x592e, 0xa42f,
  0x591c, 0xa41d, 0x590a, 0xa40c, 0x58f8, 0xa3fa, 0x58e6, 0xa3e9,
  0x58d4, 0xa3d7, 0x58c1, 0xa3c6, 0x58af, 0xa3b5, 0x589d, 0xa3a3,
  0x588b, 0xa392, 0x5879, 0xa380, 0x5867, 0xa36f, 0x5855, 0xa35e,
  0x5842, 0xa34c, 0x5830, 0xa33b, 0x581e, 0xa32a, 0x580c, 0xa318,
  0x57f9, 0xa307, 0x57e7, 0xa2f6, 0x57d5, 0xa2e5, 0x57c3, 0xa2d3,
  0x57b0, 0xa2c2, 0x579e, 0xa2b1, 0x578c, 0xa2a0, 0x5779, 0xa28f,
  0x5767, 0xa27d, 0x5755, 0xa26c, 0x5742, 0xa25b, 0x5730, 0xa24a,
  0x571d, 0xa239, 0x570b, 0xa228, 0x56f9, 0xa217, 0x56e6, 0xa206,
  0x56d4, 0xa1f5, 0x56c1, 0xa1e4, 0x56af, 0xa1d3, 0x569c, 0xa1c1,
  0x568a, 0xa1b0, 0x5677, 0xa1a0, 0x5665, 0xa18f, 0x5652, 0xa17e,
  0x5640, 0xa16d, 0x562d, 0xa15c, 0x561a, 0xa14b, 0x5608, 0xa13a,
  0x55f5, 0xa129, 0x55e3, 0xa118, 0x55d0, 0xa107, 0x55bd, 0xa0f6,
  0x55ab, 0xa0e6, 0x5598, 0xa0d5, 0x5585, 0xa0c4, 0x5572, 0xa0b3,
  0x5560, 0xa0a2, 0x554d, 0xa092, 0x553a, 0xa081, 0x5528, 0xa070,
  0x5515, 0xa060, 0x5502, 0xa04f, 0x54ef, 0xa03e, 0x54dc, 0xa02d,
  0x54ca, 0xa01d, 0x54b7, 0xa00c, 0x54a4, 0x9ffc, 0x5491, 0x9feb,
  0x547e, 0x9fda, 0x546b, 0x9fca, 0x5458, 0x9fb9, 0x5445, 0x9fa9,
  0x5433, 0x9f98, 0x5420, 0x9f88, 0x540d, 0x9f77, 0x53fa, 0x9f67,
  0x53e7, 0x9f56, 0x53d4, 0x9f46, 0x53c1, 0x9f35, 0x53ae, 0x9f25,
  0x539b, 0x9f14, 0x5388, 0x9f04, 0x5375, 0x9ef3, 0x5362, 0x9ee3,
  0x534e, 0x9ed3, 0x533b, 0x9ec2, 0x5328, 0x9eb2, 0x5315, 0x9ea2,
  0x5302, 0x9e91, 0x52ef, 0x9e81, 0x52dc, 0x9e71, 0x52c9, 0x9e61,
  0x52b5, 0x9e50, 0x52a2, 0x9e40, 0x528f, 0x9e30, 0x527c, 0x9e20,
  0x5269, 0x9e0f, 0x5255, 0x9dff, 0x5242, 0x9def, 0x522f, 0x9ddf,
  0x521c, 0x9dcf, 0x5208, 0x9dbf, 0x51f5, 0x9daf, 0x51e2, 0x9d9f,
  0x51ce, 0x9d8f, 0x51bb, 0x9d7e, 0x51a8, 0x9d6e, 0x5194, 0x9d5e,
  0x5181, 0x9d4e, 0x516e, 0x9d3e, 0x515a, 0x9d2e, 0x5147, 0x9d1e,
  0x5133, 0x9d0e, 0x5120, 0x9cff, 0x510c, 0x9cef, 0x50f9, 0x9cdf,
  0x50e5, 0x9ccf, 0x50d2, 0x9cbf, 0x50bf, 0x9caf, 0x50ab, 0x9c9f,
  0x5097, 0x9c8f, 0x5084, 0x9c80, 0x5070, 0x9c70, 0x505d, 0x9c60,
  0x5049, 0x9c50, 0x5036, 0x9c40, 0x5022, 0x9c31, 0x500f, 0x9c21,
  0x4ffb, 0x9c11, 0x4fe7, 0x9c02, 0x4fd4, 0x9bf2, 0x4fc0, 0x9be2,
  0x4fac, 0x9bd3, 0x4f99, 0x9bc3, 0x4f85, 0x9bb3, 0x4f71, 0x9ba4,
  0x4f5e, 0x9b94, 0x4f4a, 0x9b85, 0x4f36, 0x9b75, 0x4f22, 0x9b65,
  0x4f0f, 0x9b56, 0x4efb, 0x9b46, 0x4ee7, 0x9b37, 0x4ed3, 0x9b27,
  0x4ebf, 0x9b18, 0x4eac, 0x9b09, 0x4e98, 0x9af9, 0x4e84, 0x9aea,
  0x4e70, 0x9ada, 0x4e5c, 0x9acb, 0x4e48, 0x9abb, 0x4e34, 0x9aac,
  0x4e21, 0x9a9d, 0x4e0d, 0x9a8d, 0x4df9, 0x9a7e, 0x4de5, 0x9a6f,
  0x4dd1, 0x9a60, 0x4dbd, 0x9a50, 0x4da9, 0x9a41, 0x4d95, 0x9a32,
  0x4d81, 0x9a23, 0x4d6d, 0x9a13, 0x4d59, 0x9a04, 0x4d45, 0x99f5,
  0x4d31, 0x99e6, 0x4d1d, 0x99d7, 0x4d09, 0x99c7, 0x4cf5, 0x99b8,
  0x4ce1, 0x99a9, 0x4ccc, 0x999a, 0x4cb8, 0x998b, 0x4ca4, 0x997c,
  0x4c90, 0x996d, 0x4c7c, 0x995e, 0x4c68, 0x994f, 0x4c54, 0x9940,
  0x4c3f, 0x9931, 0x4c2b, 0x9922, 0x4c17, 0x9913, 0x4c03, 0x9904,
  0x4bef, 0x98f5, 0x4bda, 0x98e6, 0x4bc6, 0x98d7, 0x4bb2, 0x98c9,
  0x4b9e, 0x98ba, 0x4b89, 0x98ab, 0x4b75, 0x989c, 0x4b61, 0x988d,
  0x4b4c, 0x987e, 0x4b38, 0x9870, 0x4b24, 0x9861, 0x4b0f, 0x9852,
  0x4afb, 0x9843, 0x4ae7, 0x9835, 0x4ad2, 0x9826, 0x4abe, 0x9817,
  0x4aa9, 0x9809, 0x4a95, 0x97fa, 0x4a81, 0x97eb, 0x4a6c, 0x97dd,
  0x4a58, 0x97ce, 0x4a43, 0x97c0, 0x4a2f, 0x97b1, 0x4a1a, 0x97a2,
  0x4a06, 0x9794, 0x49f1, 0x9785, 0x49dd, 0x9777, 0x49c8, 0x9768,
  0x49b4, 0x975a, 0x499f, 0x974b, 0x498a, 0x973d, 0x4976, 0x972f,
  0x4961, 0x9720, 0x494d, 0x9712, 0x4938, 0x9703, 0x4923, 0x96f5,
  0x490f, 0x96e7, 0x48fa, 0x96d8, 0x48e6, 0x96ca, 0x48d1, 0x96bc,
  0x48bc, 0x96ad, 0x48a8, 0x969f, 0x4893, 0x9691, 0x487e, 0x9683,
  0x4869, 0x9674, 0x4855, 0x9666, 0x4840, 0x9658, 0x482b, 0x964a,
  0x4816, 0x963c, 0x4802, 0x962d, 0x47ed, 0x961f, 0x47d8, 0x9611,
  0x47c3, 0x9603, 0x47ae, 0x95f5, 0x479a, 0x95e7, 0x4785, 0x95d9,
  0x4770, 0x95cb, 0x475b, 0x95bd, 0x4746, 0x95af, 0x4731, 0x95a1,
  0x471c, 0x9593, 0x4708, 0x9585, 0x46f3, 0x9577, 0x46de, 0x9569,
  0x46c9, 0x955b, 0x46b4, 0x954d, 0x469f, 0x953f, 0x468a, 0x9532,
  0x4675, 0x9524, 0x4660, 0x9516, 0x464b, 0x9508, 0x4636, 0x94fa,
  0x4621, 0x94ed, 0x460c, 0x94df, 0x45f7, 0x94d1, 0x45e2, 0x94c3,
  0x45cd, 0x94b6, 0x45b8, 0x94a8, 0x45a3, 0x949a, 0x458d, 0x948d,
  0x4578, 0x947f, 0x4563, 0x9471, 0x454e, 0x9464, 0x4539, 0x9456,
  0x4524, 0x9448, 0x450f, 0x943b, 0x44fa, 0x942d, 0x44e4, 0x9420,
  0x44cf, 0x9412, 0x44ba, 0x9405, 0x44a5, 0x93f7, 0x4490, 0x93ea,
  0x447a, 0x93dc, 0x4465, 0x93cf, 0x4450, 0x93c1, 0x443b, 0x93b4,
  0x4425, 0x93a7, 0x4410, 0x9399, 0x43fb, 0x938c, 0x43e5, 0x937f,
  0x43d0, 0x9371, 0x43bb, 0x9364, 0x43a5, 0x9357, 0x4390, 0x9349,
  0x437b, 0x933c, 0x4365, 0x932f, 0x4350, 0x9322, 0x433b, 0x9314,
  0x4325, 0x9307, 0x4310, 0x92fa, 0x42fa, 0x92ed, 0x42e5, 0x92e0,
  0x42d0, 0x92d3, 0x42ba, 0x92c6, 0x42a5, 0x92b8, 0x428f, 0x92ab,
  0x427a, 0x929e, 0x4264, 0x9291, 0x424f, 0x9284, 0x4239, 0x9277,
  0x4224, 0x926a, 0x420e, 0x925d, 0x41f9, 0x9250, 0x41e3, 0x9243,
  0x41ce, 0x9236, 0x41b8, 0x922a, 0x41a2, 0x921d, 0x418d, 0x9210,
  0x4177, 0x9203, 0x4162, 0x91f6, 0x414c, 0x91e9, 0x4136, 0x91dc,
  0x4121, 0x91d0, 0x410b, 0x91c3, 0x40f6, 0x91b6, 0x40e0, 0x91a9,
  0x40ca, 0x919d, 0x40b5, 0x9190, 0x409f, 0x9183, 0x4089, 0x9177,
  0x4073, 0x916a, 0x405e, 0x915d, 0x4048, 0x9151, 0x4032, 0x9144,
  0x401d, 0x9137, 0x4007, 0x912b, 0x3ff1, 0x911e, 0x3fdb, 0x9112,
  0x3fc5, 0x9105, 0x3fb0, 0x90f9, 0x3f9a, 0x90ec, 0x3f84, 0x90e0,
  0x3f6e, 0x90d3, 0x3f58, 0x90c7, 0x3f43, 0x90ba, 0x3f2d, 0x90ae,
  0x3f17, 0x90a1, 0x3f01, 0x9095, 0x3eeb, 0x9089, 0x3ed5, 0x907c,
  0x3ebf, 0x9070, 0x3ea9, 0x9064, 0x3e93, 0x9057, 0x3e7d, 0x904b,
  0x3e68, 0x903f, 0x3e52, 0x9033, 0x3e3c, 0x9026, 0x3e26, 0x901a,
  0x3e10, 0x900e, 0x3dfa, 0x9002, 0x3de4, 0x8ff6, 0x3dce, 0x8fea,
  0x3db8, 0x8fdd, 0x3da2, 0x8fd1, 0x3d8c, 0x8fc5, 0x3d76, 0x8fb9,
  0x3d60, 0x8fad, 0x3d49, 0x8fa1, 0x3d33, 0x8f95, 0x3d1d, 0x8f89,
  0x3d07, 0x8f7d, 0x3cf1, 0x8f71, 0x3cdb, 0x8f65, 0x3cc5, 0x8f59,
  0x3caf, 0x8f4d, 0x3c99, 0x8f41, 0x3c83, 0x8f35, 0x3c6c, 0x8f2a,
  0x3c56, 0x8f1e, 0x3c40, 0x8f12, 0x3c2a, 0x8f06, 0x3c14, 0x8efa,
  0x3bfd, 0x8eee, 0x3be7, 0x8ee3, 0x3bd1, 0x8ed7, 0x3bbb, 0x8ecb,
  0x3ba5, 0x8ebf, 0x3b8e, 0x8eb4, 0x3b78, 0x8ea8, 0x3b62, 0x8e9c,
  0x3b4c, 0x8e91, 0x3b35, 0x8e85, 0x3b1f, 0x8e7a, 0x3b09, 0x8e6e,
  0x3af2, 0x8e62, 0x3adc, 0x8e57, 0x3ac6, 0x8e4b, 0x3aaf, 0x8e40,
  0x3a99, 0x8e34, 0x3a83, 0x8e29, 0x3a6c, 0x8e1d, 0x3a56, 0x8e12,
  0x3a40, 0x8e06, 0x3a29, 0x8dfb, 0x3a13, 0x8def, 0x39fd, 0x8de4,
  0x39e6, 0x8dd9, 0x39d0, 0x8dcd, 0x39b9, 0x8dc2, 0x39a3, 0x8db7,
  0x398c, 0x8dab, 0x3976, 0x8da0, 0x395f, 0x8d95, 0x3949, 0x8d8a,
  0x3932, 0x8d7e, 0x391c, 0x8d73, 0x3906, 0x8d68, 0x38ef, 0x8d5d,
  0x38d8, 0x8d51, 0x38c2, 0x8d46, 0x38ab, 0x8d3b, 0x3895, 0x8d30,
  0x387e, 0x8d25, 0x3868, 0x8d1a, 0x3851, 0x8d0f, 0x383b, 0x8d04,
  0x3824, 0x8cf9, 0x380d, 0x8cee, 0x37f7, 0x8ce3, 0x37e0, 0x8cd8,
  0x37ca, 0x8ccd, 0x37b3, 0x8cc2, 0x379c, 0x8cb7, 0x3786, 0x8cac,
  0x376f, 0x8ca1, 0x3758, 0x8c96, 0x3742, 0x8c8b, 0x372b, 0x8c81,
  0x3714, 0x8c76, 0x36fe, 0x8c6b, 0x36e7, 0x8c60, 0x36d0, 0x8c55,
  0x36ba, 0x8c4b, 0x36a3, 0x8c40, 0x368c, 0x8c35, 0x3675, 0x8c2a,
  0x365f, 0x8c20, 0x3648, 0x8c15, 0x3631, 0x8c0a, 0x361a, 0x8c00,
  0x3604, 0x8bf5, 0x35ed, 0x8beb, 0x35d6, 0x8be0, 0x35bf, 0x8bd5,
  0x35a8, 0x8bcb, 0x3592, 0x8bc0, 0x357b, 0x8bb6, 0x3564, 0x8bab,
  0x354d, 0x8ba1, 0x3536, 0x8b96, 0x351f, 0x8b8c, 0x3508, 0x8b82,
  0x34f2, 0x8b77, 0x34db, 0x8b6d, 0x34c4, 0x8b62, 0x34ad, 0x8b58,
  0x3496, 0x8b4e, 0x347f, 0x8b43, 0x3468, 0x8b39, 0x3451, 0x8b2f,
  0x343a, 0x8b25, 0x3423, 0x8b1a, 0x340c, 0x8b10, 0x33f5, 0x8b06,
  0x33de, 0x8afc, 0x33c7, 0x8af1, 0x33b0, 0x8ae7, 0x3399, 0x8add,
  0x3382, 0x8ad3, 0x336b, 0x8ac9, 0x3354, 0x8abf, 0x333d, 0x8ab5,
  0x3326, 0x8aab, 0x330f, 0x8aa1, 0x32f8, 0x8a97, 0x32e1, 0x8a8d,
  0x32ca, 0x8a83, 0x32b3, 0x8a79, 0x329c, 0x8a6f, 0x3285, 0x8a65,
  0x326e, 0x8a5b, 0x3257, 0x8a51, 0x3240, 0x8a47, 0x3228, 0x8a3d,
  0x3211, 0x8a34, 0x31fa, 0x8a2a, 0x31e3, 0x8a20, 0x31cc, 0x8a16,
  0x31b5, 0x8a0c, 0x319e, 0x8a03, 0x3186, 0x89f9, 0x316f, 0x89ef,
  0x3158, 0x89e5, 0x3141, 0x89dc, 0x312a, 0x89d2, 0x3112, 0x89c8,
  0x30fb, 0x89bf, 0x30e4, 0x89b5, 0x30cd, 0x89ac, 0x30b6, 0x89a2,
  0x309e, 0x8998, 0x3087, 0x898f, 0x3070, 0x8985, 0x3059, 0x897c,
  0x3041, 0x8972, 0x302a, 0x8969, 0x3013, 0x8960, 0x2ffb, 0x8956,
  0x2fe4, 0x894d, 0x2fcd, 0x8943, 0x2fb5, 0x893a, 0x2f9e, 0x8931,
  0x2f87, 0x8927, 0x2f6f, 0x891e, 0x2f58, 0x8915, 0x2f41, 0x890b,
  0x2f29, 0x8902, 0x2f12, 0x88f9, 0x2efb, 0x88f0, 0x2ee3, 0x88e6,
  0x2ecc, 0x88dd, 0x2eb5, 0x88d4, 0x2e9d, 0x88cb, 0x2e86, 0x88c2,
  0x2e6e, 0x88b9, 0x2e57, 0x88af, 0x2e3f, 0x88a6, 0x2e28, 0x889d,
  0x2e11, 0x8894, 0x2df9, 0x888b, 0x2de2, 0x8882, 0x2dca, 0x8879,
  0x2db3, 0x8870, 0x2d9b, 0x8867, 0x2d84, 0x885e, 0x2d6c, 0x8855,
  0x2d55, 0x884c, 0x2d3d, 0x8844, 0x2d26, 0x883b, 0x2d0e, 0x8832,
  0x2cf7, 0x8829, 0x2cdf, 0x8820, 0x2cc8, 0x8817, 0x2cb0, 0x880f,
  0x2c98, 0x8806, 0x2c81, 0x87fd, 0x2c69, 0x87f4, 0x2c52, 0x87ec,
  0x2c3a, 0x87e3, 0x2c23, 0x87da, 0x2c0b, 0x87d2, 0x2bf3, 0x87c9,
  0x2bdc, 0x87c0, 0x2bc4, 0x87b8, 0x2bad, 0x87af, 0x2b95, 0x87a7,
  0x2b7d, 0x879e, 0x2b66, 0x8795, 0x2b4e, 0x878d, 0x2b36, 0x8784,
  0x2b1f, 0x877c, 0x2b07, 0x8774, 0x2aef, 0x876b, 0x2ad8, 0x8763,
  0x2ac0, 0x875a, 0x2aa8, 0x8752, 0x2a91, 0x874a, 0x2a79, 0x8741,
  0x2a61, 0x8739, 0x2a49, 0x8731, 0x2a32, 0x8728, 0x2a1a, 0x8720,
  0x2a02, 0x8718, 0x29eb, 0x870f, 0x29d3, 0x8707, 0x29bb, 0x86ff,
  0x29a3, 0x86f7, 0x298b, 0x86ef, 0x2974, 0x86e7, 0x295c, 0x86de,
  0x2944, 0x86d6, 0x292c, 0x86ce, 0x2915, 0x86c6, 0x28fd, 0x86be,
  0x28e5, 0x86b6, 0x28cd, 0x86ae, 0x28b5, 0x86a6, 0x289d, 0x869e,
  0x2886, 0x8696, 0x286e, 0x868e, 0x2856, 0x8686, 0x283e, 0x867e,
  0x2826, 0x8676, 0x280e, 0x866e, 0x27f6, 0x8667, 0x27df, 0x865f,
  0x27c7, 0x8657, 0x27af, 0x864f, 0x2797, 0x8647, 0x277f, 0x8640,
  0x2767, 0x8638, 0x274f, 0x8630, 0x2737, 0x8628, 0x271f, 0x8621,
  0x2707, 0x8619, 0x26ef, 0x8611, 0x26d8, 0x860a, 0x26c0, 0x8602,
  0x26a8, 0x85fb, 0x2690, 0x85f3, 0x2678, 0x85eb, 0x2660, 0x85e4,
  0x2648, 0x85dc, 0x2630, 0x85d5, 0x2618, 0x85cd, 0x2600, 0x85c6,
  0x25e8, 0x85be, 0x25d0, 0x85b7, 0x25b8, 0x85b0, 0x25a0, 0x85a8,
  0x2588, 0x85a1, 0x2570, 0x8599, 0x2558, 0x8592, 0x2540, 0x858b,
  0x2528, 0x8583, 0x250f, 0x857c, 0x24f7, 0x8575, 0x24df, 0x856e,
  0x24c7, 0x8566, 0x24af, 0x855f, 0x2497, 0x8558, 0x247f, 0x8551,
  0x2467, 0x854a, 0x244f, 0x8543, 0x2437, 0x853b, 0x241f, 0x8534,
  0x2407, 0x852d, 0x23ee, 0x8526, 0x23d6, 0x851f, 0x23be, 0x8518,
  0x23a6, 0x8511, 0x238e, 0x850a, 0x2376, 0x8503, 0x235e, 0x84fc,
  0x2345, 0x84f5, 0x232d, 0x84ee, 0x2315, 0x84e7, 0x22fd, 0x84e1,
  0x22e5, 0x84da, 0x22cd, 0x84d3, 0x22b4, 0x84cc, 0x229c, 0x84c5,
  0x2284, 0x84be, 0x226c, 0x84b8, 0x2254, 0x84b1, 0x223b, 0x84aa,
  0x2223, 0x84a3, 0x220b, 0x849d, 0x21f3, 0x8496, 0x21da, 0x848f,
  0x21c2, 0x8489, 0x21aa, 0x8482, 0x2192, 0x847c, 0x2179, 0x8475,
  0x2161, 0x846e, 0x2149, 0x8468, 0x2131, 0x8461, 0x2118, 0x845b,
  0x2100, 0x8454, 0x20e8, 0x844e, 0x20d0, 0x8447, 0x20b7, 0x8441,
  0x209f, 0x843b, 0x2087, 0x8434, 0x206e, 0x842e, 0x2056, 0x8427,
  0x203e, 0x8421, 0x2025, 0x841b, 0x200d, 0x8415, 0x1ff5, 0x840e,
  0x1fdc, 0x8408, 0x1fc4, 0x8402, 0x1fac, 0x83fb, 0x1f93, 0x83f5,
  0x1f7b, 0x83ef, 0x1f63, 0x83e9, 0x1f4a, 0x83e3, 0x1f32, 0x83dd,
  0x1f19, 0x83d7, 0x1f01, 0x83d0, 0x1ee9, 0x83ca, 0x1ed0, 0x83c4,
  0x1eb8, 0x83be, 0x1ea0, 0x83b8, 0x1e87, 0x83b2, 0x1e6f, 0x83ac,
  0x1e56, 0x83a6, 0x1e3e, 0x83a0, 0x1e25, 0x839a, 0x1e0d, 0x8394,
  0x1df5, 0x838f, 0x1ddc, 0x8389, 0x1dc4, 0x8383, 0x1dab, 0x837d,
  0x1d93, 0x8377, 0x1d7a, 0x8371, 0x1d62, 0x836c, 0x1d49, 0x8366,
  0x1d31, 0x8360, 0x1d18, 0x835a, 0x1d00, 0x8355, 0x1ce8, 0x834f,
  0x1ccf, 0x8349, 0x1cb7, 0x8344, 0x1c9e, 0x833e, 0x1c86, 0x8338,
  0x1c6d, 0x8333, 0x1c55, 0x832d, 0x1c3c, 0x8328, 0x1c24, 0x8322,
  0x1c0b, 0x831d, 0x1bf2, 0x8317, 0x1bda, 0x8312, 0x1bc1, 0x830c,
  0x1ba9, 0x8307, 0x1b90, 0x8301, 0x1b78, 0x82fc, 0x1b5f, 0x82f7,
  0x1b47, 0x82f1, 0x1b2e, 0x82ec, 0x1b16, 0x82e7, 0x1afd, 0x82e1,
  0x1ae4, 0x82dc, 0x1acc, 0x82d7, 0x1ab3, 0x82d1, 0x1a9b, 0x82cc,
  0x1a82, 0x82c7, 0x1a6a, 0x82c2, 0x1a51, 0x82bd, 0x1a38, 0x82b7,
  0x1a20, 0x82b2, 0x1a07, 0x82ad, 0x19ef, 0x82a8, 0x19d6, 0x82a3,
  0x19bd, 0x829e, 0x19a5, 0x8299, 0x198c, 0x8294, 0x1973, 0x828f,
  0x195b, 0x828a, 0x1942, 0x8285, 0x192a, 0x8280, 0x1911, 0x827b,
  0x18f8, 0x8276, 0x18e0, 0x8271, 0x18c7, 0x826c, 0x18ae, 0x8268,
  0x1896, 0x8263, 0x187d, 0x825e, 0x1864, 0x8259, 0x184c, 0x8254,
  0x1833, 0x8250, 0x181a, 0x824b, 0x1802, 0x8246, 0x17e9, 0x8241,
  0x17d0, 0x823d, 0x17b7, 0x8238, 0x179f, 0x8233, 0x1786, 0x822f,
  0x176d, 0x822a, 0x1755, 0x8226, 0x173c, 0x8221, 0x1723, 0x821c,
  0x170a, 0x8218, 0x16f2, 0x8213, 0x16d9, 0x820f, 0x16c0, 0x820a,
  0x16a8, 0x8206, 0x168f, 0x8201, 0x1676, 0x81fd, 0x165d, 0x81f9,
  0x1645, 0x81f4, 0x162c, 0x81f0, 0x1613, 0x81ec, 0x15fa, 0x81e7,
  0x15e2, 0x81e3, 0x15c9, 0x81df, 0x15b0, 0x81da, 0x1597, 0x81d6,
  0x157f, 0x81d2, 0x1566, 0x81ce, 0x154d, 0x81c9, 0x1534, 0x81c5,
  0x151b, 0x81c1, 0x1503, 0x81bd, 0x14ea, 0x81b9, 0x14d1, 0x81b5,
  0x14b8, 0x81b1, 0x149f, 0x81ad, 0x1487, 0x81a9, 0x146e, 0x81a5,
  0x1455, 0x81a1, 0x143c, 0x819d, 0x1423, 0x8199, 0x140b, 0x8195,
  0x13f2, 0x8191, 0x13d9, 0x818d, 0x13c0, 0x8189, 0x13a7, 0x8185,
  0x138e, 0x8181, 0x1376, 0x817d, 0x135d, 0x817a, 0x1344, 0x8176,
  0x132b, 0x8172, 0x1312, 0x816e, 0x12f9, 0x816b, 0x12e0, 0x8167,
  0x12c8, 0x8163, 0x12af, 0x815f, 0x1296, 0x815c, 0x127d, 0x8158,
  0x1264, 0x8155, 0x124b, 0x8151, 0x1232, 0x814d, 0x1219, 0x814a,
  0x1201, 0x8146, 0x11e8, 0x8143, 0x11cf, 0x813f, 0x11b6, 0x813c,
  0x119d, 0x8138, 0x1184, 0x8135, 0x116b, 0x8131, 0x1152, 0x812e,
  0x1139, 0x812b, 0x1121, 0x8127, 0x1108, 0x8124, 0x10ef, 0x8121,
  0x10d6, 0x811d, 0x10bd, 0x811a, 0x10a4, 0x8117, 0x108b, 0x8113,
  0x1072, 0x8110, 0x1059, 0x810d, 0x1040, 0x810a, 0x1027, 0x8107,
  0x100e, 0x8103, 0xff5, 0x8100, 0xfdd, 0x80fd, 0xfc4, 0x80fa,
  0xfab, 0x80f7, 0xf92, 0x80f4, 0xf79, 0x80f1, 0xf60, 0x80ee,
  0xf47, 0x80eb, 0xf2e, 0x80e8, 0xf15, 0x80e5, 0xefc, 0x80e2,
  0xee3, 0x80df, 0xeca, 0x80dc, 0xeb1, 0x80d9, 0xe98, 0x80d6,
  0xe7f, 0x80d3, 0xe66, 0x80d1, 0xe4d, 0x80ce, 0xe34, 0x80cb,
  0xe1b, 0x80c8, 0xe02, 0x80c5, 0xde9, 0x80c3, 0xdd0, 0x80c0,
  0xdb7, 0x80bd, 0xd9e, 0x80bb, 0xd85, 0x80b8, 0xd6c, 0x80b5,
  0xd53, 0x80b3, 0xd3a, 0x80b0, 0xd21, 0x80ad, 0xd08, 0x80ab,
  0xcef, 0x80a8, 0xcd6, 0x80a6, 0xcbd, 0x80a3, 0xca4, 0x80a1,
  0xc8b, 0x809e, 0xc72, 0x809c, 0xc59, 0x8099, 0xc40, 0x8097,
  0xc27, 0x8095, 0xc0e, 0x8092, 0xbf5, 0x8090, 0xbdc, 0x808e,
  0xbc3, 0x808b, 0xbaa, 0x8089, 0xb91, 0x8087, 0xb78, 0x8084,
  0xb5f, 0x8082, 0xb46, 0x8080, 0xb2d, 0x807e, 0xb14, 0x807b,
  0xafb, 0x8079, 0xae2, 0x8077, 0xac9, 0x8075, 0xab0, 0x8073,
  0xa97, 0x8071, 0xa7e, 0x806f, 0xa65, 0x806d, 0xa4c, 0x806b,
  0xa33, 0x8069, 0xa19, 0x8067, 0xa00, 0x8065, 0x9e7, 0x8063,
  0x9ce, 0x8061, 0x9b5, 0x805f, 0x99c, 0x805d, 0x983, 0x805b,
  0x96a, 0x8059, 0x951, 0x8057, 0x938, 0x8056, 0x91f, 0x8054,
  0x906, 0x8052, 0x8ed, 0x8050, 0x8d4, 0x804f, 0x8bb, 0x804d,
  0x8a2, 0x804b, 0x888, 0x8049, 0x86f, 0x8048, 0x856, 0x8046,
  0x83d, 0x8044, 0x824, 0x8043, 0x80b, 0x8041, 0x7f2, 0x8040,
  0x7d9, 0x803e, 0x7c0, 0x803d, 0x7a7, 0x803b, 0x78e, 0x803a,
  0x775, 0x8038, 0x75b, 0x8037, 0x742, 0x8035, 0x729, 0x8034,
  0x710, 0x8032, 0x6f7, 0x8031, 0x6de, 0x8030, 0x6c5, 0x802e,
  0x6ac, 0x802d, 0x693, 0x802c, 0x67a, 0x802a, 0x660, 0x8029,
  0x647, 0x8028, 0x62e, 0x8027, 0x615, 0x8026, 0x5fc, 0x8024,
  0x5e3, 0x8023, 0x5ca, 0x8022, 0x5b1, 0x8021, 0x598, 0x8020,
  0x57f, 0x801f, 0x565, 0x801e, 0x54c, 0x801d, 0x533, 0x801c,
  0x51a, 0x801b, 0x501, 0x801a, 0x4e8, 0x8019, 0x4cf, 0x8018,
  0x4b6, 0x8017, 0x49c, 0x8016, 0x483, 0x8015, 0x46a, 0x8014,
  0x451, 0x8013, 0x438, 0x8012, 0x41f, 0x8012, 0x406, 0x8011,
  0x3ed, 0x8010, 0x3d4, 0x800f, 0x3ba, 0x800e, 0x3a1, 0x800e,
  0x388, 0x800d, 0x36f, 0x800c, 0x356, 0x800c, 0x33d, 0x800b,
  0x324, 0x800a, 0x30b, 0x800a, 0x2f1, 0x8009, 0x2d8, 0x8009,
  0x2bf, 0x8008, 0x2a6, 0x8008, 0x28d, 0x8007, 0x274, 0x8007,
  0x25b, 0x8006, 0x242, 0x8006, 0x228, 0x8005, 0x20f, 0x8005,
  0x1f6, 0x8004, 0x1dd, 0x8004, 0x1c4, 0x8004, 0x1ab, 0x8003,
  0x192, 0x8003, 0x178, 0x8003, 0x15f, 0x8002, 0x146, 0x8002,
  0x12d, 0x8002, 0x114, 0x8002, 0xfb, 0x8001, 0xe2, 0x8001,
  0xc9, 0x8001, 0xaf, 0x8001, 0x96, 0x8001, 0x7d, 0x8001,
  0x64, 0x8001, 0x4b, 0x8001, 0x32, 0x8001, 0x19, 0x8001,
};

static const q15_t ALIGN4 WeightsQ15_8192[16384] = {
  0x7fff, 0x0, 0x7fff, 0xfffa, 0x7fff, 0xfff4, 0x7fff, 0xffee,
  0x7fff, 0xffe7, 0x7fff, 0xffe1, 0x7fff, 0xffdb, 0x7fff, 0xffd5,
  0x7fff, 0xffce, 0x7fff, 0xffc8, 0x7fff, 0xffc2, 0x7fff, 0xffbb,
  0x7fff, 0xffb5, 0x7fff, 0xffaf, 0x7fff, 0xffa9, 0x7fff, 0xffa2,
  0x7fff, 0xff9c, 0x7fff, 0xff96, 0x7fff, 0xff8f, 0x7fff, 0xff89,
  0x7fff, 0xff83, 0x7fff, 0xff7d, 0x7fff, 0xff76, 0x7fff, 0xff70,
  0x7fff, 0xff6a, 0x7fff, 0xff63, 0x7fff, 0xff5d, 0x7fff, 0xff57,
  0x7fff, 0xff51, 0x7fff, 0xff4a, 0x7fff, 0xff44, 0x7fff, 0xff3e,
  0x7fff, 0xff37, 0x7fff, 0xff31, 0x7fff, 0xff2b, 0x7fff, 0xff25,
  0x7fff, 0xff1e, 0x7fff, 0xff18, 0x7fff, 0xff12, 0x7fff, 0xff0b,
  0x7fff, 0xff05, 0x7ffe, 0xfeff, 0x7ffe, 0xfef9, 0x7ffe, 0xfef2,
  0x7ffe, 0xfeec, 0x7ffe, 0xfee6, 0x7ffe, 0xfedf, 0x7ffe, 0xfed9,
  0x7ffe, 0xfed3, 0x7ffe, 0xfecd, 0x7ffe, 0xfec6, 0x7ffe, 0xfec0,
  0x7ffe, 0xfeba, 0x7ffe, 0xfeb3, 0x7ffe, 0xfead, 0x7ffe, 0xfea7,
  0x7ffe, 0xfea1, 0x7ffe, 0xfe9a, 0x7ffd, 0xfe94, 0x7ffd, 0xfe8e,
  0x7ffd, 0xfe88, 0x7ffd, 0xfe81, 0x7ffd, 0xfe7b, 0x7ffd, 0xfe75,
  0x7ffd, 0xfe6e, 0x7ffd, 0xfe68, 0x7ffd, 0xfe62, 0x7ffd, 0xfe5c,
  0x7ffd, 0xfe55, 0x7ffd, 0xfe4f, 0x7ffd, 0xfe49, 0x7ffc, 0xfe42,
  0x7ffc, 0xfe3c, 0x7ffc, 0xfe36, 0x7ffc, 0xfe30, 0x7ffc, 0xfe29,
  0x7ffc, 0xfe23, 0x7ffc, 0xfe1d, 0x7ffc, 0xfe16, 0x7ffc, 0xfe10,
  0x7ffc, 0xfe0a, 0x7ffc, 0xfe04, 0x7ffb, 0xfdfd, 0x7ffb, 0xfdf7,
  0x7ffb, 0xfdf1, 0x7ffb, 0xfdea, 0x7ffb, 0xfde4, 0x7ffb, 0xfdde,
  0x7ffb, 0xfdd8, 0x7ffb, 0xfdd1, 0x7ffb, 0xfdcb, 0x7ffb, 0xfdc5,
  0x7ffa, 0xfdbe, 0x7ffa, 0xfdb8, 0x7ffa, 0xfdb2, 0x7ffa, 0xfdac,
  0x7ffa, 0xfda5, 0x7ffa, 0xfd9f, 0x7ffa, 0xfd99, 0x7ffa, 0xfd93,
  0x7ff9, 0xfd8c, 0x7ff9, 0xfd86, 0x7ff9, 0xfd80, 0x7ff9, 0xfd79,
  0x7ff9, 0xfd73, 0x7ff9, 0xfd6d, 0x7ff9, 0xfd67, 0x7ff9, 0xfd60,
  0x7ff8, 0xfd5a, 0x7ff8, 0xfd54, 0x7ff8, 0xfd4d, 0x7ff8, 0xfd47,
  0x7ff8, 0xfd41, 0x7ff8, 0xfd3b, 0x7ff8, 0xfd34, 0x7ff8, 0xfd2e,
  0x7ff7, 0xfd28, 0x7ff7, 0xfd21, 0x7ff7, 0xfd1b, 0x7ff7, 0xfd15,
  0x7ff7, 0xfd0f, 0x7ff7, 0xfd08, 0x7ff7, 0xfd02, 0x7ff6, 0xfcfc,
  0x7ff6, 0xfcf5, 0x7ff6, 0xfcef, 0x7ff6, 0xfce9, 0x7ff6, 0xfce3,
  0x7ff6, 0xfcdc, 0x7ff5, 0xfcd6, 0x7ff5, 0xfcd0, 0x7ff5, 0xfcc9,
  0x7ff5, 0xfcc3, 0x7ff5, 0xfcbd, 0x7ff5, 0xfcb7, 0x7ff5, 0xfcb0,
  0x7ff4, 0xfcaa, 0x7ff4, 0xfca4, 0x7ff4, 0xfc9e, 0x7ff4, 0xfc97,
  0x7ff4, 0xfc91, 0x7ff4, 0xfc8b, 0x7ff3, 0xfc84, 0x7ff3, 0xfc7e,
  0x7ff3, 0xfc78, 0x7ff3, 0xfc72, 0x7ff3, 0xfc6b, 0x7ff2, 0xfc65,
  0x7ff2, 0xfc5f, 0x7ff2, 0xfc58, 0x7ff2, 0xfc52, 0x7ff2, 0xfc4c,
  0x7ff2, 0xfc46, 0x7ff1, 0xfc3f, 0x7ff1, 0xfc39, 0x7ff1, 0xfc33,
  0x7ff1, 0xfc2c, 0x7ff1, 0xfc26, 0x7ff0, 0xfc20, 0x7ff0, 0xfc1a,
  0x7ff0, 0xfc13, 0x7ff0, 0xfc0d, 0x7ff0, 0xfc07, 0x7fef, 0xfc01,
  0x7fef, 0xfbfa, 0x7fef, 0xfbf4, 0x7fef, 0xfbee, 0x7fef, 0xfbe7,
  0x7fee, 0xfbe1, 0x7fee, 0xfbdb, 0x7fee, 0xfbd5, 0x7fee, 0xfbce,
  0x7fee, 0xfbc8, 0x7fed, 0xfbc2, 0x7fed, 0xfbbb, 0x7fed, 0xfbb5,
  0x7fed, 0xfbaf, 0x7fed, 0xfba9, 0x7fec, 0xfba2, 0x7fec, 0xfb9c,
  0x7fec, 0xfb96, 0x7fec, 0xfb8f, 0x7fec, 0xfb89, 0x7feb, 0xfb83,
  0x7feb, 0xfb7d, 0x7feb, 0xfb76, 0x7feb, 0xfb70, 0x7fea, 0xfb6a,
  0x7fea, 0xfb64, 0x7fea, 0xfb5d, 0x7fea, 0xfb57, 0x7fea, 0xfb51,
  0x7fe9, 0xfb4a, 0x7fe9, 0xfb44, 0x7fe9, 0xfb3e, 0x7fe9, 0xfb38,
  0x7fe8, 0xfb31, 0x7fe8, 0xfb2b, 0x7fe8, 0xfb25, 0x7fe8, 0xfb1e,
  0x7fe7, 0xfb18, 0x7fe7, 0xfb12, 0x7fe7, 0xfb0c, 0x7fe7, 0xfb05,
  0x7fe6, 0xfaff, 0x7fe6, 0xfaf9, 0x7fe6, 0xfaf3, 0x7fe6, 0xfaec,
  0x7fe5, 0xfae6, 0x7fe5, 0xfae0, 0x7fe5, 0xfad9, 0x7fe5, 0xfad3,
  0x7fe4, 0xfacd, 0x7fe4, 0xfac7, 0x7fe4, 0xfac0, 0x7fe4, 0xfaba,
  0x7fe3, 0xfab4, 0x7fe3, 0xfaad, 0x7fe3, 0xfaa7, 0x7fe3, 0xfaa1,
  0x7fe2, 0xfa9b, 0x7fe2, 0xfa94, 0x7fe2, 0xfa8e, 0x7fe2, 0xfa88,
  0x7fe1, 0xfa81, 0x7fe1, 0xfa7b, 0x7fe1, 0xfa75, 0x7fe0, 0xfa6f,
  0x7fe0, 0xfa68, 0x7fe0, 0xfa62, 0x7fe0, 0xfa5c, 0x7fdf, 0xfa56,
  0x7fdf, 0xfa4f, 0x7fdf, 0xfa49, 0x7fdf, 0xfa43, 0x7fde, 0xfa3c,
  0x7fde, 0xfa36, 0x7fde, 0xfa30, 0x7fdd, 0xfa2a, 0x7fdd, 0xfa23,
  0x7fdd, 0xfa1d, 0x7fdd, 0xfa17, 0x7fdc, 0xfa11, 0x7fdc, 0xfa0a,
  0x7fdc, 0xfa04, 0x7fdb, 0xf9fe, 0x7fdb, 0xf9f7, 0x7fdb, 0xf9f1,
  0x7fda, 0xf9eb, 0x7fda, 0xf9e5, 0x7fda, 0xf9de, 0x7fda, 0xf9d8,
  0x7fd9, 0xf9d2, 0x7fd9, 0xf9cb, 0x7fd9, 0xf9c5, 0x7fd8, 0xf9bf,
  0x7fd8, 0xf9b9, 0x7fd8, 0xf9b2, 0x7fd7, 0xf9ac, 0x7fd7, 0xf9a6,
  0x7fd7, 0xf9a0, 0x7fd6, 0xf999, 0x7fd6, 0xf993, 0x7fd6, 0xf98d,
  0x7fd6, 0xf986, 0x7fd5, 0xf980, 0x7fd5, 0xf97a, 0x7fd5, 0xf974,
  0x7fd4, 0xf96d, 0x7fd4, 0xf967, 0x7fd4, 0xf961, 0x7fd3, 0xf95b,
  0x7fd3, 0xf954, 0x7fd3, 0xf94e, 0x7fd2, 0xf948, 0x7fd2, 0xf941,
  0x7fd2, 0xf93b, 0x7fd1, 0xf935, 0x7fd1, 0xf92f, 0x7fd1, 0xf928,
  0x7fd0, 0xf922, 0x7fd0, 0xf91c, 0x7fd0, 0xf916, 0x7fcf, 0xf90f,
  0x7fcf, 0xf909, 0x7fcf, 0xf903, 0x7fce, 0xf8fc, 0x7fce, 0xf8f6,
  0x7fce, 0xf8f0, 0x7fcd, 0xf8ea, 0x7fcd, 0xf8e3, 0x7fcd, 0xf8dd,
  0x7fcc, 0xf8d7, 0x7fcc, 0xf8d0, 0x7fcb, 0xf8ca, 0x7fcb, 0xf8c4,
  0x7fcb, 0xf8be, 0x7fca, 0xf8b7, 0x7fca, 0xf8b1, 0x7fca, 0xf8ab,
  0x7fc9, 0xf8a5, 0x7fc9, 0xf89e, 0x7fc9, 0xf898, 0x7fc8, 0xf892,
  0x7fc8, 0xf88b, 0x7fc7, 0xf885, 0x7fc7, 0xf87f, 0x7fc7, 0xf879,
  0x7fc6, 0xf872, 0x7fc6, 0xf86c, 0x7fc6, 0xf866, 0x7fc5, 0xf860,
  0x7fc5, 0xf859, 0x7fc5, 0xf853, 0x7fc4, 0xf84d, 0x7fc4, 0xf846,
  0x7fc3, 0xf840, 0x7fc3, 0xf83a, 0x7fc3, 0xf834, 0x7fc2, 0xf82d,
  0x7fc2, 0xf827, 0x7fc1, 0xf821, 0x7fc1, 0xf81b, 0x7fc1, 0xf814,
  0x7fc0, 0xf80e, 0x7fc0, 0xf808, 0x7fc0, 0xf802, 0x7fbf, 0xf7fb,
  0x7fbf, 0xf7f5, 0x7fbe, 0xf7ef, 0x7fbe, 0xf7e8, 0x7fbe, 0xf7e2,
  0x7fbd, 0xf7dc, 0x7fbd, 0xf7d6, 0x7fbc, 0xf7cf, 0x7fbc, 0xf7c9,
  0x7fbc, 0xf7c3, 0x7fbb, 0xf7bd, 0x7fbb, 0xf7b6, 0x7fba, 0xf7b0,
  0x7fba, 0xf7aa, 0x7fb9, 0xf7a3, 0x7fb9, 0xf79d, 0x7fb9, 0xf797,
  0x7fb8, 0xf791, 0x7fb8, 0xf78a, 0x7fb7, 0xf784, 0x7fb7, 0xf77e,
  0x7fb7, 0xf778, 0x7fb6, 0xf771, 0x7fb6, 0xf76b, 0x7fb5, 0xf765,
  0x7fb5, 0xf75e, 0x7fb4, 0xf758, 0x7fb4, 0xf752, 0x7fb4, 0xf74c,
  0x7fb3, 0xf745, 0x7fb3, 0xf73f, 0x7fb2, 0xf739, 0x7fb2, 0xf733,
  0x7fb1, 0xf72c, 0x7fb1, 0xf726, 0x7fb1, 0xf720, 0x7fb0, 0xf71a,
  0x7fb0, 0xf713, 0x7faf, 0xf70d, 0x7faf, 0xf707, 0x7fae, 0xf700,
  0x7fae, 0xf6fa, 0x7fae, 0xf6f4, 0x7fad, 0xf6ee, 0x7fad, 0xf6e7,
  0x7fac, 0xf6e1, 0x7fac, 0xf6db, 0x7fab, 0xf6d5, 0x7fab, 0xf6ce,
  0x7faa, 0xf6c8, 0x7faa, 0xf6c2, 0x7fa9, 0xf6bc, 0x7fa9, 0xf6b5,
  0x7fa9, 0xf6af, 0x7fa8, 0xf6a9, 0x7fa8, 0xf6a2, 0x7fa7, 0xf69c,
  0x7fa7, 0xf696, 0x7fa6, 0xf690, 0x7fa6, 0xf689, 0x7fa5, 0xf683,
  0x7fa5, 0xf67d, 0x7fa4, 0xf677, 0x7fa4, 0xf670, 0x7fa3, 0xf66a,
  0x7fa3, 0xf664, 0x7fa3, 0xf65e, 0x7fa2, 0xf657, 0x7fa2, 0xf651,
  0x7fa1, 0xf64b, 0x7fa1, 0xf644, 0x7fa0, 0xf63e, 0x7fa0, 0xf638,
  0x7f9f, 0xf632, 0x7f9f, 0xf62b, 0x7f9e, 0xf625, 0x7f9e, 0xf61f,
  0x7f9d, 0xf619, 0x7f9d, 0xf612, 0x7f9c, 0xf60c, 0x7f9c, 0xf606,
  0x7f9b, 0xf600, 0x7f9b, 0xf5f9, 0x7f9a, 0xf5f3, 0x7f9a, 0xf5ed,
  0x7f99, 0xf5e7, 0x7f99, 0xf5e0, 0x7f98, 0xf5da, 0x7f98, 0xf5d4,
  0x7f97, 0xf5cd, 0x7f97, 0xf5c7, 0x7f96, 0xf5c1, 0x7f96, 0xf5bb,
  0x7f95, 0xf5b4, 0x7f95, 0xf5ae, 0x7f94, 0xf5a8, 0x7f94, 0xf5a2,
  0x7f93, 0xf59b, 0x7f93, 0xf595, 0x7f92, 0xf58f, 0x7f92, 0xf589,
  0x7f91, 0xf582, 0x7f91, 0xf57c, 0x7f90, 0xf576, 0x7f90, 0xf570,
  0x7f8f, 0xf569, 0x7f8f, 0xf563, 0x7f8e, 0xf55d, 0x7f8e, 0xf556,
  0x7f8d, 0xf550, 0x7f8d, 0xf54a, 0x7f8c, 0xf544, 0x7f8b, 0xf53d,
  0x7f8b, 0xf537, 0x7f8a, 0xf531, 0x7f8a, 0xf52b, 0x7f89, 0xf524,
  0x7f89, 0xf51e, 0x7f88, 0xf518, 0x7f88, 0xf512, 0x7f87, 0xf50b,
  0x7f87, 0xf505, 0x7f86, 0xf4ff, 0x7f86, 0xf4f9, 0x7f85, 0xf4f2,
  0x7f85, 0xf4ec, 0x7f84, 0xf4e6, 0x7f83, 0xf4e0, 0x7f83, 0xf4d9,
  0x7f82, 0xf4d3, 0x7f82, 0xf4cd, 0x7f81, 0xf4c6, 0x7f81, 0xf4c0,
  0x7f80, 0xf4ba, 0x7f80, 0xf4b4, 0x7f7f, 0xf4ad, 0x7f7e, 0xf4a7,
  0x7f7e, 0xf4a1, 0x7f7d, 0xf49b, 0x7f7d, 0xf494, 0x7f7c, 0xf48e,
  0x7f7c, 0xf488, 0x7f7b, 0xf482, 0x7f7b, 0xf47b, 0x7f7a, 0xf475,
  0x7f79, 0xf46f, 0x7f79, 0xf469, 0x7f78, 0xf462, 0x7f78, 0xf45c,
  0x7f77, 0xf456, 0x7f77, 0xf450, 0x7f76, 0xf449, 0x7f75, 0xf443,
  0x7f75, 0xf43d, 0x7f74, 0xf437, 0x7f74, 0xf430, 0x7f73, 0xf42a,
  0x7f72, 0xf424, 0x7f72, 0xf41e, 0x7f71, 0xf417, 0x7f71, 0xf411,
  0x7f70, 0xf40b, 0x7f70, 0xf405, 0x7f6f, 0xf3fe, 0x7f6e, 0xf3f8,
  0x7f6e, 0xf3f2, 0x7f6d, 0xf3ec, 0x7f6d, 0xf3e5, 0x7f6c, 0xf3df,
  0x7f6b, 0xf3d9, 0x7f6b, 0xf3d2, 0x7f6a, 0xf3cc, 0x7f6a, 0xf3c6,
  0x7f69, 0xf3c0, 0x7f68, 0xf3b9, 0x7f68, 0xf3b3, 0x7f67, 0xf3ad,
  0x7f67, 0xf3a7, 0x7f66, 0xf3a0, 0x7f65, 0xf39a, 0x7f65, 0xf394,
  0x7f64, 0xf38e, 0x7f64, 0xf387, 0x7f63, 0xf381, 0x7f62, 0xf37b,
  0x7f62, 0xf375, 0x7f61, 0xf36e, 0x7f60, 0xf368, 0x7f60, 0xf362,
  0x7f5f, 0xf35c, 0x7f5f, 0xf355, 0x7f5e, 0xf34f, 0x7f5d, 0xf349,
  0x7f5d, 0xf343, 0x7f5c, 0xf33c, 0x7f5b, 0xf336, 0x7f5b, 0xf330,
  0x7f5a, 0xf32a, 0x7f5a, 0xf323, 0x7f59, 0xf31d, 0x7f58, 0xf317,
  0x7f58, 0xf311, 0x7f57, 0xf30a, 0x7f56, 0xf304, 0x7f56, 0xf2fe,
  0x7f55, 0xf2f8, 0x7f55, 0xf2f1, 0x7f54, 0xf2eb, 0x7f53, 0xf2e5,
  0x7f53, 0xf2df, 0x7f52, 0xf2d8, 0x7f51, 0xf2d2, 0x7f51, 0xf2cc,
  0x7f50, 0xf2c6, 0x7f4f, 0xf2bf, 0x7f4f, 0xf2b9, 0x7f4e, 0xf2b3,
  0x7f4d, 0xf2ad, 0x7f4d, 0xf2a6, 0x7f4c, 0xf2a0, 0x7f4b, 0xf29a,
  0x7f4b, 0xf294, 0x7f4a, 0xf28d, 0x7f49, 0xf287, 0x7f49, 0xf281,
  0x7f48, 0xf27b, 0x7f47, 0xf274, 0x7f47, 0xf26e, 0x7f46, 0xf268,
  0x7f45, 0xf262, 0x7f45, 0xf25b, 0x7f44, 0xf255, 0x7f43, 0xf24f,
  0x7f43, 0xf249, 0x7f42, 0xf242, 0x7f41, 0xf23c, 0x7f41, 0xf236,
  0x7f40, 0xf230, 0x7f3f, 0xf229, 0x7f3f, 0xf223, 0x7f3e, 0xf21d,
  0x7f3d, 0xf217, 0x7f3d, 0xf210, 0x7f3c, 0xf20a, 0x7f3b, 0xf204,
  0x7f3b, 0xf1fe, 0x7f3a, 0xf1f7, 0x7f39, 0xf1f1, 0x7f39, 0xf1eb,
  0x7f38, 0xf1e5, 0x7f37, 0xf1de, 0x7f36, 0xf1d8, 0x7f36, 0xf1d2,
  0x7f35, 0xf1cc, 0x7f34, 0xf1c6, 0x7f34, 0xf1bf, 0x7f33, 0xf1b9,
  0x7f32, 0xf1b3, 0x7f32, 0xf1ad, 0x7f31, 0xf1a6, 0x7f30, 0xf1a0,
  0x7f2f, 0xf19a, 0x7f2f, 0xf194, 0x7f2e, 0xf18d, 0x7f2d, 0xf187,
  0x7f2d, 0xf181, 0x7f2c, 0xf17b, 0x7f2b, 0xf174, 0x7f2a, 0xf16e,
  0x7f2a, 0xf168, 0x7f29, 0xf162, 0x7f28, 0xf15b, 0x7f28, 0xf155,
  0x7f27, 0xf14f, 0x7f26, 0xf149, 0x7f25, 0xf142, 0x7f25, 0xf13c,
  0x7f24, 0xf136, 0x7f23, 0xf130, 0x7f23, 0xf129, 0x7f22, 0xf123,
  0x7f21, 0xf11d, 0x7f20, 0xf117, 0x7f20, 0xf110, 0x7f1f, 0xf10a,
  0x7f1e, 0xf104, 0x7f1d, 0xf0fe, 0x7f1d, 0xf0f8, 0x7f1c, 0xf0f1,
  0x7f1b, 0xf0eb, 0x7f1a, 0xf0e5, 0x7f1a, 0xf0df, 0x7f19, 0xf0d8,
  0x7f18, 0xf0d2, 0x7f17, 0xf0cc, 0x7f17, 0xf0c6, 0x7f16, 0xf0bf,
  0x7f15, 0xf0b9, 0x7f14, 0xf0b3, 0x7f14, 0xf0ad, 0x7f13, 0xf0a6,
  0x7f12, 0xf0a0, 0x7f11, 0xf09a, 0x7f11, 0xf094, 0x7f10, 0xf08d,
  0x7f0f, 0xf087, 0x7f0e, 0xf081, 0x7f0e, 0xf07b, 0x7f0d, 0xf075,
  0x7f0c, 0xf06e, 0x7f0b, 0xf068, 0x7f0b, 0xf062, 0x7f0a, 0xf05c,
  0x7f09, 0xf055, 0x7f08, 0xf04f, 0x7f08, 0xf049, 0x7f07, 0xf043,
  0x7f06, 0xf03c, 0x7f05, 0xf036, 0x7f04, 0xf030, 0x7f04, 0xf02a,
  0x7f03, 0xf023, 0x7f02, 0xf01d, 0x7f01, 0xf017, 0x7f01, 0xf011,
  0x7f00, 0xf00b, 0x7eff, 0xf004, 0x7efe, 0xeffe, 0x7efd, 0xeff8,
  0x7efd, 0xeff2, 0x7efc, 0xefeb, 0x7efb, 0xefe5, 0x7efa, 0xefdf,
  0x7ef9, 0xefd9, 0x7ef9, 0xefd2, 0x7ef8, 0xefcc, 0x7ef7, 0xefc6,
  0x7ef6, 0xefc0, 0x7ef5, 0xefb9, 0x7ef5, 0xefb3, 0x7ef4, 0xefad,
  0x7ef3, 0xefa7, 0x7ef2, 0xefa1, 0x7ef1, 0xef9a, 0x7ef1, 0xef94,
  0x7ef0, 0xef8e, 0x7eef, 0xef88, 0x7eee, 0xef81, 0x7eed, 0xef7b,
  0x7eed, 0xef75, 0x7eec, 0xef6f, 0x7eeb, 0xef68, 0x7eea, 0xef62,
  0x7ee9, 0xef5c, 0x7ee9, 0xef56, 0x7ee8, 0xef50, 0x7ee7, 0xef49,
  0x7ee6, 0xef43, 0x7ee5, 0xef3d, 0x7ee4, 0xef37, 0x7ee4, 0xef30,
  0x7ee3, 0xef2a, 0x7ee2, 0xef24, 0x7ee1, 0xef1e, 0x7ee0, 0xef18,
  0x7edf, 0xef11, 0x7edf, 0xef0b, 0x7ede, 0xef05, 0x7edd, 0xeeff,
  0x7edc, 0xeef8, 0x7edb, 0xeef2, 0x7eda, 0xeeec, 0x7eda, 0xeee6,
  0x7ed9, 0xeedf, 0x7ed8, 0xeed9, 0x7ed7, 0xeed3, 0x7ed6, 0xeecd,
  0x7ed5, 0xeec7, 0x7ed5, 0xeec0, 0x7ed4, 0xeeba, 0x7ed3, 0xeeb4,
  0x7ed2, 0xeeae, 0x7ed1, 0xeea7, 0x7ed0, 0xeea1, 0x7ecf, 0xee9b,
  0x7ecf, 0xee95, 0x7ece, 0xee8f, 0x7ecd, 0xee88, 0x7ecc, 0xee82,
  0x7ecb, 0xee7c, 0x7eca, 0xee76, 0x7ec9, 0xee6f, 0x7ec9, 0xee69,
  0x7ec8, 0xee63, 0x7ec7, 0xee5d, 0x7ec6, 0xee57, 0x7ec5, 0xee50,
  0x7ec4, 0xee4a, 0x7ec3, 0xee44, 0x7ec3, 0xee3e, 0x7ec2, 0xee37,
  0x7ec1, 0xee31, 0x7ec0, 0xee2b, 0x7ebf, 0xee25, 0x7ebe, 0xee1f,
  0x7ebd, 0xee18, 0x7ebc, 0xee12, 0x7ebb, 0xee0c, 0x7ebb, 0xee06,
  0x7eba, 0xedff, 0x7eb9, 0xedf9, 0x7eb8, 0xedf3, 0x7eb7, 0xeded,
  0x7eb6, 0xede7, 0x7eb5, 0xede0, 0x7eb4, 0xedda, 0x7eb4, 0xedd4,
  0x7eb3, 0xedce, 0x7eb2, 0xedc7, 0x7eb1, 0xedc1, 0x7eb0, 0xedbb,
  0x7eaf, 0xedb5, 0x7eae, 0xedaf, 0x7ead, 0xeda8, 0x7eac, 0xeda2,
  0x7eab, 0xed9c, 0x7eab, 0xed96, 0x7eaa, 0xed8f, 0x7ea9, 0xed89,
  0x7ea8, 0xed83, 0x7ea7, 0xed7d, 0x7ea6, 0xed77, 0x7ea5, 0xed70,
  0x7ea4, 0xed6a, 0x7ea3, 0xed64, 0x7ea2, 0xed5e, 0x7ea1, 0xed58,
  0x7ea1, 0xed51, 0x7ea0, 0xed4b, 0x7e9f, 0xed45, 0x7e9e, 0xed3f,
  0x7e9d, 0xed38, 0x7e9c, 0xed32, 0x7e9b, 0xed2c, 0x7e9a, 0xed26,
  0x7e99, 0xed20, 0x7e98, 0xed19, 0x7e97, 0xed13, 0x7e96, 0xed0d,
  0x7e95, 0xed07, 0x7e94, 0xed01, 0x7e94, 0xecfa, 0x7e93, 0xecf4,
  0x7e92, 0xecee, 0x7e91, 0xece8, 0x7e90, 0xece1, 0x7e8f, 0xecdb,
  0x7e8e, 0xecd5, 0x7e8d, 0xeccf, 0x7e8c, 0xecc9, 0x7e8b, 0xecc2,
  0x7e8a, 0xecbc, 0x7e89, 0xecb6, 0x7e88, 0xecb0, 0x7e87, 0xecaa,
  0x7e86, 0xeca3, 0x7e85, 0xec9d, 0x7e84, 0xec97, 0x7e84, 0xec91,
  0x7e83, 0xec8a, 0x7e82, 0xec84, 0x7e81, 0xec7e, 0x7e80, 0xec78,
  0x7e7f, 0xec72, 0x7e7e, 0xec6b, 0x7e7d, 0xec65, 0x7e7c, 0xec5f,
  0x7e7b, 0xec59, 0x7e7a, 0xec53, 0x7e79, 0xec4c, 0x7e78, 0xec46,
  0x7e77, 0xec40, 0x7e76, 0xec3a, 0x7e75, 0xec34, 0x7e74, 0xec2d,
  0x7e73, 0xec27, 0x7e72, 0xec21, 0x7e71, 0xec1b, 0x7e70, 0xec15,
  0x7e6f, 0xec0e, 0x7e6e, 0xec08, 0x7e6d, 0xec02, 0x7e6c, 0xebfc,
  0x7e6b, 0xebf5, 0x7e6a, 0xebef, 0x7e69, 0xebe9, 0x7e68, 0xebe3,
  0x7e67, 0xebdd, 0x7e66, 0xebd6, 0x7e65, 0xebd0, 0x7e64, 0xebca,
  0x7e63, 0xebc4, 0x7e62, 0xebbe, 0x7e61, 0xebb7, 0x7e60, 0xebb1,
  0x7e5f, 0xebab, 0x7e5e, 0xeba5, 0x7e5d, 0xeb9f, 0x7e5c, 0xeb98,
  0x7e5b, 0xeb92, 0x7e5a, 0xeb8c, 0x7e59, 0xeb86, 0x7e58, 0xeb80,
  0x7e57, 0xeb79, 0x7e56, 0xeb73, 0x7e55, 0xeb6d, 0x7e54, 0xeb67,
  0x7e53, 0xeb61, 0x7e52, 0xeb5a, 0x7e51, 0xeb54, 0x7e50, 0xeb4e,
  0x7e4f, 0xeb48, 0x7e4e, 0xeb42, 0x7e4d, 0xeb3b, 0x7e4c, 0xeb35,
  0x7e4b, 0xeb2f, 0x7e4a, 0xeb29, 0x7e49, 0xeb23, 0x7e48, 0xeb1c,
  0x7e47, 0xeb16, 0x7e46, 0xeb10, 0x7e45, 0xeb0a, 0x7e44, 0xeb04,
  0x7e43, 0xeafd, 0x7e42, 0xeaf7, 0x7e41, 0xeaf1, 0x7e40, 0xeaeb,
  0x7e3f, 0xeae5, 0x7e3e, 0xeade, 0x7e3d, 0xead8, 0x7e3c, 0xead2,
  0x7e3b, 0xeacc, 0x7e3a, 0xeac6, 0x7e39, 0xeabf, 0x7e38, 0xeab9,
  0x7e37, 0xeab3, 0x7e35, 0xeaad, 0x7e34, 0xeaa7, 0x7e33, 0xeaa0,
  0x7e32, 0xea9a, 0x7e31, 0xea94, 0x7e30, 0xea8e, 0x7e2f, 0xea88,
  0x7e2e, 0xea81, 0x7e2d, 0xea7b, 0x7e2c, 0xea75, 0x7e2b, 0xea6f,
  0x7e2a, 0xea69, 0x7e29, 0xea63, 0x7e28, 0xea5c, 0x7e27, 0xea56,
  0x7e26, 0xea50, 0x7e25, 0xea4a, 0x7e24, 0xea44, 0x7e22, 0xea3d,
  0x7e21, 0xea37, 0x7e20, 0xea31, 0x7e1f, 0xea2b, 0x7e1e, 0xea25,
  0x7e1d, 0xea1e, 0x7e1c, 0xea18, 0x7e1b, 0xea12, 0x7e1a, 0xea0c,
  0x7e19, 0xea06, 0x7e18, 0xe9ff, 0x7e17, 0xe9f9, 0x7e16, 0xe9f3,
  0x7e14, 0xe9ed, 0x7e13, 0xe9e7, 0x7e12, 0xe9e1, 0x7e11, 0xe9da,
  0x7e10, 0xe9d4, 0x7e0f, 0xe9ce, 0x7e0e, 0xe9c8, 0x7e0d, 0xe9c2,
  0x7e0c, 0xe9bb, 0x7e0b, 0xe9b5, 0x7e0a, 0xe9af, 0x7e08, 0xe9a9,
  0x7e07, 0xe9a3, 0x7e06, 0xe99c, 0x7e05, 0xe996, 0x7e04, 0xe990,
  0x7e03, 0xe98a, 0x7e02, 0xe984, 0x7e01, 0xe97e, 0x7e00, 0xe977,
  0x7dff, 0xe971, 0x7dfd, 0xe96b, 0x7dfc, 0xe965, 0x7dfb, 0xe95f,
  0x7dfa, 0xe958, 0x7df9, 0xe952, 0x7df8, 0xe94c, 0x7df7, 0xe946,
  0x7df6, 0xe940, 0x7df5, 0xe93a, 0x7df3, 0xe933, 0x7df2, 0xe92d,
  0x7df1, 0xe927, 0x7df0, 0xe921, 0x7def, 0xe91b, 0x7dee, 0xe914,
  0x7ded, 0xe90e, 0x7dec, 0xe908, 0x7dea, 0xe902, 0x7de9, 0xe8fc,
  0x7de8, 0xe8f6, 0x7de7, 0xe8ef, 0x7de6, 0xe8e9, 0x7de5, 0xe8e3,
  0x7de4, 0xe8dd, 0x7de2, 0xe8d7, 0x7de1, 0xe8d0, 0x7de0, 0xe8ca,
  0x7ddf, 0xe8c4, 0x7dde, 0xe8be, 0x7ddd, 0xe8b8, 0x7ddc, 0xe8b2,
  0x7dda, 0xe8ab, 0x7dd9, 0xe8a5, 0x7dd8, 0xe89f, 0x7dd7, 0xe899,
  0x7dd6, 0xe893, 0x7dd5, 0xe88c, 0x7dd4, 0xe886, 0x7dd2, 0xe880,
  0x7dd1, 0xe87a, 0x7dd0, 0xe874, 0x7dcf, 0xe86e, 0x7dce, 0xe867,
  0x7dcd, 0xe861, 0x7dcc, 0xe85b, 0x7dca, 0xe855, 0x7dc9, 0xe84f,
  0x7dc8, 0xe849, 0x7dc7, 0xe842, 0x7dc6, 0xe83c, 0x7dc5, 0xe836,
  0x7dc3, 0xe830, 0x7dc2, 0xe82a, 0x7dc1, 0xe823, 0x7dc0, 0xe81d,
  0x7dbf, 0xe817, 0x7dbd, 0xe811, 0x7dbc, 0xe80b, 0x7dbb, 0xe805,
  0x7dba, 0xe7fe, 0x7db9, 0xe7f8, 0x7db8, 0xe7f2, 0x7db6, 0xe7ec,
  0x7db5, 0xe7e6, 0x7db4, 0xe7e0, 0x7db3, 0xe7d9, 0x7db2, 0xe7d3,
  0x7db0, 0xe7cd, 0x7daf, 0xe7c7, 0x7dae, 0xe7c1, 0x7dad, 0xe7bb,
  0x7dac, 0xe7b4, 0x7dab, 0xe7ae, 0x7da9, 0xe7a8, 0x7da8, 0xe7a2,
  0x7da7, 0xe79c, 0x7da6, 0xe796, 0x7da5, 0xe78f, 0x7da3, 0xe789,
  0x7da2, 0xe783, 0x7da1, 0xe77d, 0x7da0, 0xe777, 0x7d9f, 0xe771,
  0x7d9d, 0xe76a, 0x7d9c, 0xe764, 0x7d9b, 0xe75e, 0x7d9a, 0xe758,
  0x7d98, 0xe752, 0x7d97, 0xe74c, 0x7d96, 0xe745, 0x7d95, 0xe73f,
  0x7d94, 0xe739, 0x7d92, 0xe733, 0x7d91, 0xe72d, 0x7d90, 0xe727,
  0x7d8f, 0xe720, 0x7d8e, 0xe71a, 0x7d8c, 0xe714, 0x7d8b, 0xe70e,
  0x7d8a, 0xe708, 0x7d89, 0xe702, 0x7d87, 0xe6fb, 0x7d86, 0xe6f5,
  0x7d85, 0xe6ef, 0x7d84, 0xe6e9, 0x7d82, 0xe6e3, 0x7d81, 0xe6dd,
  0x7d80, 0xe6d6, 0x7d7f, 0xe6d0, 0x7d7e, 0xe6ca, 0x7d7c, 0xe6c4,
  0x7d7b, 0xe6be, 0x7d7a, 0xe6b8, 0x7d79, 0xe6b2, 0x7d77, 0xe6ab,
  0x7d76, 0xe6a5, 0x7d75, 0xe69f, 0x7d74, 0xe699, 0x7d72, 0xe693,
  0x7d71, 0xe68d, 0x7d70, 0xe686, 0x7d6f, 0xe680, 0x7d6d, 0xe67a,
  0x7d6c, 0xe674, 0x7d6b, 0xe66e, 0x7d6a, 0xe668, 0x7d68, 0xe661,
  0x7d67, 0xe65b, 0x7d66, 0xe655, 0x7d65, 0xe64f, 0x7d63, 0xe649,
  0x7d62, 0xe643, 0x7d61, 0xe63d, 0x7d60, 0xe636, 0x7d5e, 0xe630,
  0x7d5d, 0xe62a, 0x7d5c, 0xe624, 0x7d5a, 0xe61e, 0x7d59, 0xe618,
  0x7d58, 0xe611, 0x7d57, 0xe60b, 0x7d55, 0xe605, 0x7d54, 0xe5ff,
  0x7d53, 0xe5f9, 0x7d52, 0xe5f3, 0x7d50, 0xe5ed, 0x7d4f, 0xe5e6,
  0x7d4e, 0xe5e0, 0x7d4c, 0xe5da, 0x7d4b, 0xe5d4, 0x7d4a, 0xe5ce,
  0x7d49, 0xe5c8, 0x7d47, 0xe5c2, 0x7d46, 0xe5bb, 0x7d45, 0xe5b5,
  0x7d43, 0xe5af, 0x7d42, 0xe5a9, 0x7d41, 0xe5a3, 0x7d3f, 0xe59d,
  0x7d3e, 0xe596, 0x7d3d, 0xe590, 0x7d3c, 0xe58a, 0x7d3a, 0xe584,
  0x7d39, 0xe57e, 0x7d38, 0xe578, 0x7d36, 0xe572, 0x7d35, 0xe56b,
  0x7d34, 0xe565, 0x7d32, 0xe55f, 0x7d31, 0xe559, 0x7d30, 0xe553,
  0x7d2f, 0xe54d, 0x7d2d, 0xe547, 0x7d2c, 0xe540, 0x7d2b, 0xe53a,
  0x7d29, 0xe534, 0x7d28, 0xe52e, 0x7d27, 0xe528, 0x7d25, 0xe522,
  0x7d24, 0xe51c, 0x7d23, 0xe515, 0x7d21, 0xe50f, 0x7d20, 0xe509,
  0x7d1f, 0xe503, 0x7d1d, 0xe4fd, 0x7d1c, 0xe4f7, 0x7d1b, 0xe4f1,
  0x7d19, 0xe4ea, 0x7d18, 0xe4e4, 0x7d17, 0xe4de, 0x7d15, 0xe4d8,
  0x7d14, 0xe4d2, 0x7d13, 0xe4cc, 0x7d11, 0xe4c6, 0x7d10, 0xe4bf,
  0x7d0f, 0xe4b9, 0x7d0d, 0xe4b3, 0x7d0c, 0xe4ad, 0x7d0b, 0xe4a7,
  0x7d09, 0xe4a1, 0x7d08, 0xe49b, 0x7d07, 0xe494, 0x7d05, 0xe48e,
  0x7d04, 0xe488, 0x7d03, 0xe482, 0x7d01, 0xe47c, 0x7d00, 0xe476,
  0x7cff, 0xe470, 0x7cfd, 0xe46a, 0x7cfc, 0xe463, 0x7cfb, 0xe45d,
  0x7cf9, 0xe457, 0x7cf8, 0xe451, 0x7cf6, 0xe44b, 0x7cf5, 0xe445,
  0x7cf4, 0xe43f, 0x7cf2, 0xe438, 0x7cf1, 0xe432, 0x7cf0, 0xe42c,
  0x7cee, 0xe426, 0x7ced, 0xe420, 0x7cec, 0xe41a, 0x7cea, 0xe414,
  0x7ce9, 0xe40e, 0x7ce7, 0xe407, 0x7ce6, 0xe401, 0x7ce5, 0xe3fb,
  0x7ce3, 0xe3f5, 0x7ce2, 0xe3ef, 0x7ce1, 0xe3e9, 0x7cdf, 0xe3e3,
  0x7cde, 0xe3dc, 0x7cdc, 0xe3d6, 0x7cdb, 0xe3d0, 0x7cda, 0xe3ca,
  0x7cd8, 0xe3c4, 0x7cd7, 0xe3be, 0x7cd5, 0xe3b8, 0x7cd4, 0xe3b2,
  0x7cd3, 0xe3ab, 0x7cd1, 0xe3a5, 0x7cd0, 0xe39f, 0x7ccf, 0xe399,
  0x7ccd, 0xe393, 0x7ccc, 0xe38d, 0x7cca, 0xe387, 0x7cc9, 0xe381,
  0x7cc8, 0xe37a, 0x7cc6, 0xe374, 0x7cc5, 0xe36e, 0x7cc3, 0xe368,
  0x7cc2, 0xe362, 0x7cc1, 0xe35c, 0x7cbf, 0xe356, 0x7cbe, 0xe350,
  0x7cbc, 0xe349, 0x7cbb, 0xe343, 0x7cb9, 0xe33d, 0x7cb8, 0xe337,
  0x7cb7, 0xe331, 0x7cb5, 0xe32b, 0x7cb4, 0xe325, 0x7cb2, 0xe31f,
  0x7cb1, 0xe318, 0x7cb0, 0xe312, 0x7cae, 0xe30c, 0x7cad, 0xe306,
  0x7cab, 0xe300, 0x7caa, 0xe2fa, 0x7ca8, 0xe2f4, 0x7ca7, 0xe2ee,
  0x7ca6, 0xe2e8, 0x7ca4, 0xe2e1, 0x7ca3, 0xe2db, 0x7ca1, 0xe2d5,
  0x7ca0, 0xe2cf, 0x7c9e, 0xe2c9, 0x7c9d, 0xe2c3, 0x7c9c, 0xe2bd,
  0x7c9a, 0xe2b7, 0x7c99, 0xe2b0, 0x7c97, 0xe2aa, 0x7c96, 0xe2a4,
  0x7c94, 0xe29e, 0x7c93, 0xe298, 0x7c91, 0xe292, 0x7c90, 0xe28c,
  0x7c8f, 0xe286, 0x7c8d, 0xe280, 0x7c8c, 0xe279, 0x7c8a, 0xe273,
  0x7c89, 0xe26d, 0x7c87, 0xe267, 0x7c86, 0xe261, 0x7c84, 0xe25b,
  0x7c83, 0xe255, 0x7c82, 0xe24f, 0x7c80, 0xe249, 0x7c7f, 0xe242,
  0x7c7d, 0xe23c, 0x7c7c, 0xe236, 0x7c7a, 0xe230, 0x7c79, 0xe22a,
  0x7c77, 0xe224, 0x7c76, 0xe21e, 0x7c74, 0xe218, 0x7c73, 0xe212,
  0x7c71, 0xe20b, 0x7c70, 0xe205, 0x7c6e, 0xe1ff, 0x7c6d, 0xe1f9,
  0x7c6c, 0xe1f3, 0x7c6a, 0xe1ed, 0x7c69, 0xe1e7, 0x7c67, 0xe1e1,
  0x7c66, 0xe1db, 0x7c64, 0xe1d4, 0x7c63, 0xe1ce, 0x7c61, 0xe1c8,
  0x7c60, 0xe1c2, 0x7c5e, 0xe1bc, 0x7c5d, 0xe1b6, 0x7c5b, 0xe1b0,
  0x7c5a, 0xe1aa, 0x7c58, 0xe1a4, 0x7c57, 0xe19e, 0x7c55, 0xe197,
  0x7c54, 0xe191, 0x7c52, 0xe18b, 0x7c51, 0xe185, 0x7c4f, 0xe17f,
  0x7c4e, 0xe179, 0x7c4c, 0xe173, 0x7c4b, 0xe16d, 0x7c49, 0xe167,
  0x7c48, 0xe160, 0x7c46, 0xe15a, 0x7c45, 0xe154, 0x7c43, 0xe14e,
  0x7c42, 0xe148, 0x7c40, 0xe142, 0x7c3f, 0xe13c, 0x7c3d, 0xe136,
  0x7c3c, 0xe130, 0x7c3a, 0xe12a, 0x7c39, 0xe123, 0x7c37, 0xe11d,
  0x7c36, 0xe117, 0x7c34, 0xe111, 0x7c33, 0xe10b, 0x7c31, 0xe105,
  0x7c30, 0xe0ff, 0x7c2e, 0xe0f9, 0x7c2d, 0xe0f3, 0x7c2b, 0xe0ed,
  0x7c29, 0xe0e7, 0x7c28, 0xe0e0, 0x7c26, 0xe0da, 0x7c25, 0xe0d4,
  0x7c23, 0xe0ce, 0x7c22, 0xe0c8, 0x7c20, 0xe0c2, 0x7c1f, 0xe0bc,
  0x7c1d, 0xe0b6, 0x7c1c, 0xe0b0, 0x7c1a, 0xe0aa, 0x7c19, 0xe0a3,
  0x7c17, 0xe09d, 0x7c16, 0xe097, 0x7c14, 0xe091, 0x7c12, 0xe08b,
  0x7c11, 0xe085, 0x7c0f, 0xe07f, 0x7c0e, 0xe079, 0x7c0c, 0xe073,
  0x7c0b, 0xe06d, 0x7c09, 0xe067, 0x7c08, 0xe061, 0x7c06, 0xe05a,
  0x7c05, 0xe054, 0x7c03, 0xe04e, 0x7c01, 0xe048, 0x7c00, 0xe042,
  0x7bfe, 0xe03c, 0x7bfd, 0xe036, 0x7bfb, 0xe030, 0x7bfa, 0xe02a,
  0x7bf8, 0xe024, 0x7bf6, 0xe01e, 0x7bf5, 0xe017, 0x7bf3, 0xe011,
  0x7bf2, 0xe00b, 0x7bf0, 0xe005, 0x7bef, 0xdfff, 0x7bed, 0xdff9,
  0x7beb, 0xdff3, 0x7bea, 0xdfed, 0x7be8, 0xdfe7, 0x7be7, 0xdfe1,
  0x7be5, 0xdfdb, 0x7be4, 0xdfd5, 0x7be2, 0xdfce, 0x7be0, 0xdfc8,
  0x7bdf, 0xdfc2, 0x7bdd, 0xdfbc, 0x7bdc, 0xdfb6, 0x7bda, 0xdfb0,
  0x7bd9, 0xdfaa, 0x7bd7, 0xdfa4, 0x7bd5, 0xdf9e, 0x7bd4, 0xdf98,
  0x7bd2, 0xdf92, 0x7bd1, 0xdf8c, 0x7bcf, 0xdf86, 0x7bcd, 0xdf7f,
  0x7bcc, 0xdf79, 0x7bca, 0xdf73, 0x7bc9, 0xdf6d, 0x7bc7, 0xdf67,
  0x7bc5, 0xdf61, 0x7bc4, 0xdf5b, 0x7bc2, 0xdf55, 0x7bc1, 0xdf4f,
  0x7bbf, 0xdf49, 0x7bbd, 0xdf43, 0x7bbc, 0xdf3d, 0x7bba, 0xdf37,
  0x7bb9, 0xdf30, 0x7bb7, 0xdf2a, 0x7bb5, 0xdf24, 0x7bb4, 0xdf1e,
  0x7bb2, 0xdf18, 0x7bb0, 0xdf12, 0x7baf, 0xdf0c, 0x7bad, 0xdf06,
  0x7bac, 0xdf00, 0x7baa, 0xdefa, 0x7ba8, 0xdef4, 0x7ba7, 0xdeee,
  0x7ba5, 0xdee8, 0x7ba3, 0xdee2, 0x7ba2, 0xdedb, 0x7ba0, 0xded5,
  0x7b9f, 0xdecf, 0x7b9d, 0xdec9, 0x7b9b, 0xdec3, 0x7b9a, 0xdebd,
  0x7b98, 0xdeb7, 0x7b96, 0xdeb1, 0x7b95, 0xdeab, 0x7b93, 0xdea5,
  0x7b92, 0xde9f, 0x7b90, 0xde99, 0x7b8e, 0xde93, 0x7b8d, 0xde8d,
  0x7b8b, 0xde87, 0x7b89, 0xde80, 0x7b88, 0xde7a, 0x7b86, 0xde74,
  0x7b84, 0xde6e, 0x7b83, 0xde68, 0x7b81, 0xde62, 0x7b7f, 0xde5c,
  0x7b7e, 0xde56, 0x7b7c, 0xde50, 0x7b7a, 0xde4a, 0x7b79, 0xde44,
  0x7b77, 0xde3e, 0x7b76, 0xde38, 0x7b74, 0xde32, 0x7b72, 0xde2c,
  0x7b71, 0xde26, 0x7b6f, 0xde1f, 0x7b6d, 0xde19, 0x7b6c, 0xde13,
  0x7b6a, 0xde0d, 0x7b68, 0xde07, 0x7b67, 0xde01, 0x7b65, 0xddfb,
  0x7b63, 0xddf5, 0x7b62, 0xddef, 0x7b60, 0xdde9, 0x7b5e, 0xdde3,
  0x7b5d, 0xdddd, 0x7b5b, 0xddd7, 0x7b59, 0xddd1, 0x7b57, 0xddcb,
  0x7b56, 0xddc5, 0x7b54, 0xddbf, 0x7b52, 0xddb9, 0x7b51, 0xddb2,
  0x7b4f, 0xddac, 0x7b4d, 0xdda6, 0x7b4c, 0xdda0, 0x7b4a, 0xdd9a,
  0x7b48, 0xdd94, 0x7b47, 0xdd8e, 0x7b45, 0xdd88, 0x7b43, 0xdd82,
  0x7b42, 0xdd7c, 0x7b40, 0xdd76, 0x7b3e, 0xdd70, 0x7b3c, 0xdd6a,
  0x7b3b, 0xdd64, 0x7b39, 0xdd5e, 0x7b37, 0xdd58, 0x7b36, 0xdd52,
  0x7b34, 0xdd4c, 0x7b32, 0xdd46, 0x7b31, 0xdd40, 0x7b2f, 0xdd39,
  0x7b2d, 0xdd33, 0x7b2b, 0xdd2d, 0x7b2a, 0xdd27, 0x7b28, 0xdd21,
  0x7b26, 0xdd1b, 0x7b25, 0xdd15, 0x7b23, 0xdd0f, 0x7b21, 0xdd09,
  0x7b1f, 0xdd03, 0x7b1e, 0xdcfd, 0x7b1c, 0xdcf7, 0x7b1a, 0xdcf1,
  0x7b19, 0xdceb, 0x7b17, 0xdce5, 0x7b15, 0xdcdf, 0x7b13, 0xdcd9,
  0x7b12, 0xdcd3, 0x7b10, 0xdccd, 0x7b0e, 0xdcc7, 0x7b0c, 0xdcc1,
  0x7b0b, 0xdcbb, 0x7b09, 0xdcb5, 0x7b07, 0xdcae, 0x7b06, 0xdca8,
  0x7b04, 0xdca2, 0x7b02, 0xdc9c, 0x7b00, 0xdc96, 0x7aff, 0xdc90,
  0x7afd, 0xdc8a, 0x7afb, 0xdc84, 0x7af9, 0xdc7e, 0x7af8, 0xdc78,
  0x7af6, 0xdc72, 0x7af4, 0xdc6c, 0x7af2, 0xdc66, 0x7af1, 0xdc60,
  0x7aef, 0xdc5a, 0x7aed, 0xdc54, 0x7aeb, 0xdc4e, 0x7aea, 0xdc48,
  0x7ae8, 0xdc42, 0x7ae6, 0xdc3c, 0x7ae4, 0xdc36, 0x7ae3, 0xdc30,
  0x7ae1, 0xdc2a, 0x7adf, 0xdc24, 0x7add, 0xdc1e, 0x7adc, 0xdc18,
  0x7ada, 0xdc12, 0x7ad8, 0xdc0c, 0x7ad6, 0xdc06, 0x7ad5, 0xdbff,
  0x7ad3, 0xdbf9, 0x7ad1, 0xdbf3, 0x7acf, 0xdbed, 0x7acd, 0xdbe7,
  0x7acc, 0xdbe1, 0x7aca, 0xdbdb, 0x7ac8, 0xdbd5, 0x7ac6, 0xdbcf,
  0x7ac5, 0xdbc9, 0x7ac3, 0xdbc3, 0x7ac1, 0xdbbd, 0x7abf, 0xdbb7,
  0x7abd, 0xdbb1, 0x7abc, 0xdbab, 0x7aba, 0xdba5, 0x7ab8, 0xdb9f,
  0x7ab6, 0xdb99, 0x7ab5, 0xdb93, 0x7ab3, 0xdb8d, 0x7ab1, 0xdb87,
  0x7aaf, 0xdb81, 0x7aad, 0xdb7b, 0x7aac, 0xdb75, 0x7aaa, 0xdb6f,
  0x7aa8, 0xdb69, 0x7aa6, 0xdb63, 0x7aa4, 0xdb5d, 0x7aa3, 0xdb57,
  0x7aa1, 0xdb51, 0x7a9f, 0xdb4b, 0x7a9d, 0xdb45, 0x7a9b, 0xdb3f,
  0x7a9a, 0xdb39, 0x7a98, 0xdb33, 0x7a96, 0xdb2d, 0x7a94, 0xdb27,
  0x7a92, 0xdb21, 0x7a91, 0xdb1b, 0x7a8f, 0xdb15, 0x7a8d, 0xdb0f,
  0x7a8b, 0xdb09, 0x7a89, 0xdb03, 0x7a87, 0xdafd, 0x7a86, 0xdaf7,
  0x7a84, 0xdaf1, 0x7a82, 0xdaea, 0x7a80, 0xdae4, 0x7a7e, 0xdade,
  0x7a7d, 0xdad8, 0x7a7b, 0xdad2, 0x7a79, 0xdacc, 0x7a77, 0xdac6,
  0x7a75, 0xdac0, 0x7a73, 0xdaba, 0x7a72, 0xdab4, 0x7a70, 0xdaae,
  0x7a6e, 0xdaa8, 0x7a6c, 0xdaa2, 0x7a6a, 0xda9c, 0x7a68, 0xda96,
  0x7a67, 0xda90, 0x7a65, 0xda8a, 0x7a63, 0xda84, 0x7a61, 0xda7e,
  0x7a5f, 0xda78, 0x7a5d, 0xda72, 0x7a5c, 0xda6c, 0x7a5a, 0xda66,
  0x7a58, 0xda60, 0x7a56, 0xda5a, 0x7a54, 0xda54, 0x7a52, 0xda4e,
  0x7a50, 0xda48, 0x7a4f, 0xda42, 0x7a4d, 0xda3c, 0x7a4b, 0xda36,
  0x7a49, 0xda30, 0x7a47, 0xda2a, 0x7a45, 0xda24, 0x7a43, 0xda1e,
  0x7a42, 0xda18, 0x7a40, 0xda12, 0x7a3e, 0xda0c, 0x7a3c, 0xda06,
  0x7a3a, 0xda00, 0x7a38, 0xd9fa, 0x7a36, 0xd9f4, 0x7a35, 0xd9ee,
  0x7a33, 0xd9e8, 0x7a31, 0xd9e2, 0x7a2f, 0xd9dc, 0x7a2d, 0xd9d6,
  0x7a2b, 0xd9d0, 0x7a29, 0xd9ca, 0x7a27, 0xd9c4, 0x7a26, 0xd9be,
  0x7a24, 0xd9b8, 0x7a22, 0xd9b2, 0x7a20, 0xd9ac, 0x7a1e, 0xd9a6,
  0x7a1c, 0xd9a0, 0x7a1a, 0xd99a, 0x7a18, 0xd994, 0x7a16, 0xd98e,
  0x7a15, 0xd988, 0x7a13, 0xd982, 0x7a11, 0xd97c, 0x7a0f, 0xd976,
  0x7a0d, 0xd970, 0x7a0b, 0xd96a, 0x7a09, 0xd964, 0x7a07, 0xd95e,
  0x7a05, 0xd958, 0x7a04, 0xd952, 0x7a02, 0xd94c, 0x7a00, 0xd946,
  0x79fe, 0xd940, 0x79fc, 0xd93a, 0x79fa, 0xd934, 0x79f8, 0xd92e,
  0x79f6, 0xd928, 0x79f4, 0xd922, 0x79f2, 0xd91c, 0x79f0, 0xd917,
  0x79ef, 0xd911, 0x79ed, 0xd90b, 0x79eb, 0xd905, 0x79e9, 0xd8ff,
  0x79e7, 0xd8f9, 0x79e5, 0xd8f3, 0x79e3, 0xd8ed, 0x79e1, 0xd8e7,
  0x79df, 0xd8e1, 0x79dd, 0xd8db, 0x79db, 0xd8d5, 0x79d9, 0xd8cf,
  0x79d8, 0xd8c9, 0x79d6, 0xd8c3, 0x79d4, 0xd8bd, 0x79d2, 0xd8b7,
  0x79d0, 0xd8b1, 0x79ce, 0xd8ab, 0x79cc, 0xd8a5, 0x79ca, 0xd89f,
  0x79c8, 0xd899, 0x79c6, 0xd893, 0x79c4, 0xd88d, 0x79c2, 0xd887,
  0x79c0, 0xd881, 0x79be, 0xd87b, 0x79bc, 0xd875, 0x79bb, 0xd86f,
  0x79b9, 0xd869, 0x79b7, 0xd863, 0x79b5, 0xd85d, 0x79b3, 0xd857,
  0x79b1, 0xd851, 0x79af, 0xd84b, 0x79ad, 0xd845, 0x79ab, 0xd83f,
  0x79a9, 0xd839, 0x79a7, 0xd833, 0x79a5, 0xd82d, 0x79a3, 0xd827,
  0x79a1, 0xd821, 0x799f, 0xd81b, 0x799d, 0xd815, 0x799b, 0xd80f,
  0x7999, 0xd80a, 0x7997, 0xd804, 0x7995, 0xd7fe, 0x7993, 0xd7f8,
  0x7992, 0xd7f2, 0x7990, 0xd7ec, 0x798e, 0xd7e6, 0x798c, 0xd7e0,
  0x798a, 0xd7da, 0x7988, 0xd7d4, 0x7986, 0xd7ce, 0x7984, 0xd7c8,
  0x7982, 0xd7c2, 0x7980, 0xd7bc, 0x797e, 0xd7b6, 0x797c, 0xd7b0,
  0x797a, 0xd7aa, 0x7978, 0xd7a4, 0x7976, 0xd79e, 0x7974, 0xd798,
  0x7972, 0xd792, 0x7970, 0xd78c, 0x796e, 0xd786, 0x796c, 0xd780,
  0x796a, 0xd77a, 0x7968, 0xd774, 0x7966, 0xd76e, 0x7964, 0xd768,
  0x7962, 0xd763, 0x7960, 0xd75d, 0x795e, 0xd757, 0x795c, 0xd751,
  0x795a, 0xd74b, 0x7958, 0xd745, 0x7956, 0xd73f, 0x7954, 0xd739,
  0x7952, 0xd733, 0x7950, 0xd72d, 0x794e, 0xd727, 0x794c, 0xd721,
  0x794a, 0xd71b, 0x7948, 0xd715, 0x7946, 0xd70f, 0x7944, 0xd709,
  0x7942, 0xd703, 0x7940, 0xd6fd, 0x793e, 0xd6f7, 0x793c, 0xd6f1,
  0x793a, 0xd6eb, 0x7938, 0xd6e5, 0x7936, 0xd6e0, 0x7934, 0xd6da,
  0x7932, 0xd6d4, 0x7930, 0xd6ce, 0x792e, 0xd6c8, 0x792c, 0xd6c2,
  0x792a, 0xd6bc, 0x7928, 0xd6b6, 0x7926, 0xd6b0, 0x7924, 0xd6aa,
  0x7922, 0xd6a4, 0x7920, 0xd69e, 0x791e, 0xd698, 0x791c, 0xd692,
  0x7919, 0xd68c, 0x7917, 0xd686, 0x7915, 0xd680, 0x7913, 0xd67a,
  0x7911, 0xd675, 0x790f, 0xd66f, 0x790d, 0xd669, 0x790b, 0xd663,
  0x7909, 0xd65d, 0x7907, 0xd657, 0x7905, 0xd651, 0x7903, 0xd64b,
  0x7901, 0xd645, 0x78ff, 0xd63f, 0x78fd, 0xd639, 0x78fb, 0xd633,
  0x78f9, 0xd62d, 0x78f7, 0xd627, 0x78f5, 0xd621, 0x78f3, 0xd61b,
  0x78f1, 0xd615, 0x78ee, 0xd610, 0x78ec, 0xd60a, 0x78ea, 0xd604,
  0x78e8, 0xd5fe, 0x78e6, 0xd5f8, 0x78e4, 0xd5f2, 0x78e2, 0xd5ec,
  0x78e0, 0xd5e6, 0x78de, 0xd5e0, 0x78dc, 0xd5da, 0x78da, 0xd5d4,
  0x78d8, 0xd5ce, 0x78d6, 0xd5c8, 0x78d4, 0xd5c2, 0x78d2, 0xd5bc,
  0x78cf, 0xd5b7, 0x78cd, 0xd5b1, 0x78cb, 0xd5ab, 0x78c9, 0xd5a5,
  0x78c7, 0xd59f, 0x78c5, 0xd599, 0x78c3, 0xd593, 0x78c1, 0xd58d,
  0x78bf, 0xd587, 0x78bd, 0xd581, 0x78bb, 0xd57b, 0x78b9, 0xd575,
  0x78b6, 0xd56f, 0x78b4, 0xd569, 0x78b2, 0xd564, 0x78b0, 0xd55e,
  0x78ae, 0xd558, 0x78ac, 0xd552, 0x78aa, 0xd54c, 0x78a8, 0xd546,
  0x78a6, 0xd540, 0x78a4, 0xd53a, 0x78a2, 0xd534, 0x789f, 0xd52e,
  0x789d, 0xd528, 0x789b, 0xd522, 0x7899, 0xd51c, 0x7897, 0xd517,
  0x7895, 0xd511, 0x7893, 0xd50b, 0x7891, 0xd505, 0x788f, 0xd4ff,
  0x788c, 0xd4f9, 0x788a, 0xd4f3, 0x7888, 0xd4ed, 0x7886, 0xd4e7,
  0x7884, 0xd4e1, 0x7882, 0xd4db, 0x7880, 0xd4d5, 0x787e, 0xd4d0,
  0x787c, 0xd4ca, 0x7879, 0xd4c4, 0x7877, 0xd4be, 0x7875, 0xd4b8,
  0x7873, 0xd4b2, 0x7871, 0xd4ac, 0x786f, 0xd4a6, 0x786d, 0xd4a0,
  0x786b, 0xd49a, 0x7868, 0xd494, 0x7866, 0xd48f, 0x7864, 0xd489,
  0x7862, 0xd483, 0x7860, 0xd47d, 0x785e, 0xd477, 0x785c, 0xd471,
  0x7859, 0xd46b, 0x7857, 0xd465, 0x7855, 0xd45f, 0x7853, 0xd459,
  0x7851, 0xd453, 0x784f, 0xd44e, 0x784d, 0xd448, 0x784a, 0xd442,
  0x7848, 0xd43c, 0x7846, 0xd436, 0x7844, 0xd430, 0x7842, 0xd42a,
  0x7840, 0xd424, 0x783e, 0xd41e, 0x783b, 0xd418, 0x7839, 0xd412,
  0x7837, 0xd40d, 0x7835, 0xd407, 0x7833, 0xd401, 0x7831, 0xd3fb,
  0x782e, 0xd3f5, 0x782c, 0xd3ef, 0x782a, 0xd3e9, 0x7828, 0xd3e3,
  0x7826, 0xd3dd, 0x7824, 0xd3d7, 0x7821, 0xd3d2, 0x781f, 0xd3cc,
  0x781d, 0xd3c6, 0x781b, 0xd3c0, 0x7819, 0xd3ba, 0x7817, 0xd3b4,
  0x7814, 0xd3ae, 0x7812, 0xd3a8, 0x7810, 0xd3a2, 0x780e, 0xd39d,
  0x780c, 0xd397, 0x780a, 0xd391, 0x7807, 0xd38b, 0x7805, 0xd385,
  0x7803, 0xd37f, 0x7801, 0xd379, 0x77ff, 0xd373, 0x77fc, 0xd36d,
  0x77fa, 0xd368, 0x77f8, 0xd362, 0x77f6, 0xd35c, 0x77f4, 0xd356,
  0x77f1, 0xd350, 0x77ef, 0xd34a, 0x77ed, 0xd344, 0x77eb, 0xd33e,
  0x77e9, 0xd338, 0x77e6, 0xd333, 0x77e4, 0xd32d, 0x77e2, 0xd327,
  0x77e0, 0xd321, 0x77de, 0xd31b, 0x77db, 0xd315, 0x77d9, 0xd30f,
  0x77d7, 0xd309, 0x77d5, 0xd303, 0x77d3, 0xd2fe, 0x77d0, 0xd2f8,
  0x77ce, 0xd2f2, 0x77cc, 0xd2ec, 0x77ca, 0xd2e6, 0x77c8, 0xd2e0,
  0x77c5, 0xd2da, 0x77c3, 0xd2d4, 0x77c1, 0xd2cf, 0x77bf, 0xd2c9,
  0x77bc, 0xd2c3, 0x77ba, 0xd2bd, 0x77b8, 0xd2b7, 0x77b6, 0xd2b1,
  0x77b4, 0xd2ab, 0x77b1, 0xd2a5, 0x77af, 0xd2a0, 0x77ad, 0xd29a,
  0x77ab, 0xd294, 0x77a8, 0xd28e, 0x77a6, 0xd288, 0x77a4, 0xd282,
  0x77a2, 0xd27c, 0x77a0, 0xd276, 0x779d, 0xd271, 0x779b, 0xd26b,
  0x7799, 0xd265, 0x7797, 0xd25f, 0x7794, 0xd259, 0x7792, 0xd253,
  0x7790, 0xd24d, 0x778e, 0xd247, 0x778b, 0xd242, 0x7789, 0xd23c,
  0x7787, 0xd236, 0x7785, 0xd230, 0x7782, 0xd22a, 0x7780, 0xd224,
  0x777e, 0xd21e, 0x777c, 0xd219, 0x7779, 0xd213, 0x7777, 0xd20d,
  0x7775, 0xd207, 0x7773, 0xd201, 0x7770, 0xd1fb, 0x776e, 0xd1f5,
  0x776c, 0xd1ef, 0x776a, 0xd1ea, 0x7767, 0xd1e4, 0x7765, 0xd1de,
  0x7763, 0xd1d8, 0x7760, 0xd1d2, 0x775e, 0xd1cc, 0x775c, 0xd1c6,
  0x775a, 0xd1c1, 0x7757, 0xd1bb, 0x7755, 0xd1b5, 0x7753, 0xd1af,
  0x7751, 0xd1a9, 0x774e, 0xd1a3, 0x774c, 0xd19d, 0x774a, 0xd198,
  0x7747, 0xd192, 0x7745, 0xd18c, 0x7743, 0xd186, 0x7741, 0xd180,
  0x773e, 0xd17a, 0x773c, 0xd174, 0x773a, 0xd16f, 0x7738, 0xd169,
  0x7735, 0xd163, 0x7733, 0xd15d, 0x7731, 0xd157, 0x772e, 0xd151,
  0x772c, 0xd14b, 0x772a, 0xd146, 0x7727, 0xd140, 0x7725, 0xd13a,
  0x7723, 0xd134, 0x7721, 0xd12e, 0x771e, 0xd128, 0x771c, 0xd123,
  0x771a, 0xd11d, 0x7717, 0xd117, 0x7715, 0xd111, 0x7713, 0xd10b,
  0x7710, 0xd105, 0x770e, 0xd0ff, 0x770c, 0xd0fa, 0x770a, 0xd0f4,
  0x7707, 0xd0ee, 0x7705, 0xd0e8, 0x7703, 0xd0e2, 0x7700, 0xd0dc,
  0x76fe, 0xd0d7, 0x76fc, 0xd0d1, 0x76f9, 0xd0cb, 0x76f7, 0xd0c5,
  0x76f5, 0xd0bf, 0x76f2, 0xd0b9, 0x76f0, 0xd0b4, 0x76ee, 0xd0ae,
  0x76eb, 0xd0a8, 0x76e9, 0xd0a2, 0x76e7, 0xd09c, 0x76e4, 0xd096,
  0x76e2, 0xd091, 0x76e0, 0xd08b, 0x76dd, 0xd085, 0x76db, 0xd07f,
  0x76d9, 0xd079, 0x76d6, 0xd073, 0x76d4, 0xd06e, 0x76d2, 0xd068,
  0x76cf, 0xd062, 0x76cd, 0xd05c, 0x76cb, 0xd056, 0x76c8, 0xd050,
  0x76c6, 0xd04b, 0x76c4, 0xd045, 0x76c1, 0xd03f, 0x76bf, 0xd039,
  0x76bd, 0xd033, 0x76ba, 0xd02d, 0x76b8, 0xd028, 0x76b6, 0xd022,
  0x76b3, 0xd01c, 0x76b1, 0xd016, 0x76af, 0xd010, 0x76ac, 0xd00a,
  0x76aa, 0xd005, 0x76a8, 0xcfff, 0x76a5, 0xcff9, 0x76a3, 0xcff3,
  0x76a0, 0xcfed, 0x769e, 0xcfe7, 0x769c, 0xcfe2, 0x7699, 0xcfdc,
  0x7697, 0xcfd6, 0x7695, 0xcfd0, 0x7692, 0xcfca, 0x7690, 0xcfc5,
  0x768e, 0xcfbf, 0x768b, 0xcfb9, 0x7689, 0xcfb3, 0x7686, 0xcfad,
  0x7684, 0xcfa7, 0x7682, 0xcfa2, 0x767f, 0xcf9c, 0x767d, 0xcf96,
  0x767b, 0xcf90, 0x7678, 0xcf8a, 0x7676, 0xcf85, 0x7673, 0xcf7f,
  0x7671, 0xcf79, 0x766f, 0xcf73, 0x766c, 0xcf6d, 0x766a, 0xcf67,
  0x7668, 0xcf62, 0x7665, 0xcf5c, 0x7663, 0xcf56, 0x7660, 0xcf50,
  0x765e, 0xcf4a, 0x765c, 0xcf45, 0x7659, 0xcf3f, 0x7657, 0xcf39,
  0x7654, 0xcf33, 0x7652, 0xcf2d, 0x7650, 0xcf28, 0x764d, 0xcf22,
  0x764b, 0xcf1c, 0x7648, 0xcf16, 0x7646, 0xcf10, 0x7644, 0xcf0b,
  0x7641, 0xcf05, 0x763f, 0xceff, 0x763c, 0xcef9, 0x763a, 0xcef3,
  0x7638, 0xceee, 0x7635, 0xcee8, 0x7633, 0xcee2, 0x7630, 0xcedc,
  0x762e, 0xced6, 0x762b, 0xced1, 0x7629, 0xcecb, 0x7627, 0xcec5,
  0x7624, 0xcebf, 0x7622, 0xceb9, 0x761f, 0xceb4, 0x761d, 0xceae,
  0x761b, 0xcea8, 0x7618, 0xcea2, 0x7616, 0xce9c, 0x7613, 0xce97,
  0x7611, 0xce91, 0x760e, 0xce8b, 0x760c, 0xce85, 0x760a, 0xce7f,
  0x7607, 0xce7a, 0x7605, 0xce74, 0x7602, 0xce6e, 0x7600, 0xce68,
  0x75fd, 0xce62, 0x75fb, 0xce5d, 0x75f9, 0xce57, 0x75f6, 0xce51,
  0x75f4, 0xce4b, 0x75f1, 0xce45, 0x75ef, 0xce40, 0x75ec, 0xce3a,
  0x75ea, 0xce34, 0x75e7, 0xce2e, 0x75e5, 0xce28, 0x75e3, 0xce23,
  0x75e0, 0xce1d, 0x75de, 0xce17, 0x75db, 0xce11, 0x75d9, 0xce0c,
  0x75d6, 0xce06, 0x75d4, 0xce00, 0x75d1, 0xcdfa, 0x75cf, 0xcdf4,
  0x75cc, 0xcdef, 0x75ca, 0xcde9, 0x75c8, 0xcde3, 0x75c5, 0xcddd,
  0x75c3, 0xcdd8, 0x75c0, 0xcdd2, 0x75be, 0xcdcc, 0x75bb, 0xcdc6,
  0x75b9, 0xcdc0, 0x75b6, 0xcdbb, 0x75b4, 0xcdb5, 0x75b1, 0xcdaf,
  0x75af, 0xcda9, 0x75ac, 0xcda3, 0x75aa, 0xcd9e, 0x75a7, 0xcd98,
  0x75a5, 0xcd92, 0x75a3, 0xcd8c, 0x75a0, 0xcd87, 0x759e, 0xcd81,
  0x759b, 0xcd7b, 0x7599, 0xcd75, 0x7596, 0xcd70, 0x7594, 0xcd6a,
  0x7591, 0xcd64, 0x758f, 0xcd5e, 0x758c, 0xcd58, 0x758a, 0xcd53,
  0x7587, 0xcd4d, 0x7585, 0xcd47, 0x7582, 0xcd41, 0x7580, 0xcd3c,
  0x757d, 0xcd36, 0x757b, 0xcd30, 0x7578, 0xcd2a, 0x7576, 0xcd25,
  0x7573, 0xcd1f, 0x7571, 0xcd19, 0x756e, 0xcd13, 0x756c, 0xcd0d,
  0x7569, 0xcd08, 0x7567, 0xcd02, 0x7564, 0xccfc, 0x7562, 0xccf6,
  0x755f, 0xccf1, 0x755d, 0xcceb, 0x755a, 0xcce5, 0x7558, 0xccdf,
  0x7555, 0xccda, 0x7553, 0xccd4, 0x7550, 0xccce, 0x754e, 0xccc8,
  0x754b, 0xccc3, 0x7549, 0xccbd, 0x7546, 0xccb7, 0x7544, 0xccb1,
  0x7541, 0xccac, 0x753f, 0xcca6, 0x753c, 0xcca0, 0x753a, 0xcc9a,
  0x7537, 0xcc95, 0x7535, 0xcc8f, 0x7532, 0xcc89, 0x752f, 0xcc83,
  0x752d, 0xcc7e, 0x752a, 0xcc78, 0x7528, 0xcc72, 0x7525, 0xcc6c,
  0x7523, 0xcc67, 0x7520, 0xcc61, 0x751e, 0xcc5b, 0x751b, 0xcc55,
  0x7519, 0xcc50, 0x7516, 0xcc4a, 0x7514, 0xcc44, 0x7511, 0xcc3e,
  0x750f, 0xcc39, 0x750c, 0xcc33, 0x7509, 0xcc2d, 0x7507, 0xcc27,
  0x7504, 0xcc22, 0x7502, 0xcc1c, 0x74ff, 0xcc16, 0x74fd, 0xcc10,
  0x74fa, 0xcc0b, 0x74f8, 0xcc05, 0x74f5, 0xcbff, 0x74f2, 0xcbf9,
  0x74f0, 0xcbf4, 0x74ed, 0xcbee, 0x74eb, 0xcbe8, 0x74e8, 0xcbe2,
  0x74e6, 0xcbdd, 0x74e3, 0xcbd7, 0x74e1, 0xcbd1, 0x74de, 0xcbcb,
  0x74db, 0xcbc6, 0x74d9, 0xcbc0, 0x74d6, 0xcbba, 0x74d4, 0xcbb5,
  0x74d1, 0xcbaf, 0x74cf, 0xcba9, 0x74cc, 0xcba3, 0x74c9, 0xcb9e,
  0x74c7, 0xcb98, 0x74c4, 0xcb92, 0x74c2, 0xcb8c, 0x74bf, 0xcb87,
  0x74bd, 0xcb81, 0x74ba, 0xcb7b, 0x74b7, 0xcb75, 0x74b5, 0xcb70,
  0x74b2, 0xcb6a, 0x74b0, 0xcb64, 0x74ad, 0xcb5f, 0x74ab, 0xcb59,
  0x74a8, 0xcb53, 0x74a5, 0xcb4d, 0x74a3, 0xcb48, 0x74a0, 0xcb42,
  0x749e, 0xcb3c, 0x749b, 0xcb36, 0x7498, 0xcb31, 0x7496, 0xcb2b,
  0x7493, 0xcb25, 0x7491, 0xcb20, 0x748e, 0xcb1a, 0x748b, 0xcb14,
  0x7489, 0xcb0e, 0x7486, 0xcb09, 0x7484, 0xcb03, 0x7481, 0xcafd,
  0x747e, 0xcaf8, 0x747c, 0xcaf2, 0x7479, 0xcaec, 0x7477, 0xcae6,
  0x7474, 0xcae1, 0x7471, 0xcadb, 0x746f, 0xcad5, 0x746c, 0xcad0,
  0x746a, 0xcaca, 0x7467, 0xcac4, 0x7464, 0xcabe, 0x7462, 0xcab9,
  0x745f, 0xcab3, 0x745c, 0xcaad, 0x745a, 0xcaa8, 0x7457, 0xcaa2,
  0x7455, 0xca9c, 0x7452, 0xca96, 0x744f, 0xca91, 0x744d, 0xca8b,
  0x744a, 0xca85, 0x7448, 0xca80, 0x7445, 0xca7a, 0x7442, 0xca74,
  0x7440, 0xca6e, 0x743d, 0xca69, 0x743a, 0xca63, 0x7438, 0xca5d,
  0x7435, 0xca58, 0x7432, 0xca52, 0x7430, 0xca4c, 0x742d, 0xca46,
  0x742b, 0xca41, 0x7428, 0xca3b, 0x7425, 0xca35, 0x7423, 0xca30,
  0x7420, 0xca2a, 0x741d, 0xca24, 0x741b, 0xca1f, 0x7418, 0xca19,
  0x7415, 0xca13, 0x7413, 0xca0d, 0x7410, 0xca08, 0x740d, 0xca02,
  0x740b, 0xc9fc, 0x7408, 0xc9f7, 0x7406, 0xc9f1, 0x7403, 0xc9eb,
  0x7400, 0xc9e6, 0x73fe, 0xc9e0, 0x73fb, 0xc9da, 0x73f8, 0xc9d5,
  0x73f6, 0xc9cf, 0x73f3, 0xc9c9, 0x73f0, 0xc9c3, 0x73ee, 0xc9be,
  0x73eb, 0xc9b8, 0x73e8, 0xc9b2, 0x73e6, 0xc9ad, 0x73e3, 0xc9a7,
  0x73e0, 0xc9a1, 0x73de, 0xc99c, 0x73db, 0xc996, 0x73d8, 0xc990,
  0x73d6, 0xc98b, 0x73d3, 0xc985, 0x73d0, 0xc97f, 0x73ce, 0xc97a,
  0x73cb, 0xc974, 0x73c8, 0xc96e, 0x73c6, 0xc968, 0x73c3, 0xc963,
  0x73c0, 0xc95d, 0x73bd, 0xc957, 0x73bb, 0xc952, 0x73b8, 0xc94c,
  0x73b5, 0xc946, 0x73b3, 0xc941, 0x73b0, 0xc93b, 0x73ad, 0xc935,
  0x73ab, 0xc930, 0x73a8, 0xc92a, 0x73a5, 0xc924, 0x73a3, 0xc91f,
  0x73a0, 0xc919, 0x739d, 0xc913, 0x739b, 0xc90e, 0x7398, 0xc908,
  0x7395, 0xc902, 0x7392, 0xc8fd, 0x7390, 0xc8f7, 0x738d, 0xc8f1,
  0x738a, 0xc8ec, 0x7388, 0xc8e6, 0x7385, 0xc8e0, 0x7382, 0xc8db,
  0x737f, 0xc8d5, 0x737d, 0xc8cf, 0x737a, 0xc8ca, 0x7377, 0xc8c4,
  0x7375, 0xc8be, 0x7372, 0xc8b9, 0x736f, 0xc8b3, 0x736c, 0xc8ad,
  0x736a, 0xc8a8, 0x7367, 0xc8a2, 0x7364, 0xc89c, 0x7362, 0xc897,
  0x735f, 0xc891, 0x735c, 0xc88b, 0x7359, 0xc886, 0x7357, 0xc880,
  0x7354, 0xc87a, 0x7351, 0xc875, 0x734f, 0xc86f, 0x734c, 0xc869,
  0x7349, 0xc864, 0x7346, 0xc85e, 0x7344, 0xc858, 0x7341, 0xc853,
  0x733e, 0xc84d, 0x733b, 0xc847, 0x7339, 0xc842, 0x7336, 0xc83c,
  0x7333, 0xc836, 0x7330, 0xc831, 0x732e, 0xc82b, 0x732b, 0xc825,
  0x7328, 0xc820, 0x7326, 0xc81a, 0x7323, 0xc814, 0x7320, 0xc80f,
  0x731d, 0xc809, 0x731b, 0xc803, 0x7318, 0xc7fe, 0x7315, 0xc7f8,
  0x7312, 0xc7f3, 0x7310, 0xc7ed, 0x730d, 0xc7e7, 0x730a, 0xc7e2,
  0x7307, 0xc7dc, 0x7305, 0xc7d6, 0x7302, 0xc7d1, 0x72ff, 0xc7cb,
  0x72fc, 0xc7c5, 0x72f9, 0xc7c0, 0x72f7, 0xc7ba, 0x72f4, 0xc7b4,
  0x72f1, 0xc7af, 0x72ee, 0xc7a9, 0x72ec, 0xc7a3, 0x72e9, 0xc79e,
  0x72e6, 0xc798, 0x72e3, 0xc793, 0x72e1, 0xc78d, 0x72de, 0xc787,
  0x72db, 0xc782, 0x72d8, 0xc77c, 0x72d5, 0xc776, 0x72d3, 0xc771,
  0x72d0, 0xc76b, 0x72cd, 0xc765, 0x72ca, 0xc760, 0x72c8, 0xc75a,
  0x72c5, 0xc755, 0x72c2, 0xc74f, 0x72bf, 0xc749, 0x72bc, 0xc744,
  0x72ba, 0xc73e, 0x72b7, 0xc738, 0x72b4, 0xc733, 0x72b1, 0xc72d,
  0x72af, 0xc728, 0x72ac, 0xc722, 0x72a9, 0xc71c, 0x72a6, 0xc717,
  0x72a3, 0xc711, 0x72a1, 0xc70b, 0x729e, 0xc706, 0x729b, 0xc700,
  0x7298, 0xc6fa, 0x7295, 0xc6f5, 0x7293, 0xc6ef, 0x7290, 0xc6ea,
  0x728d, 0xc6e4, 0x728a, 0xc6de, 0x7287, 0xc6d9, 0x7285, 0xc6d3,
  0x7282, 0xc6ce, 0x727f, 0xc6c8, 0x727c, 0xc6c2, 0x7279, 0xc6bd,
  0x7276, 0xc6b7, 0x7274, 0xc6b1, 0x7271, 0xc6ac, 0x726e, 0xc6a6,
  0x726b, 0xc6a1, 0x7268, 0xc69b, 0x7266, 0xc695, 0x7263, 0xc690,
  0x7260, 0xc68a, 0x725d, 0xc684, 0x725a, 0xc67f, 0x7257, 0xc679,
  0x7255, 0xc674, 0x7252, 0xc66e, 0x724f, 0xc668, 0x724c, 0xc663,
  0x7249, 0xc65d, 0x7247, 0xc658, 0x7244, 0xc652, 0x7241, 0xc64c,
  0x723e, 0xc647, 0x723b, 0xc641, 0x7238, 0xc63c, 0x7236, 0xc636,
  0x7233, 0xc630, 0x7230, 0xc62b, 0x722d, 0xc625, 0x722a, 0xc620,
  0x7227, 0xc61a, 0x7224, 0xc614, 0x7222, 0xc60f, 0x721f, 0xc609,
  0x721c, 0xc603, 0x7219, 0xc5fe, 0x7216, 0xc5f8, 0x7213, 0xc5f3,
  0x7211, 0xc5ed, 0x720e, 0xc5e7, 0x720b, 0xc5e2, 0x7208, 0xc5dc,
  0x7205, 0xc5d7, 0x7202, 0xc5d1, 0x71ff, 0xc5cc, 0x71fd, 0xc5c6,
  0x71fa, 0xc5c0, 0x71f7, 0xc5bb, 0x71f4, 0xc5b5, 0x71f1, 0xc5b0,
  0x71ee, 0xc5aa, 0x71eb, 0xc5a4, 0x71e9, 0xc59f, 0x71e6, 0xc599,
  0x71e3, 0xc594, 0x71e0, 0xc58e, 0x71dd, 0xc588, 0x71da, 0xc583,
  0x71d7, 0xc57d, 0x71d4, 0xc578, 0x71d2, 0xc572, 0x71cf, 0xc56c,
  0x71cc, 0xc567, 0x71c9, 0xc561, 0x71c6, 0xc55c, 0x71c3, 0xc556,
  0x71c0, 0xc551, 0x71bd, 0xc54b, 0x71bb, 0xc545, 0x71b8, 0xc540,
  0x71b5, 0xc53a, 0x71b2, 0xc535, 0x71af, 0xc52f, 0x71ac, 0xc529,
  0x71a9, 0xc524, 0x71a6, 0xc51e, 0x71a3, 0xc519, 0x71a1, 0xc513,
  0x719e, 0xc50e, 0x719b, 0xc508, 0x7198, 0xc502, 0x7195, 0xc4fd,
  0x7192, 0xc4f7, 0x718f, 0xc4f2, 0x718c, 0xc4ec, 0x7189, 0xc4e7,
  0x7186, 0xc4e1, 0x7184, 0xc4db, 0x7181, 0xc4d6, 0x717e, 0xc4d0,
  0x717b, 0xc4cb, 0x7178, 0xc4c5, 0x7175, 0xc4c0, 0x7172, 0xc4ba,
  0x716f, 0xc4b4, 0x716c, 0xc4af, 0x7169, 0xc4a9, 0x7167, 0xc4a4,
  0x7164, 0xc49e, 0x7161, 0xc499, 0x715e, 0xc493, 0x715b, 0xc48d,
  0x7158, 0xc488, 0x7155, 0xc482, 0x7152, 0xc47d, 0x714f, 0xc477,
  0x714c, 0xc472, 0x7149, 0xc46c, 0x7146, 0xc467, 0x7143, 0xc461,
  0x7141, 0xc45b, 0x713e, 0xc456, 0x713b, 0xc450, 0x7138, 0xc44b,
  0x7135, 0xc445, 0x7132, 0xc440, 0x712f, 0xc43a, 0x712c, 0xc434,
  0x7129, 0xc42f, 0x7126, 0xc429, 0x7123, 0xc424, 0x7120, 0xc41e,
  0x711d, 0xc419, 0x711a, 0xc413, 0x7117, 0xc40e, 0x7114, 0xc408,
  0x7112, 0xc403, 0x710f, 0xc3fd, 0x710c, 0xc3f7, 0x7109, 0xc3f2,
  0x7106, 0xc3ec, 0x7103, 0xc3e7, 0x7100, 0xc3e1, 0x70fd, 0xc3dc,
  0x70fa, 0xc3d6, 0x70f7, 0xc3d1, 0x70f4, 0xc3cb, 0x70f1, 0xc3c5,
  0x70ee, 0xc3c0, 0x70eb, 0xc3ba, 0x70e8, 0xc3b5, 0x70e5, 0xc3af,
  0x70e2, 0xc3aa, 0x70df, 0xc3a4, 0x70dc, 0xc39f, 0x70d9, 0xc399,
  0x70d6, 0xc394, 0x70d3, 0xc38e, 0x70d1, 0xc389, 0x70ce, 0xc383,
  0x70cb, 0xc37d, 0x70c8, 0xc378, 0x70c5, 0xc372, 0x70c2, 0xc36d,
  0x70bf, 0xc367, 0x70bc, 0xc362, 0x70b9, 0xc35c, 0x70b6, 0xc357,
  0x70b3, 0xc351, 0x70b0, 0xc34c, 0x70ad, 0xc346, 0x70aa, 0xc341,
  0x70a7, 0xc33b, 0x70a4, 0xc336, 0x70a1, 0xc330, 0x709e, 0xc32a,
  0x709b, 0xc325, 0x7098, 0xc31f, 0x7095, 0xc31a, 0x7092, 0xc314,
  0x708f, 0xc30f, 0x708c, 0xc309, 0x7089, 0xc304, 0x7086, 0xc2fe,
  0x7083, 0xc2f9, 0x7080, 0xc2f3, 0x707d, 0xc2ee, 0x707a, 0xc2e8,
  0x7077, 0xc2e3, 0x7074, 0xc2dd, 0x7071, 0xc2d8, 0x706e, 0xc2d2,
  0x706b, 0xc2cd, 0x7068, 0xc2c7, 0x7065, 0xc2c2, 0x7062, 0xc2bc,
  0x705f, 0xc2b7, 0x705c, 0xc2b1, 0x7059, 0xc2ab, 0x7056, 0xc2a6,
  0x7053, 0xc2a0, 0x7050, 0xc29b, 0x704d, 0xc295, 0x704a, 0xc290,
  0x7047, 0xc28a, 0x7044, 0xc285, 0x7041, 0xc27f, 0x703e, 0xc27a,
  0x703b, 0xc274, 0x7038, 0xc26f, 0x7035, 0xc269, 0x7032, 0xc264,
  0x702f, 0xc25e, 0x702c, 0xc259, 0x7029, 0xc253, 0x7026, 0xc24e,
  0x7023, 0xc248, 0x7020, 0xc243, 0x701d, 0xc23d, 0x7019, 0xc238,
  0x7016, 0xc232, 0x7013, 0xc22d, 0x7010, 0xc227, 0x700d, 0xc222,
  0x700a, 0xc21c, 0x7007, 0xc217, 0x7004, 0xc211, 0x7001, 0xc20c,
  0x6ffe, 0xc206, 0x6ffb, 0xc201, 0x6ff8, 0xc1fb, 0x6ff5, 0xc1f6,
  0x6ff2, 0xc1f0, 0x6fef, 0xc1eb, 0x6fec, 0xc1e5, 0x6fe9, 0xc1e0,
  0x6fe6, 0xc1da, 0x6fe3, 0xc1d5, 0x6fe0, 0xc1cf, 0x6fdd, 0xc1ca,
  0x6fda, 0xc1c4, 0x6fd6, 0xc1bf, 0x6fd3, 0xc1b9, 0x6fd0, 0xc1b4,
  0x6fcd, 0xc1ae, 0x6fca, 0xc1a9, 0x6fc7, 0xc1a3, 0x6fc4, 0xc19e,
  0x6fc1, 0xc198, 0x6fbe, 0xc193, 0x6fbb, 0xc18d, 0x6fb8, 0xc188,
  0x6fb5, 0xc183, 0x6fb2, 0xc17d, 0x6faf, 0xc178, 0x6fac, 0xc172,
  0x6fa9, 0xc16d, 0x6fa5, 0xc167, 0x6fa2, 0xc162, 0x6f9f, 0xc15c,
  0x6f9c, 0xc157, 0x6f99, 0xc151, 0x6f96, 0xc14c, 0x6f93, 0xc146,
  0x6f90, 0xc141, 0x6f8d, 0xc13b, 0x6f8a, 0xc136, 0x6f87, 0xc130,
  0x6f84, 0xc12b, 0x6f81, 0xc125, 0x6f7d, 0xc120, 0x6f7a, 0xc11a,
  0x6f77, 0xc115, 0x6f74, 0xc10f, 0x6f71, 0xc10a, 0x6f6e, 0xc105,
  0x6f6b, 0xc0ff, 0x6f68, 0xc0fa, 0x6f65, 0xc0f4, 0x6f62, 0xc0ef,
  0x6f5f, 0xc0e9, 0x6f5b, 0xc0e4, 0x6f58, 0xc0de, 0x6f55, 0xc0d9,
  0x6f52, 0xc0d3, 0x6f4f, 0xc0ce, 0x6f4c, 0xc0c8, 0x6f49, 0xc0c3,
  0x6f46, 0xc0bd, 0x6f43, 0xc0b8, 0x6f3f, 0xc0b3, 0x6f3c, 0xc0ad,
  0x6f39, 0xc0a8, 0x6f36, 0xc0a2, 0x6f33, 0xc09d, 0x6f30, 0xc097,
  0x6f2d, 0xc092, 0x6f2a, 0xc08c, 0x6f27, 0xc087, 0x6f23, 0xc081,
  0x6f20, 0xc07c, 0x6f1d, 0xc077, 0x6f1a, 0xc071, 0x6f17, 0xc06c,
  0x6f14, 0xc066, 0x6f11, 0xc061, 0x6f0e, 0xc05b, 0x6f0b, 0xc056,
  0x6f07, 0xc050, 0x6f04, 0xc04b, 0x6f01, 0xc045, 0x6efe, 0xc040,
  0x6efb, 0xc03b, 0x6ef8, 0xc035, 0x6ef5, 0xc030, 0x6ef1, 0xc02a,
  0x6eee, 0xc025, 0x6eeb, 0xc01f, 0x6ee8, 0xc01a, 0x6ee5, 0xc014,
  0x6ee2, 0xc00f, 0x6edf, 0xc00a, 0x6edc, 0xc004, 0x6ed8, 0xbfff,
  0x6ed5, 0xbff9, 0x6ed2, 0xbff4, 0x6ecf, 0xbfee, 0x6ecc, 0xbfe9,
  0x6ec9, 0xbfe3, 0x6ec6, 0xbfde, 0x6ec2, 0xbfd9, 0x6ebf, 0xbfd3,
  0x6ebc, 0xbfce, 0x6eb9, 0xbfc8, 0x6eb6, 0xbfc3, 0x6eb3, 0xbfbd,
  0x6eaf, 0xbfb8, 0x6eac, 0xbfb3, 0x6ea9, 0xbfad, 0x6ea6, 0xbfa8,
  0x6ea3, 0xbfa2, 0x6ea0, 0xbf9d, 0x6e9c, 0xbf97, 0x6e99, 0xbf92,
  0x6e96, 0xbf8d, 0x6e93, 0xbf87, 0x6e90, 0xbf82, 0x6e8d, 0xbf7c,
  0x6e89, 0xbf77, 0x6e86, 0xbf71, 0x6e83, 0xbf6c, 0x6e80, 0xbf67,
  0x6e7d, 0xbf61, 0x6e7a, 0xbf5c, 0x6e76, 0xbf56, 0x6e73, 0xbf51,
  0x6e70, 0xbf4b, 0x6e6d, 0xbf46, 0x6e6a, 0xbf41, 0x6e67, 0xbf3b,
  0x6e63, 0xbf36, 0x6e60, 0xbf30, 0x6e5d, 0xbf2b, 0x6e5a, 0xbf26,
  0x6e57, 0xbf20, 0x6e53, 0xbf1b, 0x6e50, 0xbf15, 0x6e4d, 0xbf10,
  0x6e4a, 0xbf0a, 0x6e47, 0xbf05, 0x6e44, 0xbf00, 0x6e40, 0xbefa,
  0x6e3d, 0xbef5, 0x6e3a, 0xbeef, 0x6e37, 0xbeea, 0x6e34, 0xbee5,
  0x6e30, 0xbedf, 0x6e2d, 0xbeda, 0x6e2a, 0xbed4, 0x6e27, 0xbecf,
  0x6e24, 0xbeca, 0x6e20, 0xbec4, 0x6e1d, 0xbebf, 0x6e1a, 0xbeb9,
  0x6e17, 0xbeb4, 0x6e14, 0xbeae, 0x6e10, 0xbea9, 0x6e0d, 0xbea4,
  0x6e0a, 0xbe9e, 0x6e07, 0xbe99, 0x6e04, 0xbe93, 0x6e00, 0xbe8e,
  0x6dfd, 0xbe89, 0x6dfa, 0xbe83, 0x6df7, 0xbe7e, 0x6df3, 0xbe78,
  0x6df0, 0xbe73, 0x6ded, 0xbe6e, 0x6dea, 0xbe68, 0x6de7, 0xbe63,
  0x6de3, 0xbe5e, 0x6de0, 0xbe58, 0x6ddd, 0xbe53, 0x6dda, 0xbe4d,
  0x6dd6, 0xbe48, 0x6dd3, 0xbe43, 0x6dd0, 0xbe3d, 0x6dcd, 0xbe38,
  0x6dca, 0xbe32, 0x6dc6, 0xbe2d, 0x6dc3, 0xbe28, 0x6dc0, 0xbe22,
  0x6dbd, 0xbe1d, 0x6db9, 0xbe17, 0x6db6, 0xbe12, 0x6db3, 0xbe0d,
  0x6db0, 0xbe07, 0x6dac, 0xbe02, 0x6da9, 0xbdfd, 0x6da6, 0xbdf7,
  0x6da3, 0xbdf2, 0x6d9f, 0xbdec, 0x6d9c, 0xbde7, 0x6d99, 0xbde2,
  0x6d96, 0xbddc, 0x6d92, 0xbdd7, 0x6d8f, 0xbdd1, 0x6d8c, 0xbdcc,
  0x6d89, 0xbdc7, 0x6d85, 0xbdc1, 0x6d82, 0xbdbc, 0x6d7f, 0xbdb7,
  0x6d7c, 0xbdb1, 0x6d78, 0xbdac, 0x6d75, 0xbda6, 0x6d72, 0xbda1,
  0x6d6f, 0xbd9c, 0x6d6b, 0xbd96, 0x6d68, 0xbd91, 0x6d65, 0xbd8c,
  0x6d62, 0xbd86, 0x6d5e, 0xbd81, 0x6d5b, 0xbd7c, 0x6d58, 0xbd76,
  0x6d55, 0xbd71, 0x6d51, 0xbd6b, 0x6d4e, 0xbd66, 0x6d4b, 0xbd61,
  0x6d48, 0xbd5b, 0x6d44, 0xbd56, 0x6d41, 0xbd51, 0x6d3e, 0xbd4b,
  0x6d3a, 0xbd46, 0x6d37, 0xbd40, 0x6d34, 0xbd3b, 0x6d31, 0xbd36,
  0x6d2d, 0xbd30, 0x6d2a, 0xbd2b, 0x6d27, 0xbd26, 0x6d23, 0xbd20,
  0x6d20, 0xbd1b, 0x6d1d, 0xbd16, 0x6d1a, 0xbd10, 0x6d16, 0xbd0b,
  0x6d13, 0xbd06, 0x6d10, 0xbd00, 0x6d0c, 0xbcfb, 0x6d09, 0xbcf5,
  0x6d06, 0xbcf0, 0x6d03, 0xbceb, 0x6cff, 0xbce5, 0x6cfc, 0xbce0,
  0x6cf9, 0xbcdb, 0x6cf5, 0xbcd5, 0x6cf2, 0xbcd0, 0x6cef, 0xbccb,
  0x6cec, 0xbcc5, 0x6ce8, 0xbcc0, 0x6ce5, 0xbcbb, 0x6ce2, 0xbcb5,
  0x6cde, 0xbcb0, 0x6cdb, 0xbcab, 0x6cd8, 0xbca5, 0x6cd4, 0xbca0,
  0x6cd1, 0xbc9b, 0x6cce, 0xbc95, 0x6cca, 0xbc90, 0x6cc7, 0xbc8b,
  0x6cc4, 0xbc85, 0x6cc1, 0xbc80, 0x6cbd, 0xbc7b, 0x6cba, 0xbc75,
  0x6cb7, 0xbc70, 0x6cb3, 0xbc6b, 0x6cb0, 0xbc65, 0x6cad, 0xbc60,
  0x6ca9, 0xbc5b, 0x6ca6, 0xbc55, 0x6ca3, 0xbc50, 0x6c9f, 0xbc4b,
  0x6c9c, 0xbc45, 0x6c99, 0xbc40, 0x6c95, 0xbc3b, 0x6c92, 0xbc35,
  0x6c8f, 0xbc30, 0x6c8b, 0xbc2b, 0x6c88, 0xbc25, 0x6c85, 0xbc20,
  0x6c81, 0xbc1b, 0x6c7e, 0xbc15, 0x6c7b, 0xbc10, 0x6c77, 0xbc0b,
  0x6c74, 0xbc05, 0x6c71, 0xbc00, 0x6c6d, 0xbbfb, 0x6c6a, 0xbbf5,
  0x6c67, 0xbbf0, 0x6c63, 0xbbeb, 0x6c60, 0xbbe5, 0x6c5d, 0xbbe0,
  0x6c59, 0xbbdb, 0x6c56, 0xbbd5, 0x6c53, 0xbbd0, 0x6c4f, 0xbbcb,
  0x6c4c, 0xbbc5, 0x6c49, 0xbbc0, 0x6c45, 0xbbbb, 0x6c42, 0xbbb5,
  0x6c3f, 0xbbb0, 0x6c3b, 0xbbab, 0x6c38, 0xbba6, 0x6c34, 0xbba0,
  0x6c31, 0xbb9b, 0x6c2e, 0xbb96, 0x6c2a, 0xbb90, 0x6c27, 0xbb8b,
  0x6c24, 0xbb86, 0x6c20, 0xbb80, 0x6c1d, 0xbb7b, 0x6c1a, 0xbb76,
  0x6c16, 0xbb70, 0x6c13, 0xbb6b, 0x6c0f, 0xbb66, 0x6c0c, 0xbb61,
  0x6c09, 0xbb5b, 0x6c05, 0xbb56, 0x6c02, 0xbb51, 0x6bff, 0xbb4b,
  0x6bfb, 0xbb46, 0x6bf8, 0xbb41, 0x6bf5, 0xbb3b, 0x6bf1, 0xbb36,
  0x6bee, 0xbb31, 0x6bea, 0xbb2c, 0x6be7, 0xbb26, 0x6be4, 0xbb21,
  0x6be0, 0xbb1c, 0x6bdd, 0xbb16, 0x6bd9, 0xbb11, 0x6bd6, 0xbb0c,
  0x6bd3, 0xbb06, 0x6bcf, 0xbb01, 0x6bcc, 0xbafc, 0x6bc9, 0xbaf7,
  0x6bc5, 0xbaf1, 0x6bc2, 0xbaec, 0x6bbe, 0xbae7, 0x6bbb, 0xbae1,
  0x6bb8, 0xbadc, 0x6bb4, 0xbad7, 0x6bb1, 0xbad2, 0x6bad, 0xbacc,
  0x6baa, 0xbac7, 0x6ba7, 0xbac2, 0x6ba3, 0xbabc, 0x6ba0, 0xbab7,
  0x6b9c, 0xbab2, 0x6b99, 0xbaad, 0x6b96, 0xbaa7, 0x6b92, 0xbaa2,
  0x6b8f, 0xba9d, 0x6b8b, 0xba97, 0x6b88, 0xba92, 0x6b85, 0xba8d,
  0x6b81, 0xba88, 0x6b7e, 0xba82, 0x6b7a, 0xba7d, 0x6b77, 0xba78,
  0x6b73, 0xba73, 0x6b70, 0xba6d, 0x6b6d, 0xba68, 0x6b69, 0xba63,
  0x6b66, 0xba5d, 0x6b62, 0xba58, 0x6b5f, 0xba53, 0x6b5c, 0xba4e,
  0x6b58, 0xba48, 0x6b55, 0xba43, 0x6b51, 0xba3e, 0x6b4e, 0xba39,
  0x6b4a, 0xba33, 0x6b47, 0xba2e, 0x6b44, 0xba29, 0x6b40, 0xba23,
  0x6b3d, 0xba1e, 0x6b39, 0xba19, 0x6b36, 0xba14, 0x6b32, 0xba0e,
  0x6b2f, 0xba09, 0x6b2c, 0xba04, 0x6b28, 0xb9ff, 0x6b25, 0xb9f9,
  0x6b21, 0xb9f4, 0x6b1e, 0xb9ef, 0x6b1a, 0xb9ea, 0x6b17, 0xb9e4,
  0x6b13, 0xb9df, 0x6b10, 0xb9da, 0x6b0d, 0xb9d5, 0x6b09, 0xb9cf,
  0x6b06, 0xb9ca, 0x6b02, 0xb9c5, 0x6aff, 0xb9c0, 0x6afb, 0xb9ba,
  0x6af8, 0xb9b5, 0x6af4, 0xb9b0, 0x6af1, 0xb9ab, 0x6aee, 0xb9a5,
  0x6aea, 0xb9a0, 0x6ae7, 0xb99b, 0x6ae3, 0xb996, 0x6ae0, 0xb990,
  0x6adc, 0xb98b, 0x6ad9, 0xb986, 0x6ad5, 0xb981, 0x6ad2, 0xb97b,
  0x6ace, 0xb976, 0x6acb, 0xb971, 0x6ac8, 0xb96c, 0x6ac4, 0xb966,
  0x6ac1, 0xb961, 0x6abd, 0xb95c, 0x6aba, 0xb957, 0x6ab6, 0xb951,
  0x6ab3, 0xb94c, 0x6aaf, 0xb947, 0x6aac, 0xb942, 0x6aa8, 0xb93c,
  0x6aa5, 0xb937, 0x6aa1, 0xb932, 0x6a9e, 0xb92d, 0x6a9a, 0xb928,
  0x6a97, 0xb922, 0x6a93, 0xb91d, 0x6a90, 0xb918, 0x6a8c, 0xb913,
  0x6a89, 0xb90d, 0x6a86, 0xb908, 0x6a82, 0xb903, 0x6a7f, 0xb8fe,
  0x6a7b, 0xb8f8, 0x6a78, 0xb8f3, 0x6a74, 0xb8ee, 0x6a71, 0xb8e9,
  0x6a6d, 0xb8e4, 0x6a6a, 0xb8de, 0x6a66, 0xb8d9, 0x6a63, 0xb8d4,
  0x6a5f, 0xb8cf, 0x6a5c, 0xb8c9, 0x6a58, 0xb8c4, 0x6a55, 0xb8bf,
  0x6a51, 0xb8ba, 0x6a4e, 0xb8b5, 0x6a4a, 0xb8af, 0x6a47, 0xb8aa,
  0x6a43, 0xb8a5, 0x6a40, 0xb8a0, 0x6a3c, 0xb89b, 0x6a39, 0xb895,
  0x6a35, 0xb890, 0x6a32, 0xb88b, 0x6a2e, 0xb886, 0x6a2b, 0xb880,
  0x6a27, 0xb87b, 0x6a24, 0xb876, 0x6a20, 0xb871, 0x6a1d, 0xb86c,
  0x6a19, 0xb866, 0x6a16, 0xb861, 0x6a12, 0xb85c, 0x6a0e, 0xb857,
  0x6a0b, 0xb852, 0x6a07, 0xb84c, 0x6a04, 0xb847, 0x6a00, 0xb842,
  0x69fd, 0xb83d, 0x69f9, 0xb838, 0x69f6, 0xb832, 0x69f2, 0xb82d,
  0x69ef, 0xb828, 0x69eb, 0xb823, 0x69e8, 0xb81e, 0x69e4, 0xb818,
  0x69e1, 0xb813, 0x69dd, 0xb80e, 0x69da, 0xb809, 0x69d6, 0xb804,
  0x69d3, 0xb7fe, 0x69cf, 0xb7f9, 0x69cb, 0xb7f4, 0x69c8, 0xb7ef,
  0x69c4, 0xb7ea, 0x69c1, 0xb7e4, 0x69bd, 0xb7df, 0x69ba, 0xb7da,
  0x69b6, 0xb7d5, 0x69b3, 0xb7d0, 0x69af, 0xb7ca, 0x69ac, 0xb7c5,
  0x69a8, 0xb7c0, 0x69a5, 0xb7bb, 0x69a1, 0xb7b6, 0x699d, 0xb7b1,
  0x699a, 0xb7ab, 0x6996, 0xb7a6, 0x6993, 0xb7a1, 0x698f, 0xb79c,
  0x698c, 0xb797, 0x6988, 0xb791, 0x6985, 0xb78c, 0x6981, 0xb787,
  0x697d, 0xb782, 0x697a, 0xb77d, 0x6976, 0xb778, 0x6973, 0xb772,
  0x696f, 0xb76d, 0x696c, 0xb768, 0x6968, 0xb763, 0x6964, 0xb75e,
  0x6961, 0xb758, 0x695d, 0xb753, 0x695a, 0xb74e, 0x6956, 0xb749,
  0x6953, 0xb744, 0x694f, 0xb73f, 0x694b, 0xb739, 0x6948, 0xb734,
  0x6944, 0xb72f, 0x6941, 0xb72a, 0x693d, 0xb725, 0x693a, 0xb720,
  0x6936, 0xb71a, 0x6932, 0xb715, 0x692f, 0xb710, 0x692b, 0xb70b,
  0x6928, 0xb706, 0x6924, 0xb701, 0x6921, 0xb6fb, 0x691d, 0xb6f6,
  0x6919, 0xb6f1, 0x6916, 0xb6ec, 0x6912, 0xb6e7, 0x690f, 0xb6e2,
  0x690b, 0xb6dd, 0x6907, 0xb6d7, 0x6904, 0xb6d2, 0x6900, 0xb6cd,
  0x68fd, 0xb6c8, 0x68f9, 0xb6c3, 0x68f5, 0xb6be, 0x68f2, 0xb6b8,
  0x68ee, 0xb6b3, 0x68eb, 0xb6ae, 0x68e7, 0xb6a9, 0x68e3, 0xb6a4,
  0x68e0, 0xb69f, 0x68dc, 0xb69a, 0x68d9, 0xb694, 0x68d5, 0xb68f,
  0x68d1, 0xb68a, 0x68ce, 0xb685, 0x68ca, 0xb680, 0x68c7, 0xb67b,
  0x68c3, 0xb676, 0x68bf, 0xb670, 0x68bc, 0xb66b, 0x68b8, 0xb666,
  0x68b5, 0xb661, 0x68b1, 0xb65c, 0x68ad, 0xb657, 0x68aa, 0xb652,
  0x68a6, 0xb64c, 0x68a3, 0xb647, 0x689f, 0xb642, 0x689b, 0xb63d,
  0x6898, 0xb638, 0x6894, 0xb633, 0x6890, 0xb62e, 0x688d, 0xb628,
  0x6889, 0xb623, 0x6886, 0xb61e, 0x6882, 0xb619, 0x687e, 0xb614,
  0x687b, 0xb60f, 0x6877, 0xb60a, 0x6873, 0xb605, 0x6870, 0xb5ff,
  0x686c, 0xb5fa, 0x6868, 0xb5f5, 0x6865, 0xb5f0, 0x6861, 0xb5eb,
  0x685e, 0xb5e6, 0x685a, 0xb5e1, 0x6856, 0xb5dc, 0x6853, 0xb5d6,
  0x684f, 0xb5d1, 0x684b, 0xb5cc, 0x6848, 0xb5c7, 0x6844, 0xb5c2,
  0x6840, 0xb5bd, 0x683d, 0xb5b8, 0x6839, 0xb5b3, 0x6835, 0xb5ae,
  0x6832, 0xb5a8, 0x682e, 0xb5a3, 0x682b, 0xb59e, 0x6827, 0xb599,
  0x6823, 0xb594, 0x6820, 0xb58f, 0x681c, 0xb58a, 0x6818, 0xb585,
  0x6815, 0xb57f, 0x6811, 0xb57a, 0x680d, 0xb575, 0x680a, 0xb570,
  0x6806, 0xb56b, 0x6802, 0xb566, 0x67ff, 0xb561, 0x67fb, 0xb55c,
  0x67f7, 0xb557, 0x67f4, 0xb552, 0x67f0, 0xb54c, 0x67ec, 0xb547,
  0x67e9, 0xb542, 0x67e5, 0xb53d, 0x67e1, 0xb538, 0x67de, 0xb533,
  0x67da, 0xb52e, 0x67d6, 0xb529, 0x67d3, 0xb524, 0x67cf, 0xb51f,
  0x67cb, 0xb519, 0x67c8, 0xb514, 0x67c4, 0xb50f, 0x67c0, 0xb50a,
  0x67bd, 0xb505, 0x67b9, 0xb500, 0x67b5, 0xb4fb, 0x67b2, 0xb4f6,
  0x67ae, 0xb4f1, 0x67aa, 0xb4ec, 0x67a6, 0xb4e7, 0x67a3, 0xb4e1,
  0x679f, 0xb4dc, 0x679b, 0xb4d7, 0x6798, 0xb4d2, 0x6794, 0xb4cd,
  0x6790, 0xb4c8, 0x678d, 0xb4c3, 0x6789, 0xb4be, 0x6785, 0xb4b9,
  0x6782, 0xb4b4, 0x677e, 0xb4af, 0x677a, 0xb4aa, 0x6776, 0xb4a4,
  0x6773, 0xb49f, 0x676f, 0xb49a, 0x676b, 0xb495, 0x6768, 0xb490,
  0x6764, 0xb48b, 0x6760, 0xb486, 0x675d, 0xb481, 0x6759, 0xb47c,
  0x6755, 0xb477, 0x6751, 0xb472, 0x674e, 0xb46d, 0x674a, 0xb468,
  0x6746, 0xb462, 0x6743, 0xb45d, 0x673f, 0xb458, 0x673b, 0xb453,
  0x6737, 0xb44e, 0x6734, 0xb449, 0x6730, 0xb444, 0x672c, 0xb43f,
  0x6729, 0xb43a, 0x6725, 0xb435, 0x6721, 0xb430, 0x671d, 0xb42b,
  0x671a, 0xb426, 0x6716, 0xb421, 0x6712, 0xb41c, 0x670e, 0xb417,
  0x670b, 0xb411, 0x6707, 0xb40c, 0x6703, 0xb407, 0x6700, 0xb402,
  0x66fc, 0xb3fd, 0x66f8, 0xb3f8, 0x66f4, 0xb3f3, 0x66f1, 0xb3ee,
  0x66ed, 0xb3e9, 0x66e9, 0xb3e4, 0x66e5, 0xb3df, 0x66e2, 0xb3da,
  0x66de, 0xb3d5, 0x66da, 0xb3d0, 0x66d6, 0xb3cb, 0x66d3, 0xb3c6,
  0x66cf, 0xb3c1, 0x66cb, 0xb3bc, 0x66c8, 0xb3b7, 0x66c4, 0xb3b1,
  0x66c0, 0xb3ac, 0x66bc, 0xb3a7, 0x66b9, 0xb3a2, 0x66b5, 0xb39d,
  0x66b1, 0xb398, 0x66ad, 0xb393, 0x66aa, 0xb38e, 0x66a6, 0xb389,
  0x66a2, 0xb384, 0x669e, 0xb37f, 0x669b, 0xb37a, 0x6697, 0xb375,
  0x6693, 0xb370, 0x668f, 0xb36b, 0x668b, 0xb366, 0x6688, 0xb361,
  0x6684, 0xb35c, 0x6680, 0xb357, 0x667c, 0xb352, 0x6679, 0xb34d,
  0x6675, 0xb348, 0x6671, 0xb343, 0x666d, 0xb33e, 0x666a, 0xb339,
  0x6666, 0xb334, 0x6662, 0xb32f, 0x665e, 0xb32a, 0x665b, 0xb325,
  0x6657, 0xb31f, 0x6653, 0xb31a, 0x664f, 0xb315, 0x664b, 0xb310,
  0x6648, 0xb30b, 0x6644, 0xb306, 0x6640, 0xb301, 0x663c, 0xb2fc,
  0x6639, 0xb2f7, 0x6635, 0xb2f2, 0x6631, 0xb2ed, 0x662d, 0xb2e8,
  0x6629, 0xb2e3, 0x6626, 0xb2de, 0x6622, 0xb2d9, 0x661e, 0xb2d4,
  0x661a, 0xb2cf, 0x6616, 0xb2ca, 0x6613, 0xb2c5, 0x660f, 0xb2c0,
  0x660b, 0xb2bb, 0x6607, 0xb2b6, 0x6603, 0xb2b1, 0x6600, 0xb2ac,
  0x65fc, 0xb2a7, 0x65f8, 0xb2a2, 0x65f4, 0xb29d, 0x65f0, 0xb298,
  0x65ed, 0xb293, 0x65e9, 0xb28e, 0x65e5, 0xb289, 0x65e1, 0xb284,
  0x65dd, 0xb27f, 0x65da, 0xb27a, 0x65d6, 0xb275, 0x65d2, 0xb270,
  0x65ce, 0xb26b, 0x65ca, 0xb266, 0x65c7, 0xb261, 0x65c3, 0xb25c,
  0x65bf, 0xb257, 0x65bb, 0xb252, 0x65b7, 0xb24d, 0x65b4, 0xb248,
  0x65b0, 0xb243, 0x65ac, 0xb23e, 0x65a8, 0xb239, 0x65a4, 0xb234,
  0x65a0, 0xb22f, 0x659d, 0xb22a, 0x6599, 0xb225, 0x6595, 0xb220,
  0x6591, 0xb21b, 0x658d, 0xb216, 0x658a, 0xb211, 0x6586, 0xb20c,
  0x6582, 0xb207, 0x657e, 0xb202, 0x657a, 0xb1fd, 0x6576, 0xb1f8,
  0x6573, 0xb1f3, 0x656f, 0xb1ee, 0x656b, 0xb1e9, 0x6567, 0xb1e4,
  0x6563, 0xb1df, 0x655f, 0xb1da, 0x655c, 0xb1d6, 0x6558, 0xb1d1,
  0x6554, 0xb1cc, 0x6550, 0xb1c7, 0x654c, 0xb1c2, 0x6548, 0xb1bd,
  0x6545, 0xb1b8, 0x6541, 0xb1b3, 0x653d, 0xb1ae, 0x6539, 0xb1a9,
  0x6535, 0xb1a4, 0x6531, 0xb19f, 0x652d, 0xb19a, 0x652a, 0xb195,
  0x6526, 0xb190, 0x6522, 0xb18b, 0x651e, 0xb186, 0x651a, 0xb181,
  0x6516, 0xb17c, 0x6513, 0xb177, 0x650f, 0xb172, 0x650b, 0xb16d,
  0x6507, 0xb168, 0x6503, 0xb163, 0x64ff, 0xb15e, 0x64fb, 0xb159,
  0x64f7, 0xb154, 0x64f4, 0xb14f, 0x64f0, 0xb14a, 0x64ec, 0xb146,
  0x64e8, 0xb141, 0x64e4, 0xb13c, 0x64e0, 0xb137, 0x64dc, 0xb132,
  0x64d9, 0xb12d, 0x64d5, 0xb128, 0x64d1, 0xb123, 0x64cd, 0xb11e,
  0x64c9, 0xb119, 0x64c5, 0xb114, 0x64c1, 0xb10f, 0x64bd, 0xb10a,
  0x64ba, 0xb105, 0x64b6, 0xb100, 0x64b2, 0xb0fb, 0x64ae, 0xb0f6,
  0x64aa, 0xb0f1, 0x64a6, 0xb0ec, 0x64a2, 0xb0e8, 0x649e, 0xb0e3,
  0x649b, 0xb0de, 0x6497, 0xb0d9, 0x6493, 0xb0d4, 0x648f, 0xb0cf,
  0x648b, 0xb0ca, 0x6487, 0xb0c5, 0x6483, 0xb0c0, 0x647f, 0xb0bb,
  0x647b, 0xb0b6, 0x6478, 0xb0b1, 0x6474, 0xb0ac, 0x6470, 0xb0a7,
  0x646c, 0xb0a2, 0x6468, 0xb09e, 0x6464, 0xb099, 0x6460, 0xb094,
  0x645c, 0xb08f, 0x6458, 0xb08a, 0x6454, 0xb085, 0x6451, 0xb080,
  0x644d, 0xb07b, 0x6449, 0xb076, 0x6445, 0xb071, 0x6441, 0xb06c,
  0x643d, 0xb067, 0x6439, 0xb062, 0x6435, 0xb05e, 0x6431, 0xb059,
  0x642d, 0xb054, 0x6429, 0xb04f, 0x6426, 0xb04a, 0x6422, 0xb045,
  0x641e, 0xb040, 0x641a, 0xb03b, 0x6416, 0xb036, 0x6412, 0xb031,
  0x640e, 0xb02c, 0x640a, 0xb027, 0x6406, 0xb023, 0x6402, 0xb01e,
  0x63fe, 0xb019, 0x63fa, 0xb014, 0x63f7, 0xb00f, 0x63f3, 0xb00a,
  0x63ef, 0xb005, 0x63eb, 0xb000, 0x63e7, 0xaffb, 0x63e3, 0xaff6,
  0x63df, 0xaff1, 0x63db, 0xafed, 0x63d7, 0xafe8, 0x63d3, 0xafe3,
  0x63cf, 0xafde, 0x63cb, 0xafd9, 0x63c7, 0xafd4, 0x63c3, 0xafcf,
  0x63c0, 0xafca, 0x63bc, 0xafc5, 0x63b8, 0xafc1, 0x63b4, 0xafbc,
  0x63b0, 0xafb7, 0x63ac, 0xafb2, 0x63a8, 0xafad, 0x63a4, 0xafa8,
  0x63a0, 0xafa3, 0x639c, 0xaf9e, 0x6398, 0xaf99, 0x6394, 0xaf94,
  0x6390, 0xaf90, 0x638c, 0xaf8b, 0x6388, 0xaf86, 0x6384, 0xaf81,
  0x6380, 0xaf7c, 0x637c, 0xaf77, 0x6378, 0xaf72, 0x6375, 0xaf6d,
  0x6371, 0xaf69, 0x636d, 0xaf64, 0x6369, 0xaf5f, 0x6365, 0xaf5a,
  0x6361, 0xaf55, 0x635d, 0xaf50, 0x6359, 0xaf4b, 0x6355, 0xaf46,
  0x6351, 0xaf41, 0x634d, 0xaf3d, 0x6349, 0xaf38, 0x6345, 0xaf33,
  0x6341, 0xaf2e, 0x633d, 0xaf29, 0x6339, 0xaf24, 0x6335, 0xaf1f,
  0x6331, 0xaf1b, 0x632d, 0xaf16, 0x6329, 0xaf11, 0x6325, 0xaf0c,
  0x6321, 0xaf07, 0x631d, 0xaf02, 0x6319, 0xaefd, 0x6315, 0xaef8,
  0x6311, 0xaef4, 0x630d, 0xaeef, 0x6309, 0xaeea, 0x6305, 0xaee5,
  0x6301, 0xaee0, 0x62fd, 0xaedb, 0x62f9, 0xaed6, 0x62f5, 0xaed2,
  0x62f2, 0xaecd, 0x62ee, 0xaec8, 0x62ea, 0xaec3, 0x62e6, 0xaebe,
  0x62e2, 0xaeb9, 0x62de, 0xaeb4, 0x62da, 0xaeb0, 0x62d6, 0xaeab,
  0x62d2, 0xaea6, 0x62ce, 0xaea1, 0x62ca, 0xae9c, 0x62c6, 0xae97,
  0x62c2, 0xae92, 0x62be, 0xae8e, 0x62ba, 0xae89, 0x62b6, 0xae84,
  0x62b2, 0xae7f, 0x62ae, 0xae7a, 0x62aa, 0xae75, 0x62a6, 0xae71,
  0x62a2, 0xae6c, 0x629e, 0xae67, 0x629a, 0xae62, 0x6296, 0xae5d,
  0x6292, 0xae58, 0x628e, 0xae54, 0x628a, 0xae4f, 0x6286, 0xae4a,
  0x6282, 0xae45, 0x627e, 0xae40, 0x627a, 0xae3b, 0x6275, 0xae37,
  0x6271, 0xae32, 0x626d, 0xae2d, 0x6269, 0xae28, 0x6265, 0xae23,
  0x6261, 0xae1e, 0x625d, 0xae1a, 0x6259, 0xae15, 0x6255, 0xae10,
  0x6251, 0xae0b, 0x624d, 0xae06, 0x6249, 0xae01, 0x6245, 0xadfd,
  0x6241, 0xadf8, 0x623d, 0xadf3, 0x6239, 0xadee, 0x6235, 0xade9,
  0x6231, 0xade4, 0x622d, 0xade0, 0x6229, 0xaddb, 0x6225, 0xadd6,
  0x6221, 0xadd1, 0x621d, 0xadcc, 0x6219, 0xadc8, 0x6215, 0xadc3,
  0x6211, 0xadbe, 0x620d, 0xadb9, 0x6209, 0xadb4, 0x6205, 0xadaf,
  0x6201, 0xadab, 0x61fd, 0xada6, 0x61f9, 0xada1, 0x61f5, 0xad9c,
  0x61f1, 0xad97, 0x61ec, 0xad93, 0x61e8, 0xad8e, 0x61e4, 0xad89,
  0x61e0, 0xad84, 0x61dc, 0xad7f, 0x61d8, 0xad7b, 0x61d4, 0xad76,
  0x61d0, 0xad71, 0x61cc, 0xad6c, 0x61c8, 0xad67, 0x61c4, 0xad63,
  0x61c0, 0xad5e, 0x61bc, 0xad59, 0x61b8, 0xad54, 0x61b4, 0xad4f,
  0x61b0, 0xad4b, 0x61ac, 0xad46, 0x61a8, 0xad41, 0x61a3, 0xad3c,
  0x619f, 0xad37, 0x619b, 0xad33, 0x6197, 0xad2e, 0x6193, 0xad29,
  0x618f, 0xad24, 0x618b, 0xad1f, 0x6187, 0xad1b, 0x6183, 0xad16,
  0x617f, 0xad11, 0x617b, 0xad0c, 0x6177, 0xad08, 0x6173, 0xad03,
  0x616f, 0xacfe, 0x616b, 0xacf9, 0x6166, 0xacf4, 0x6162, 0xacf0,
  0x615e, 0xaceb, 0x615a, 0xace6, 0x6156, 0xace1, 0x6152, 0xacdd,
  0x614e, 0xacd8, 0x614a, 0xacd3, 0x6146, 0xacce, 0x6142, 0xacc9,
  0x613e, 0xacc5, 0x613a, 0xacc0, 0x6135, 0xacbb, 0x6131, 0xacb6,
  0x612d, 0xacb2, 0x6129, 0xacad, 0x6125, 0xaca8, 0x6121, 0xaca3,
  0x611d, 0xac9e, 0x6119, 0xac9a, 0x6115, 0xac95, 0x6111, 0xac90,
  0x610d, 0xac8b, 0x6108, 0xac87, 0x6104, 0xac82, 0x6100, 0xac7d,
  0x60fc, 0xac78, 0x60f8, 0xac74, 0x60f4, 0xac6f, 0x60f0, 0xac6a,
  0x60ec, 0xac65, 0x60e8, 0xac61, 0x60e4, 0xac5c, 0x60df, 0xac57,
  0x60db, 0xac52, 0x60d7, 0xac4e, 0x60d3, 0xac49, 0x60cf, 0xac44,
  0x60cb, 0xac3f, 0x60c7, 0xac3b, 0x60c3, 0xac36, 0x60bf, 0xac31,
  0x60ba, 0xac2c, 0x60b6, 0xac28, 0x60b2, 0xac23, 0x60ae, 0xac1e,
  0x60aa, 0xac19, 0x60a6, 0xac15, 0x60a2, 0xac10, 0x609e, 0xac0b,
  0x6099, 0xac06, 0x6095, 0xac02, 0x6091, 0xabfd, 0x608d, 0xabf8,
  0x6089, 0xabf3, 0x6085, 0xabef, 0x6081, 0xabea, 0x607d, 0xabe5,
  0x6078, 0xabe0, 0x6074, 0xabdc, 0x6070, 0xabd7, 0x606c, 0xabd2,
  0x6068, 0xabcd, 0x6064, 0xabc9, 0x6060, 0xabc4, 0x605c, 0xabbf,
  0x6057, 0xabbb, 0x6053, 0xabb6, 0x604f, 0xabb1, 0x604b, 0xabac,
  0x6047, 0xaba8, 0x6043, 0xaba3, 0x603f, 0xab9e, 0x603a, 0xab99,
  0x6036, 0xab95, 0x6032, 0xab90, 0x602e, 0xab8b, 0x602a, 0xab87,
  0x6026, 0xab82, 0x6022, 0xab7d, 0x601d, 0xab78, 0x6019, 0xab74,
  0x6015, 0xab6f, 0x6011, 0xab6a, 0x600d, 0xab66, 0x6009, 0xab61,
  0x6004, 0xab5c, 0x6000, 0xab57, 0x5ffc, 0xab53, 0x5ff8, 0xab4e,
  0x5ff4, 0xab49, 0x5ff0, 0xab45, 0x5fec, 0xab40, 0x5fe7, 0xab3b,
  0x5fe3, 0xab36, 0x5fdf, 0xab32, 0x5fdb, 0xab2d, 0x5fd7, 0xab28,
  0x5fd3, 0xab24, 0x5fce, 0xab1f, 0x5fca, 0xab1a, 0x5fc6, 0xab16,
  0x5fc2, 0xab11, 0x5fbe, 0xab0c, 0x5fba, 0xab07, 0x5fb5, 0xab03,
  0x5fb1, 0xaafe, 0x5fad, 0xaaf9, 0x5fa9, 0xaaf5, 0x5fa5, 0xaaf0,
  0x5fa0, 0xaaeb, 0x5f9c, 0xaae7, 0x5f98, 0xaae2, 0x5f94, 0xaadd,
  0x5f90, 0xaad8, 0x5f8c, 0xaad4, 0x5f87, 0xaacf, 0x5f83, 0xaaca,
  0x5f7f, 0xaac6, 0x5f7b, 0xaac1, 0x5f77, 0xaabc, 0x5f72, 0xaab8,
  0x5f6e, 0xaab3, 0x5f6a, 0xaaae, 0x5f66, 0xaaaa, 0x5f62, 0xaaa5,
  0x5f5e, 0xaaa0, 0x5f59, 0xaa9c, 0x5f55, 0xaa97, 0x5f51, 0xaa92,
  0x5f4d, 0xaa8e, 0x5f49, 0xaa89, 0x5f44, 0xaa84, 0x5f40, 0xaa7f,
  0x5f3c, 0xaa7b, 0x5f38, 0xaa76, 0x5f34, 0xaa71, 0x5f2f, 0xaa6d,
  0x5f2b, 0xaa68, 0x5f27, 0xaa63, 0x5f23, 0xaa5f, 0x5f1f, 0xaa5a,
  0x5f1a, 0xaa55, 0x5f16, 0xaa51, 0x5f12, 0xaa4c, 0x5f0e, 0xaa47,
  0x5f0a, 0xaa43, 0x5f05, 0xaa3e, 0x5f01, 0xaa39, 0x5efd, 0xaa35,
  0x5ef9, 0xaa30, 0x5ef5, 0xaa2b, 0x5ef0, 0xaa27, 0x5eec, 0xaa22,
  0x5ee8, 0xaa1d, 0x5ee4, 0xaa19, 0x5edf, 0xaa14, 0x5edb, 0xaa10,
  0x5ed7, 0xaa0b, 0x5ed3, 0xaa06, 0x5ecf, 0xaa02, 0x5eca, 0xa9fd,
  0x5ec6, 0xa9f8, 0x5ec2, 0xa9f4, 0x5ebe, 0xa9ef, 0x5eb9, 0xa9ea,
  0x5eb5, 0xa9e6, 0x5eb1, 0xa9e1, 0x5ead, 0xa9dc, 0x5ea9, 0xa9d8,
  0x5ea4, 0xa9d3, 0x5ea0, 0xa9ce, 0x5e9c, 0xa9ca, 0x5e98, 0xa9c5,
  0x5e93, 0xa9c0, 0x5e8f, 0xa9bc, 0x5e8b, 0xa9b7, 0x5e87, 0xa9b3,
  0x5e82, 0xa9ae, 0x5e7e, 0xa9a9, 0x5e7a, 0xa9a5, 0x5e76, 0xa9a0,
  0x5e71, 0xa99b, 0x5e6d, 0xa997, 0x5e69, 0xa992, 0x5e65, 0xa98d,
  0x5e60, 0xa989, 0x5e5c, 0xa984, 0x5e58, 0xa980, 0x5e54, 0xa97b,
  0x5e50, 0xa976, 0x5e4b, 0xa972, 0x5e47, 0xa96d, 0x5e43, 0xa968,
  0x5e3f, 0xa964, 0x5e3a, 0xa95f, 0x5e36, 0xa95b, 0x5e32, 0xa956,
  0x5e2d, 0xa951, 0x5e29, 0xa94d, 0x5e25, 0xa948, 0x5e21, 0xa943,
  0x5e1c, 0xa93f, 0x5e18, 0xa93a, 0x5e14, 0xa936, 0x5e10, 0xa931,
  0x5e0b, 0xa92c, 0x5e07, 0xa928, 0x5e03, 0xa923, 0x5dff, 0xa91e,
  0x5dfa, 0xa91a, 0x5df6, 0xa915, 0x5df2, 0xa911, 0x5dee, 0xa90c,
  0x5de9, 0xa907, 0x5de5, 0xa903, 0x5de1, 0xa8fe, 0x5ddc, 0xa8fa,
  0x5dd8, 0xa8f5, 0x5dd4, 0xa8f0, 0x5dd0, 0xa8ec, 0x5dcb, 0xa8e7,
  0x5dc7, 0xa8e3, 0x5dc3, 0xa8de, 0x5dbf, 0xa8d9, 0x5dba, 0xa8d5,
  0x5db6, 0xa8d0, 0x5db2, 0xa8cc, 0x5dad, 0xa8c7, 0x5da9, 0xa8c2,
  0x5da5, 0xa8be, 0x5da1, 0xa8b9, 0x5d9c, 0xa8b5, 0x5d98, 0xa8b0,
  0x5d94, 0xa8ab, 0x5d8f, 0xa8a7, 0x5d8b, 0xa8a2, 0x5d87, 0xa89e,
  0x5d83, 0xa899, 0x5d7e, 0xa894, 0x5d7a, 0xa890, 0x5d76, 0xa88b,
  0x5d71, 0xa887, 0x5d6d, 0xa882, 0x5d69, 0xa87d, 0x5d65, 0xa879,
  0x5d60, 0xa874, 0x5d5c, 0xa870, 0x5d58, 0xa86b, 0x5d53, 0xa867,
  0x5d4f, 0xa862, 0x5d4b, 0xa85d, 0x5d46, 0xa859, 0x5d42, 0xa854,
  0x5d3e, 0xa850, 0x5d3a, 0xa84b, 0x5d35, 0xa847, 0x5d31, 0xa842,
  0x5d2d, 0xa83d, 0x5d28, 0xa839, 0x5d24, 0xa834, 0x5d20, 0xa830,
  0x5d1b, 0xa82b, 0x5d17, 0xa827, 0x5d13, 0xa822, 0x5d0e, 0xa81d,
  0x5d0a, 0xa819, 0x5d06, 0xa814, 0x5d01, 0xa810, 0x5cfd, 0xa80b,
  0x5cf9, 0xa807, 0x5cf5, 0xa802, 0x5cf0, 0xa7fd, 0x5cec, 0xa7f9,
  0x5ce8, 0xa7f4, 0x5ce3, 0xa7f0, 0x5cdf, 0xa7eb, 0x5cdb, 0xa7e7,
  0x5cd6, 0xa7e2, 0x5cd2, 0xa7de, 0x5cce, 0xa7d9, 0x5cc9, 0xa7d4,
  0x5cc5, 0xa7d0, 0x5cc1, 0xa7cb, 0x5cbc, 0xa7c7, 0x5cb8, 0xa7c2,
  0x5cb4, 0xa7be, 0x5caf, 0xa7b9, 0x5cab, 0xa7b5, 0x5ca7, 0xa7b0,
  0x5ca2, 0xa7ab, 0x5c9e, 0xa7a7, 0x5c9a, 0xa7a2, 0x5c95, 0xa79e,
  0x5c91, 0xa799, 0x5c8d, 0xa795, 0x5c88, 0xa790, 0x5c84, 0xa78c,
  0x5c80, 0xa787, 0x5c7b, 0xa783, 0x5c77, 0xa77e, 0x5c73, 0xa779,
  0x5c6e, 0xa775, 0x5c6a, 0xa770, 0x5c66, 0xa76c, 0x5c61, 0xa767,
  0x5c5d, 0xa763, 0x5c58, 0xa75e, 0x5c54, 0xa75a, 0x5c50, 0xa755,
  0x5c4b, 0xa751, 0x5c47, 0xa74c, 0x5c43, 0xa748, 0x5c3e, 0xa743,
  0x5c3a, 0xa73f, 0x5c36, 0xa73a, 0x5c31, 0xa735, 0x5c2d, 0xa731,
  0x5c29, 0xa72c, 0x5c24, 0xa728, 0x5c20, 0xa723, 0x5c1b, 0xa71f,
  0x5c17, 0xa71a, 0x5c13, 0xa716, 0x5c0e, 0xa711, 0x5c0a, 0xa70d,
  0x5c06, 0xa708, 0x5c01, 0xa704, 0x5bfd, 0xa6ff, 0x5bf9, 0xa6fb,
  0x5bf4, 0xa6f6, 0x5bf0, 0xa6f2, 0x5beb, 0xa6ed, 0x5be7, 0xa6e9,
  0x5be3, 0xa6e4, 0x5bde, 0xa6e0, 0x5bda, 0xa6db, 0x5bd6, 0xa6d7,
  0x5bd1, 0xa6d2, 0x5bcd, 0xa6ce, 0x5bc8, 0xa6c9, 0x5bc4, 0xa6c5,
  0x5bc0, 0xa6c0, 0x5bbb, 0xa6bc, 0x5bb7, 0xa6b7, 0x5bb2, 0xa6b3,
  0x5bae, 0xa6ae, 0x5baa, 0xa6aa, 0x5ba5, 0xa6a5, 0x5ba1, 0xa6a1,
  0x5b9d, 0xa69c, 0x5b98, 0xa698, 0x5b94, 0xa693, 0x5b8f, 0xa68f,
  0x5b8b, 0xa68a, 0x5b87, 0xa686, 0x5b82, 0xa681, 0x5b7e, 0xa67d,
  0x5b79, 0xa678, 0x5b75, 0xa674, 0x5b71, 0xa66f, 0x5b6c, 0xa66b,
  0x5b68, 0xa666, 0x5b63, 0xa662, 0x5b5f, 0xa65d, 0x5b5b, 0xa659,
  0x5b56, 0xa654, 0x5b52, 0xa650, 0x5b4d, 0xa64b, 0x5b49, 0xa647,
  0x5b45, 0xa642, 0x5b40, 0xa63e, 0x5b3c, 0xa639, 0x5b37, 0xa635,
  0x5b33, 0xa630, 0x5b2f, 0xa62c, 0x5b2a, 0xa627, 0x5b26, 0xa623,
  0x5b21, 0xa61f, 0x5b1d, 0xa61a, 0x5b19, 0xa616, 0x5b14, 0xa611,
  0x5b10, 0xa60d, 0x5b0b, 0xa608, 0x5b07, 0xa604, 0x5b02, 0xa5ff,
  0x5afe, 0xa5fb, 0x5afa, 0xa5f6, 0x5af5, 0xa5f2, 0x5af1, 0xa5ed,
  0x5aec, 0xa5e9, 0x5ae8, 0xa5e4, 0x5ae4, 0xa5e0, 0x5adf, 0xa5dc,
  0x5adb, 0xa5d7, 0x5ad6, 0xa5d3, 0x5ad2, 0xa5ce, 0x5acd, 0xa5ca,
  0x5ac9, 0xa5c5, 0x5ac5, 0xa5c1, 0x5ac0, 0xa5bc, 0x5abc, 0xa5b8,
  0x5ab7, 0xa5b3, 0x5ab3, 0xa5af, 0x5aae, 0xa5aa, 0x5aaa, 0xa5a6,
  0x5aa5, 0xa5a2, 0x5aa1, 0xa59d, 0x5a9d, 0xa599, 0x5a98, 0xa594,
  0x5a94, 0xa590, 0x5a8f, 0xa58b, 0x5a8b, 0xa587, 0x5a86, 0xa582,
  0x5a82, 0xa57e, 0x5a7e, 0xa57a, 0x5a79, 0xa575, 0x5a75, 0xa571,
  0x5a70, 0xa56c, 0x5a6c, 0xa568, 0x5a67, 0xa563, 0x5a63, 0xa55f,
  0x5a5e, 0xa55b, 0x5a5a, 0xa556, 0x5a56, 0xa552, 0x5a51, 0xa54d,
  0x5a4d, 0xa549, 0x5a48, 0xa544, 0x5a44, 0xa540, 0x5a3f, 0xa53b,
  0x5a3b, 0xa537, 0x5a36, 0xa533, 0x5a32, 0xa52e, 0x5a2d, 0xa52a,
  0x5a29, 0xa525, 0x5a24, 0xa521, 0x5a20, 0xa51c, 0x5a1c, 0xa518,
  0x5a17, 0xa514, 0x5a13, 0xa50f, 0x5a0e, 0xa50b, 0x5a0a, 0xa506,
  0x5a05, 0xa502, 0x5a01, 0xa4fe, 0x59fc, 0xa4f9, 0x59f8, 0xa4f5,
  0x59f3, 0xa4f0, 0x59ef, 0xa4ec, 0x59ea, 0xa4e7, 0x59e6, 0xa4e3,
  0x59e1, 0xa4df, 0x59dd, 0xa4da, 0x59d9, 0xa4d6, 0x59d4, 0xa4d1,
  0x59d0, 0xa4cd, 0x59cb, 0xa4c9, 0x59c7, 0xa4c4, 0x59c2, 0xa4c0,
  0x59be, 0xa4bb, 0x59b9, 0xa4b7, 0x59b5, 0xa4b3, 0x59b0, 0xa4ae,
  0x59ac, 0xa4aa, 0x59a7, 0xa4a5, 0x59a3, 0xa4a1, 0x599e, 0xa49d,
  0x599a, 0xa498, 0x5995, 0xa494, 0x5991, 0xa48f, 0x598c, 0xa48b,
  0x5988, 0xa487, 0x5983, 0xa482, 0x597f, 0xa47e, 0x597a, 0xa479,
  0x5976, 0xa475, 0x5971, 0xa471, 0x596d, 0xa46c, 0x5968, 0xa468,
  0x5964, 0xa463, 0x595f, 0xa45f, 0x595b, 0xa45b, 0x5956, 0xa456,
  0x5952, 0xa452, 0x594d, 0xa44e, 0x5949, 0xa449, 0x5944, 0xa445,
  0x5940, 0xa440, 0x593b, 0xa43c, 0x5937, 0xa438, 0x5932, 0xa433,
  0x592e, 0xa42f, 0x5929, 0xa42a, 0x5925, 0xa426, 0x5920, 0xa422,
  0x591c, 0xa41d, 0x5917, 0xa419, 0x5913, 0xa415, 0x590e, 0xa410,
  0x590a, 0xa40c, 0x5905, 0xa407, 0x5901, 0xa403, 0x58fc, 0xa3ff,
  0x58f8, 0xa3fa, 0x58f3, 0xa3f6, 0x58ef, 0xa3f2, 0x58ea, 0xa3ed,
  0x58e6, 0xa3e9, 0x58e1, 0xa3e5, 0x58dd, 0xa3e0, 0x58d8, 0xa3dc,
  0x58d4, 0xa3d7, 0x58cf, 0xa3d3, 0x58cb, 0xa3cf, 0x58c6, 0xa3ca,
  0x58c1, 0xa3c6, 0x58bd, 0xa3c2, 0x58b8, 0xa3bd, 0x58b4, 0xa3b9,
  0x58af, 0xa3b5, 0x58ab, 0xa3b0, 0x58a6, 0xa3ac, 0x58a2, 0xa3a8,
  0x589d, 0xa3a3, 0x5899, 0xa39f, 0x5894, 0xa39a, 0x5890, 0xa396,
  0x588b, 0xa392, 0x5887, 0xa38d, 0x5882, 0xa389, 0x587d, 0xa385,
  0x5879, 0xa380, 0x5874, 0xa37c, 0x5870, 0xa378, 0x586b, 0xa373,
  0x5867, 0xa36f, 0x5862, 0xa36b, 0x585e, 0xa366, 0x5859, 0xa362,
  0x5855, 0xa35e, 0x5850, 0xa359, 0x584b, 0xa355, 0x5847, 0xa351,
  0x5842, 0xa34c, 0x583e, 0xa348, 0x5839, 0xa344, 0x5835, 0xa33f,
  0x5830, 0xa33b, 0x582c, 0xa337, 0x5827, 0xa332, 0x5822, 0xa32e,
  0x581e, 0xa32a, 0x5819, 0xa325, 0x5815, 0xa321, 0x5810, 0xa31d,
  0x580c, 0xa318, 0x5807, 0xa314, 0x5803, 0xa310, 0x57fe, 0xa30b,
  0x57f9, 0xa307, 0x57f5, 0xa303, 0x57f0, 0xa2ff, 0x57ec, 0xa2fa,
  0x57e7, 0xa2f6, 0x57e3, 0xa2f2, 0x57de, 0xa2ed, 0x57d9, 0xa2e9,
  0x57d5, 0xa2e5, 0x57d0, 0xa2e0, 0x57cc, 0xa2dc, 0x57c7, 0xa2d8,
  0x57c3, 0xa2d3, 0x57be, 0xa2cf, 0x57b9, 0xa2cb, 0x57b5, 0xa2c6,
  0x57b0, 0xa2c2, 0x57ac, 0xa2be, 0x57a7, 0xa2ba, 0x57a3, 0xa2b5,
  0x579e, 0xa2b1, 0x5799, 0xa2ad, 0x5795, 0xa2a8, 0x5790, 0xa2a4,
  0x578c, 0xa2a0, 0x5787, 0xa29b, 0x5783, 0xa297, 0x577e, 0xa293,
  0x5779, 0xa28f, 0x5775, 0xa28a, 0x5770, 0xa286, 0x576c, 0xa282,
  0x5767, 0xa27d, 0x5762, 0xa279, 0x575e, 0xa275, 0x5759, 0xa271,
  0x5755, 0xa26c, 0x5750, 0xa268, 0x574b, 0xa264, 0x5747, 0xa25f,
  0x5742, 0xa25b, 0x573e, 0xa257, 0x5739, 0xa253, 0x5734, 0xa24e,
  0x5730, 0xa24a, 0x572b, 0xa246, 0x5727, 0xa241, 0x5722, 0xa23d,
  0x571d, 0xa239, 0x5719, 0xa235, 0x5714, 0xa230, 0x5710, 0xa22c,
  0x570b, 0xa228, 0x5706, 0xa224, 0x5702, 0xa21f, 0x56fd, 0xa21b,
  0x56f9, 0xa217, 0x56f4, 0xa212, 0x56ef, 0xa20e, 0x56eb, 0xa20a,
  0x56e6, 0xa206, 0x56e2, 0xa201, 0x56dd, 0xa1fd, 0x56d8, 0xa1f9,
  0x56d4, 0xa1f5, 0x56cf, 0xa1f0, 0x56ca, 0xa1ec, 0x56c6, 0xa1e8,
  0x56c1, 0xa1e4, 0x56bd, 0xa1df, 0x56b8, 0xa1db, 0x56b3, 0xa1d7,
  0x56af, 0xa1d3, 0x56aa, 0xa1ce, 0x56a5, 0xa1ca, 0x56a1, 0xa1c6,
  0x569c, 0xa1c1, 0x5698, 0xa1bd, 0x5693, 0xa1b9, 0x568e, 0xa1b5,
  0x568a, 0xa1b0, 0x5685, 0xa1ac, 0x5680, 0xa1a8, 0x567c, 0xa1a4,
  0x5677, 0xa1a0, 0x5673, 0xa19b, 0x566e, 0xa197, 0x5669, 0xa193,
  0x5665, 0xa18f, 0x5660, 0xa18a, 0x565b, 0xa186, 0x5657, 0xa182,
  0x5652, 0xa17e, 0x564d, 0xa179, 0x5649, 0xa175, 0x5644, 0xa171,
  0x5640, 0xa16d, 0x563b, 0xa168, 0x5636, 0xa164, 0x5632, 0xa160,
  0x562d, 0xa15c, 0x5628, 0xa157, 0x5624, 0xa153, 0x561f, 0xa14f,
  0x561a, 0xa14b, 0x5616, 0xa147, 0x5611, 0xa142, 0x560c, 0xa13e,
  0x5608, 0xa13a, 0x5603, 0xa136, 0x55fe, 0xa131, 0x55fa, 0xa12d,
  0x55f5, 0xa129, 0x55f0, 0xa125, 0x55ec, 0xa121, 0x55e7, 0xa11c,
  0x55e3, 0xa118, 0x55de, 0xa114, 0x55d9, 0xa110, 0x55d5, 0xa10b,
  0x55d0, 0xa107, 0x55cb, 0xa103, 0x55c7, 0xa0ff, 0x55c2, 0xa0fb,
  0x55bd, 0xa0f6, 0x55b9, 0xa0f2, 0x55b4, 0xa0ee, 0x55af, 0xa0ea,
  0x55ab, 0xa0e6, 0x55a6, 0xa0e1, 0x55a1, 0xa0dd, 0x559d, 0xa0d9,
  0x5598, 0xa0d5, 0x5593, 0xa0d1, 0x558f, 0xa0cc, 0x558a, 0xa0c8,
  0x5585, 0xa0c4, 0x5581, 0xa0c0, 0x557c, 0xa0bc, 0x5577, 0xa0b7,
  0x5572, 0xa0b3, 0x556e, 0xa0af, 0x5569, 0xa0ab, 0x5564, 0xa0a7,
  0x5560, 0xa0a2, 0x555b, 0xa09e, 0x5556, 0xa09a, 0x5552, 0xa096,
  0x554d, 0xa092, 0x5548, 0xa08e, 0x5544, 0xa089, 0x553f, 0xa085,
  0x553a, 0xa081, 0x5536, 0xa07d, 0x5531, 0xa079, 0x552c, 0xa074,
  0x5528, 0xa070, 0x5523, 0xa06c, 0x551e, 0xa068, 0x5519, 0xa064,
  0x5515, 0xa060, 0x5510, 0xa05b, 0x550b, 0xa057, 0x5507, 0xa053,
  0x5502, 0xa04f, 0x54fd, 0xa04b, 0x54f9, 0xa046, 0x54f4, 0xa042,
  0x54ef, 0xa03e, 0x54ea, 0xa03a, 0x54e6, 0xa036, 0x54e1, 0xa032,
  0x54dc, 0xa02d, 0x54d8, 0xa029, 0x54d3, 0xa025, 0x54ce, 0xa021,
  0x54ca, 0xa01d, 0x54c5, 0xa019, 0x54c0, 0xa014, 0x54bb, 0xa010,
  0x54b7, 0xa00c, 0x54b2, 0xa008, 0x54ad, 0xa004, 0x54a9, 0xa000,
  0x54a4, 0x9ffc, 0x549f, 0x9ff7, 0x549a, 0x9ff3, 0x5496, 0x9fef,
  0x5491, 0x9feb, 0x548c, 0x9fe7, 0x5488, 0x9fe3, 0x5483, 0x9fde,
  0x547e, 0x9fda, 0x5479, 0x9fd6, 0x5475, 0x9fd2, 0x5470, 0x9fce,
  0x546b, 0x9fca, 0x5467, 0x9fc6, 0x5462, 0x9fc1, 0x545d, 0x9fbd,
  0x5458, 0x9fb9, 0x5454, 0x9fb5, 0x544f, 0x9fb1, 0x544a, 0x9fad,
  0x5445, 0x9fa9, 0x5441, 0x9fa4, 0x543c, 0x9fa0, 0x5437, 0x9f9c,
  0x5433, 0x9f98, 0x542e, 0x9f94, 0x5429, 0x9f90, 0x5424, 0x9f8c,
  0x5420, 0x9f88, 0x541b, 0x9f83, 0x5416, 0x9f7f, 0x5411, 0x9f7b,
  0x540d, 0x9f77, 0x5408, 0x9f73, 0x5403, 0x9f6f, 0x53fe, 0x9f6b,
  0x53fa, 0x9f67, 0x53f5, 0x9f62, 0x53f0, 0x9f5e, 0x53eb, 0x9f5a,
  0x53e7, 0x9f56, 0x53e2, 0x9f52, 0x53dd, 0x9f4e, 0x53d8, 0x9f4a,
  0x53d4, 0x9f46, 0x53cf, 0x9f41, 0x53ca, 0x9f3d, 0x53c5, 0x9f39,
  0x53c1, 0x9f35, 0x53bc, 0x9f31, 0x53b7, 0x9f2d, 0x53b2, 0x9f29,
  0x53ae, 0x9f25, 0x53a9, 0x9f21, 0x53a4, 0x9f1c, 0x539f, 0x9f18,
  0x539b, 0x9f14, 0x5396, 0x9f10, 0x5391, 0x9f0c, 0x538c, 0x9f08,
  0x5388, 0x9f04, 0x5383, 0x9f00, 0x537e, 0x9efc, 0x5379, 0x9ef8,
  0x5375, 0x9ef3, 0x5370, 0x9eef, 0x536b, 0x9eeb, 0x5366, 0x9ee7,
  0x5362, 0x9ee3, 0x535d, 0x9edf, 0x5358, 0x9edb, 0x5353, 0x9ed7,
  0x534e, 0x9ed3, 0x534a, 0x9ecf, 0x5345, 0x9ecb, 0x5340, 0x9ec6,
  0x533b, 0x9ec2, 0x5337, 0x9ebe, 0x5332, 0x9eba, 0x532d, 0x9eb6,
  0x5328, 0x9eb2, 0x5323, 0x9eae, 0x531f, 0x9eaa, 0x531a, 0x9ea6,
  0x5315, 0x9ea2, 0x5310, 0x9e9e, 0x530c, 0x9e9a, 0x5307, 0x9e95,
  0x5302, 0x9e91, 0x52fd, 0x9e8d, 0x52f8, 0x9e89, 0x52f4, 0x9e85,
  0x52ef, 0x9e81, 0x52ea, 0x9e7d, 0x52e5, 0x9e79, 0x52e1, 0x9e75,
  0x52dc, 0x9e71, 0x52d7, 0x9e6d, 0x52d2, 0x9e69, 0x52cd, 0x9e65,
  0x52c9, 0x9e61, 0x52c4, 0x9e5d, 0x52bf, 0x9e58, 0x52ba, 0x9e54,
  0x52b5, 0x9e50, 0x52b1, 0x9e4c, 0x52ac, 0x9e48, 0x52a7, 0x9e44,
  0x52a2, 0x9e40, 0x529d, 0x9e3c, 0x5299, 0x9e38, 0x5294, 0x9e34,
  0x528f, 0x9e30, 0x528a, 0x9e2c, 0x5285, 0x9e28, 0x5281, 0x9e24,
  0x527c, 0x9e20, 0x5277, 0x9e1c, 0x5272, 0x9e18, 0x526d, 0x9e14,
  0x5269, 0x9e0f, 0x5264, 0x9e0b, 0x525f, 0x9e07, 0x525a, 0x9e03,
  0x5255, 0x9dff, 0x5251, 0x9dfb, 0x524c, 0x9df7, 0x5247, 0x9df3,
  0x5242, 0x9def, 0x523d, 0x9deb, 0x5238, 0x9de7, 0x5234, 0x9de3,
  0x522f, 0x9ddf, 0x522a, 0x9ddb, 0x5225, 0x9dd7, 0x5220, 0x9dd3,
  0x521c, 0x9dcf, 0x5217, 0x9dcb, 0x5212, 0x9dc7, 0x520d, 0x9dc3,
  0x5208, 0x9dbf, 0x5203, 0x9dbb, 0x51ff, 0x9db7, 0x51fa, 0x9db3,
  0x51f5, 0x9daf, 0x51f0, 0x9dab, 0x51eb, 0x9da7, 0x51e6, 0x9da3,
  0x51e2, 0x9d9f, 0x51dd, 0x9d9b, 0x51d8, 0x9d97, 0x51d3, 0x9d93,
  0x51ce, 0x9d8f, 0x51c9, 0x9d8b, 0x51c5, 0x9d86, 0x51c0, 0x9d82,
  0x51bb, 0x9d7e, 0x51b6, 0x9d7a, 0x51b1, 0x9d76, 0x51ac, 0x9d72,
  0x51a8, 0x9d6e, 0x51a3, 0x9d6a, 0x519e, 0x9d66, 0x5199, 0x9d62,
  0x5194, 0x9d5e, 0x518f, 0x9d5a, 0x518b, 0x9d56, 0x5186, 0x9d52,
  0x5181, 0x9d4e, 0x517c, 0x9d4a, 0x5177, 0x9d46, 0x5172, 0x9d42,
  0x516e, 0x9d3e, 0x5169, 0x9d3a, 0x5164, 0x9d36, 0x515f, 0x9d32,
  0x515a, 0x9d2e, 0x5155, 0x9d2a, 0x5150, 0x9d26, 0x514c, 0x9d22,
  0x5147, 0x9d1e, 0x5142, 0x9d1a, 0x513d, 0x9d16, 0x5138, 0x9d12,
  0x5133, 0x9d0e, 0x512e, 0x9d0b, 0x512a, 0x9d07, 0x5125, 0x9d03,
  0x5120, 0x9cff, 0x511b, 0x9cfb, 0x5116, 0x9cf7, 0x5111, 0x9cf3,
  0x510c, 0x9cef, 0x5108, 0x9ceb, 0x5103, 0x9ce7, 0x50fe, 0x9ce3,
  0x50f9, 0x9cdf, 0x50f4, 0x9cdb, 0x50ef, 0x9cd7, 0x50ea, 0x9cd3,
  0x50e5, 0x9ccf, 0x50e1, 0x9ccb, 0x50dc, 0x9cc7, 0x50d7, 0x9cc3,
  0x50d2, 0x9cbf, 0x50cd, 0x9cbb, 0x50c8, 0x9cb7, 0x50c3, 0x9cb3,
  0x50bf, 0x9caf, 0x50ba, 0x9cab, 0x50b5, 0x9ca7, 0x50b0, 0x9ca3,
  0x50ab, 0x9c9f, 0x50a6, 0x9c9b, 0x50a1, 0x9c97, 0x509c, 0x9c93,
  0x5097, 0x9c8f, 0x5093, 0x9c8b, 0x508e, 0x9c88, 0x5089, 0x9c84,
  0x5084, 0x9c80, 0x507f, 0x9c7c, 0x507a, 0x9c78, 0x5075, 0x9c74,
  0x5070, 0x9c70, 0x506c, 0x9c6c, 0x5067, 0x9c68, 0x5062, 0x9c64,
  0x505d, 0x9c60, 0x5058, 0x9c5c, 0x5053, 0x9c58, 0x504e, 0x9c54,
  0x5049, 0x9c50, 0x5044, 0x9c4c, 0x503f, 0x9c48, 0x503b, 0x9c44,
  0x5036, 0x9c40, 0x5031, 0x9c3d, 0x502c, 0x9c39, 0x5027, 0x9c35,
  0x5022, 0x9c31, 0x501d, 0x9c2d, 0x5018, 0x9c29, 0x5013, 0x9c25,
  0x500f, 0x9c21, 0x500a, 0x9c1d, 0x5005, 0x9c19, 0x5000, 0x9c15,
  0x4ffb, 0x9c11, 0x4ff6, 0x9c0d, 0x4ff1, 0x9c09, 0x4fec, 0x9c06,
  0x4fe7, 0x9c02, 0x4fe2, 0x9bfe, 0x4fdd, 0x9bfa, 0x4fd9, 0x9bf6,
  0x4fd4, 0x9bf2, 0x4fcf, 0x9bee, 0x4fca, 0x9bea, 0x4fc5, 0x9be6,
  0x4fc0, 0x9be2, 0x4fbb, 0x9bde, 0x4fb6, 0x9bda, 0x4fb1, 0x9bd7,
  0x4fac, 0x9bd3, 0x4fa7, 0x9bcf, 0x4fa2, 0x9bcb, 0x4f9e, 0x9bc7,
  0x4f99, 0x9bc3, 0x4f94, 0x9bbf, 0x4f8f, 0x9bbb, 0x4f8a, 0x9bb7,
  0x4f85, 0x9bb3, 0x4f80, 0x9baf, 0x4f7b, 0x9bac, 0x4f76, 0x9ba8,
  0x4f71, 0x9ba4, 0x4f6c, 0x9ba0, 0x4f67, 0x9b9c, 0x4f62, 0x9b98,
  0x4f5e, 0x9b94, 0x4f59, 0x9b90, 0x4f54, 0x9b8c, 0x4f4f, 0x9b88,
  0x4f4a, 0x9b85, 0x4f45, 0x9b81, 0x4f40, 0x9b7d, 0x4f3b, 0x9b79,
  0x4f36, 0x9b75, 0x4f31, 0x9b71, 0x4f2c, 0x9b6d, 0x4f27, 0x9b69,
  0x4f22, 0x9b65, 0x4f1d, 0x9b62, 0x4f18, 0x9b5e, 0x4f14, 0x9b5a,
  0x4f0f, 0x9b56, 0x4f0a, 0x9b52, 0x4f05, 0x9b4e, 0x4f00, 0x9b4a,
  0x4efb, 0x9b46, 0x4ef6, 0x9b43, 0x4ef1, 0x9b3f, 0x4eec, 0x9b3b,
  0x4ee7, 0x9b37, 0x4ee2, 0x9b33, 0x4edd, 0x9b2f, 0x4ed8, 0x9b2b,
  0x4ed3, 0x9b27, 0x4ece, 0x9b24, 0x4ec9, 0x9b20, 0x4ec4, 0x9b1c,
  0x4ebf, 0x9b18, 0x4eba, 0x9b14, 0x4eb6, 0x9b10, 0x4eb1, 0x9b0c,
  0x4eac, 0x9b09, 0x4ea7, 0x9b05, 0x4ea2, 0x9b01, 0x4e9d, 0x9afd,
  0x4e98, 0x9af9, 0x4e93, 0x9af5, 0x4e8e, 0x9af1, 0x4e89, 0x9aed,
  0x4e84, 0x9aea, 0x4e7f, 0x9ae6, 0x4e7a, 0x9ae2, 0x4e75, 0x9ade,
  0x4e70, 0x9ada, 0x4e6b, 0x9ad6, 0x4e66, 0x9ad3, 0x4e61, 0x9acf,
  0x4e5c, 0x9acb, 0x4e57, 0x9ac7, 0x4e52, 0x9ac3, 0x4e4d, 0x9abf,
  0x4e48, 0x9abb, 0x4e43, 0x9ab8, 0x4e3e, 0x9ab4, 0x4e39, 0x9ab0,
  0x4e34, 0x9aac, 0x4e2f, 0x9aa8, 0x4e2a, 0x9aa4, 0x4e26, 0x9aa1,
  0x4e21, 0x9a9d, 0x4e1c, 0x9a99, 0x4e17, 0x9a95, 0x4e12, 0x9a91,
  0x4e0d, 0x9a8d, 0x4e08, 0x9a8a, 0x4e03, 0x9a86, 0x4dfe, 0x9a82,
  0x4df9, 0x9a7e, 0x4df4, 0x9a7a, 0x4def, 0x9a76, 0x4dea, 0x9a73,
  0x4de5, 0x9a6f, 0x4de0, 0x9a6b, 0x4ddb, 0x9a67, 0x4dd6, 0x9a63,
  0x4dd1, 0x9a60, 0x4dcc, 0x9a5c, 0x4dc7, 0x9a58, 0x4dc2, 0x9a54,
  0x4dbd, 0x9a50, 0x4db8, 0x9a4c, 0x4db3, 0x9a49, 0x4dae, 0x9a45,
  0x4da9, 0x9a41, 0x4da4, 0x9a3d, 0x4d9f, 0x9a39, 0x4d9a, 0x9a36,
  0x4d95, 0x9a32, 0x4d90, 0x9a2e, 0x4d8b, 0x9a2a, 0x4d86, 0x9a26,
  0x4d81, 0x9a23, 0x4d7c, 0x9a1f, 0x4d77, 0x9a1b, 0x4d72, 0x9a17,
  0x4d6d, 0x9a13, 0x4d68, 0x9a10, 0x4d63, 0x9a0c, 0x4d5e, 0x9a08,
  0x4d59, 0x9a04, 0x4d54, 0x9a00, 0x4d4f, 0x99fd, 0x4d4a, 0x99f9,
  0x4d45, 0x99f5, 0x4d40, 0x99f1, 0x4d3b, 0x99ed, 0x4d36, 0x99ea,
  0x4d31, 0x99e6, 0x4d2c, 0x99e2, 0x4d27, 0x99de, 0x4d22, 0x99da,
  0x4d1d, 0x99d7, 0x4d18, 0x99d3, 0x4d13, 0x99cf, 0x4d0e, 0x99cb,
  0x4d09, 0x99c7, 0x4d04, 0x99c4, 0x4cff, 0x99c0, 0x4cfa, 0x99bc,
  0x4cf5, 0x99b8, 0x4cf0, 0x99b5, 0x4ceb, 0x99b1, 0x4ce6, 0x99ad,
  0x4ce1, 0x99a9, 0x4cdb, 0x99a5, 0x4cd6, 0x99a2, 0x4cd1, 0x999e,
  0x4ccc, 0x999a, 0x4cc7, 0x9996, 0x4cc2, 0x9993, 0x4cbd, 0x998f,
  0x4cb8, 0x998b, 0x4cb3, 0x9987, 0x4cae, 0x9984, 0x4ca9, 0x9980,
  0x4ca4, 0x997c, 0x4c9f, 0x9978, 0x4c9a, 0x9975, 0x4c95, 0x9971,
  0x4c90, 0x996d, 0x4c8b, 0x9969, 0x4c86, 0x9965, 0x4c81, 0x9962,
  0x4c7c, 0x995e, 0x4c77, 0x995a, 0x4c72, 0x9956, 0x4c6d, 0x9953,
  0x4c68, 0x994f, 0x4c63, 0x994b, 0x4c5e, 0x9947, 0x4c59, 0x9944,
  0x4c54, 0x9940, 0x4c4f, 0x993c, 0x4c49, 0x9938, 0x4c44, 0x9935,
  0x4c3f, 0x9931, 0x4c3a, 0x992d, 0x4c35, 0x992a, 0x4c30, 0x9926,
  0x4c2b, 0x9922, 0x4c26, 0x991e, 0x4c21, 0x991b, 0x4c1c, 0x9917,
  0x4c17, 0x9913, 0x4c12, 0x990f, 0x4c0d, 0x990c, 0x4c08, 0x9908,
  0x4c03, 0x9904, 0x4bfe, 0x9900, 0x4bf9, 0x98fd, 0x4bf4, 0x98f9,
  0x4bef, 0x98f5, 0x4be9, 0x98f2, 0x4be4, 0x98ee, 0x4bdf, 0x98ea,
  0x4bda, 0x98e6, 0x4bd5, 0x98e3, 0x4bd0, 0x98df, 0x4bcb, 0x98db,
  0x4bc6, 0x98d7, 0x4bc1, 0x98d4, 0x4bbc, 0x98d0, 0x4bb7, 0x98cc,
  0x4bb2, 0x98c9, 0x4bad, 0x98c5, 0x4ba8, 0x98c1, 0x4ba3, 0x98bd,
  0x4b9e, 0x98ba, 0x4b98, 0x98b6, 0x4b93, 0x98b2, 0x4b8e, 0x98af,
  0x4b89, 0x98ab, 0x4b84, 0x98a7, 0x4b7f, 0x98a3, 0x4b7a, 0x98a0,
  0x4b75, 0x989c, 0x4b70, 0x9898, 0x4b6b, 0x9895, 0x4b66, 0x9891,
  0x4b61, 0x988d, 0x4b5c, 0x988a, 0x4b56, 0x9886, 0x4b51, 0x9882,
  0x4b4c, 0x987e, 0x4b47, 0x987b, 0x4b42, 0x9877, 0x4b3d, 0x9873,
  0x4b38, 0x9870, 0x4b33, 0x986c, 0x4b2e, 0x9868, 0x4b29, 0x9865,
  0x4b24, 0x9861, 0x4b1f, 0x985d, 0x4b19, 0x985a, 0x4b14, 0x9856,
  0x4b0f, 0x9852, 0x4b0a, 0x984e, 0x4b05, 0x984b, 0x4b00, 0x9847,
  0x4afb, 0x9843, 0x4af6, 0x9840, 0x4af1, 0x983c, 0x4aec, 0x9838,
  0x4ae7, 0x9835, 0x4ae1, 0x9831, 0x4adc, 0x982d, 0x4ad7, 0x982a,
  0x4ad2, 0x9826, 0x4acd, 0x9822, 0x4ac8, 0x981f, 0x4ac3, 0x981b,
  0x4abe, 0x9817, 0x4ab9, 0x9814, 0x4ab4, 0x9810, 0x4aae, 0x980c,
  0x4aa9, 0x9809, 0x4aa4, 0x9805, 0x4a9f, 0x9801, 0x4a9a, 0x97fe,
  0x4a95, 0x97fa, 0x4a90, 0x97f6, 0x4a8b, 0x97f3, 0x4a86, 0x97ef,
  0x4a81, 0x97eb, 0x4a7b, 0x97e8, 0x4a76, 0x97e4, 0x4a71, 0x97e0,
  0x4a6c, 0x97dd, 0x4a67, 0x97d9, 0x4a62, 0x97d5, 0x4a5d, 0x97d2,
  0x4a58, 0x97ce, 0x4a52, 0x97cb, 0x4a4d, 0x97c7, 0x4a48, 0x97c3,
  0x4a43, 0x97c0, 0x4a3e, 0x97bc, 0x4a39, 0x97b8, 0x4a34, 0x97b5,
  0x4a2f, 0x97b1, 0x4a2a, 0x97ad, 0x4a24, 0x97aa, 0x4a1f, 0x97a6,
  0x4a1a, 0x97a2, 0x4a15, 0x979f, 0x4a10, 0x979b, 0x4a0b, 0x9798,
  0x4a06, 0x9794, 0x4a01, 0x9790, 0x49fb, 0x978d, 0x49f6, 0x9789,
  0x49f1, 0x9785, 0x49ec, 0x9782, 0x49e7, 0x977e, 0x49e2, 0x977a,
  0x49dd, 0x9777, 0x49d8, 0x9773, 0x49d2, 0x9770, 0x49cd, 0x976c,
  0x49c8, 0x9768, 0x49c3, 0x9765, 0x49be, 0x9761, 0x49b9, 0x975d,
  0x49b4, 0x975a, 0x49ae, 0x9756, 0x49a9, 0x9753, 0x49a4, 0x974f,
  0x499f, 0x974b, 0x499a, 0x9748, 0x4995, 0x9744, 0x4990, 0x9741,
  0x498a, 0x973d, 0x4985, 0x9739, 0x4980, 0x9736, 0x497b, 0x9732,
  0x4976, 0x972f, 0x4971, 0x972b, 0x496c, 0x9727, 0x4966, 0x9724,
  0x4961, 0x9720, 0x495c, 0x971d, 0x4957, 0x9719, 0x4952, 0x9715,
  0x494d, 0x9712, 0x4948, 0x970e, 0x4942, 0x970b, 0x493d, 0x9707,
  0x4938, 0x9703, 0x4933, 0x9700, 0x492e, 0x96fc, 0x4929, 0x96f9,
  0x4923, 0x96f5, 0x491e, 0x96f1, 0x4919, 0x96ee, 0x4914, 0x96ea,
  0x490f, 0x96e7, 0x490a, 0x96e3, 0x4905, 0x96df, 0x48ff, 0x96dc,
  0x48fa, 0x96d8, 0x48f5, 0x96d5, 0x48f0, 0x96d1, 0x48eb, 0x96ce,
  0x48e6, 0x96ca, 0x48e0, 0x96c6, 0x48db, 0x96c3, 0x48d6, 0x96bf,
  0x48d1, 0x96bc, 0x48cc, 0x96b8, 0x48c7, 0x96b5, 0x48c1, 0x96b1,
  0x48bc, 0x96ad, 0x48b7, 0x96aa, 0x48b2, 0x96a6, 0x48ad, 0x96a3,
  0x48a8, 0x969f, 0x48a2, 0x969c, 0x489d, 0x9698, 0x4898, 0x9694,
  0x4893, 0x9691, 0x488e, 0x968d, 0x4888, 0x968a, 0x4883, 0x9686,
  0x487e, 0x9683, 0x4879, 0x967f, 0x4874, 0x967b, 0x486f, 0x9678,
  0x4869, 0x9674, 0x4864, 0x9671, 0x485f, 0x966d, 0x485a, 0x966a,
  0x4855, 0x9666, 0x484f, 0x9663, 0x484a, 0x965f, 0x4845, 0x965b,
  0x4840, 0x9658, 0x483b, 0x9654, 0x4836, 0x9651, 0x4830, 0x964d,
  0x482b, 0x964a, 0x4826, 0x9646, 0x4821, 0x9643, 0x481c, 0x963f,
  0x4816, 0x963c, 0x4811, 0x9638, 0x480c, 0x9635, 0x4807, 0x9631,
  0x4802, 0x962d, 0x47fc, 0x962a, 0x47f7, 0x9626, 0x47f2, 0x9623,
  0x47ed, 0x961f, 0x47e8, 0x961c, 0x47e2, 0x9618, 0x47dd, 0x9615,
  0x47d8, 0x9611, 0x47d3, 0x960e, 0x47ce, 0x960a, 0x47c8, 0x9607,
  0x47c3, 0x9603, 0x47be, 0x9600, 0x47b9, 0x95fc, 0x47b4, 0x95f9,
  0x47ae, 0x95f5, 0x47a9, 0x95f2, 0x47a4, 0x95ee, 0x479f, 0x95ea,
  0x479a, 0x95e7, 0x4794, 0x95e3, 0x478f, 0x95e0, 0x478a, 0x95dc,
  0x4785, 0x95d9, 0x4780, 0x95d5, 0x477a, 0x95d2, 0x4775, 0x95ce,
  0x4770, 0x95cb, 0x476b, 0x95c7, 0x4765, 0x95c4, 0x4760, 0x95c0,
  0x475b, 0x95bd, 0x4756, 0x95b9, 0x4751, 0x95b6, 0x474b, 0x95b2,
  0x4746, 0x95af, 0x4741, 0x95ab, 0x473c, 0x95a8, 0x4737, 0x95a4,
  0x4731, 0x95a1, 0x472c, 0x959d, 0x4727, 0x959a, 0x4722, 0x9596,
  0x471c, 0x9593, 0x4717, 0x958f, 0x4712, 0x958c, 0x470d, 0x9588,
  0x4708, 0x9585, 0x4702, 0x9581, 0x46fd, 0x957e, 0x46f8, 0x957a,
  0x46f3, 0x9577, 0x46ed, 0x9574, 0x46e8, 0x9570, 0x46e3, 0x956d,
  0x46de, 0x9569, 0x46d8, 0x9566, 0x46d3, 0x9562, 0x46ce, 0x955f,
  0x46c9, 0x955b, 0x46c4, 0x9558, 0x46be, 0x9554, 0x46b9, 0x9551,
  0x46b4, 0x954d, 0x46af, 0x954a, 0x46a9, 0x9546, 0x46a4, 0x9543,
  0x469f, 0x953f, 0x469a, 0x953c, 0x4694, 0x9538, 0x468f, 0x9535,
  0x468a, 0x9532, 0x4685, 0x952e, 0x467f, 0x952b, 0x467a, 0x9527,
  0x4675, 0x9524, 0x4670, 0x9520, 0x466a, 0x951d, 0x4665, 0x9519,
  0x4660, 0x9516, 0x465b, 0x9512, 0x4655, 0x950f, 0x4650, 0x950c,
  0x464b, 0x9508, 0x4646, 0x9505, 0x4640, 0x9501, 0x463b, 0x94fe,
  0x4636, 0x94fa, 0x4631, 0x94f7, 0x462b, 0x94f3, 0x4626, 0x94f0,
  0x4621, 0x94ed, 0x461c, 0x94e9, 0x4616, 0x94e6, 0x4611, 0x94e2,
  0x460c, 0x94df, 0x4607, 0x94db, 0x4601, 0x94d8, 0x45fc, 0x94d4,
  0x45f7, 0x94d1, 0x45f2, 0x94ce, 0x45ec, 0x94ca, 0x45e7, 0x94c7,
  0x45e2, 0x94c3, 0x45dd, 0x94c0, 0x45d7, 0x94bc, 0x45d2, 0x94b9,
  0x45cd, 0x94b6, 0x45c7, 0x94b2, 0x45c2, 0x94af, 0x45bd, 0x94ab,
  0x45b8, 0x94a8, 0x45b2, 0x94a4, 0x45ad, 0x94a1, 0x45a8, 0x949e,
  0x45a3, 0x949a, 0x459d, 0x9497, 0x4598, 0x9493, 0x4593, 0x9490,
  0x458d, 0x948d, 0x4588, 0x9489, 0x4583, 0x9486, 0x457e, 0x9482,
  0x4578, 0x947f, 0x4573, 0x947b, 0x456e, 0x9478, 0x4569, 0x9475,
  0x4563, 0x9471, 0x455e, 0x946e, 0x4559, 0x946a, 0x4553, 0x9467,
  0x454e, 0x9464, 0x4549, 0x9460, 0x4544, 0x945d, 0x453e, 0x9459,
  0x4539, 0x9456, 0x4534, 0x9453, 0x452e, 0x944f, 0x4529, 0x944c,
  0x4524, 0x9448, 0x451f, 0x9445, 0x4519, 0x9442, 0x4514, 0x943e,
  0x450f, 0x943b, 0x4509, 0x9437, 0x4504, 0x9434, 0x44ff, 0x9431,
  0x44fa, 0x942d, 0x44f4, 0x942a, 0x44ef, 0x9427, 0x44ea, 0x9423,
  0x44e4, 0x9420, 0x44df, 0x941c, 0x44da, 0x9419, 0x44d4, 0x9416,
  0x44cf, 0x9412, 0x44ca, 0x940f, 0x44c5, 0x940b, 0x44bf, 0x9408,
  0x44ba, 0x9405, 0x44b5, 0x9401, 0x44af, 0x93fe, 0x44aa, 0x93fb,
  0x44a5, 0x93f7, 0x449f, 0x93f4, 0x449a, 0x93f1, 0x4495, 0x93ed,
  0x4490, 0x93ea, 0x448a, 0x93e6, 0x4485, 0x93e3, 0x4480, 0x93e0,
  0x447a, 0x93dc, 0x4475, 0x93d9, 0x4470, 0x93d6, 0x446a, 0x93d2,
  0x4465, 0x93cf, 0x4460, 0x93cc, 0x445a, 0x93c8, 0x4455, 0x93c5,
  0x4450, 0x93c1, 0x444b, 0x93be, 0x4445, 0x93bb, 0x4440, 0x93b7,
  0x443b, 0x93b4, 0x4435, 0x93b1, 0x4430, 0x93ad, 0x442b, 0x93aa,
  0x4425, 0x93a7, 0x4420, 0x93a3, 0x441b, 0x93a0, 0x4415, 0x939d,
  0x4410, 0x9399, 0x440b, 0x9396, 0x4405, 0x9393, 0x4400, 0x938f,
  0x43fb, 0x938c, 0x43f5, 0x9389, 0x43f0, 0x9385, 0x43eb, 0x9382,
  0x43e5, 0x937f, 0x43e0, 0x937b, 0x43db, 0x9378, 0x43d5, 0x9375,
  0x43d0, 0x9371, 0x43cb, 0x936e, 0x43c5, 0x936b, 0x43c0, 0x9367,
  0x43bb, 0x9364, 0x43b5, 0x9361, 0x43b0, 0x935d, 0x43ab, 0x935a,
  0x43a5, 0x9357, 0x43a0, 0x9353, 0x439b, 0x9350, 0x4395, 0x934d,
  0x4390, 0x9349, 0x438b, 0x9346, 0x4385, 0x9343, 0x4380, 0x933f,
  0x437b, 0x933c, 0x4375, 0x9339, 0x4370, 0x9336, 0x436b, 0x9332,
  0x4365, 0x932f, 0x4360, 0x932c, 0x435b, 0x9328, 0x4355, 0x9325,
  0x4350, 0x9322, 0x434b, 0x931e, 0x4345, 0x931b, 0x4340, 0x9318,
  0x433b, 0x9314, 0x4335, 0x9311, 0x4330, 0x930e, 0x432b, 0x930b,
  0x4325, 0x9307, 0x4320, 0x9304, 0x431b, 0x9301, 0x4315, 0x92fd,
  0x4310, 0x92fa, 0x430b, 0x92f7, 0x4305, 0x92f4, 0x4300, 0x92f0,
  0x42fa, 0x92ed, 0x42f5, 0x92ea, 0x42f0, 0x92e6, 0x42ea, 0x92e3,
  0x42e5, 0x92e0, 0x42e0, 0x92dd, 0x42da, 0x92d9, 0x42d5, 0x92d6,
  0x42d0, 0x92d3, 0x42ca, 0x92cf, 0x42c5, 0x92cc, 0x42c0, 0x92c9,
  0x42ba, 0x92c6, 0x42b5, 0x92c2, 0x42af, 0x92bf, 0x42aa, 0x92bc,
  0x42a5, 0x92b8, 0x429f, 0x92b5, 0x429a, 0x92b2, 0x4295, 0x92af,
  0x428f, 0x92ab, 0x428a, 0x92a8, 0x4284, 0x92a5, 0x427f, 0x92a2,
  0x427a, 0x929e, 0x4274, 0x929b, 0x426f, 0x9298, 0x426a, 0x9295,
  0x4264, 0x9291, 0x425f, 0x928e, 0x425a, 0x928b, 0x4254, 0x9288,
  0x424f, 0x9284, 0x4249, 0x9281, 0x4244, 0x927e, 0x423f, 0x927b,
  0x4239, 0x9277, 0x4234, 0x9274, 0x422f, 0x9271, 0x4229, 0x926e,
  0x4224, 0x926a, 0x421e, 0x9267, 0x4219, 0x9264, 0x4214, 0x9261,
  0x420e, 0x925d, 0x4209, 0x925a, 0x4203, 0x9257, 0x41fe, 0x9254,
  0x41f9, 0x9250, 0x41f3, 0x924d, 0x41ee, 0x924a, 0x41e9, 0x9247,
  0x41e3, 0x9243, 0x41de, 0x9240, 0x41d8, 0x923d, 0x41d3, 0x923a,
  0x41ce, 0x9236, 0x41c8, 0x9233, 0x41c3, 0x9230, 0x41bd, 0x922d,
  0x41b8, 0x922a, 0x41b3, 0x9226, 0x41ad, 0x9223, 0x41a8, 0x9220,
  0x41a2, 0x921d, 0x419d, 0x9219, 0x4198, 0x9216, 0x4192, 0x9213,
  0x418d, 0x9210, 0x4188, 0x920d, 0x4182, 0x9209, 0x417d, 0x9206,
  0x4177, 0x9203, 0x4172, 0x9200, 0x416d, 0x91fc, 0x4167, 0x91f9,
  0x4162, 0x91f6, 0x415c, 0x91f3, 0x4157, 0x91f0, 0x4152, 0x91ec,
  0x414c, 0x91e9, 0x4147, 0x91e6, 0x4141, 0x91e3, 0x413c, 0x91e0,
  0x4136, 0x91dc, 0x4131, 0x91d9, 0x412c, 0x91d6, 0x4126, 0x91d3,
  0x4121, 0x91d0, 0x411b, 0x91cc, 0x4116, 0x91c9, 0x4111, 0x91c6,
  0x410b, 0x91c3, 0x4106, 0x91c0, 0x4100, 0x91bc, 0x40fb, 0x91b9,
  0x40f6, 0x91b6, 0x40f0, 0x91b3, 0x40eb, 0x91b0, 0x40e5, 0x91ad,
  0x40e0, 0x91a9, 0x40da, 0x91a6, 0x40d5, 0x91a3, 0x40d0, 0x91a0,
  0x40ca, 0x919d, 0x40c5, 0x9199, 0x40bf, 0x9196, 0x40ba, 0x9193,
  0x40b5, 0x9190, 0x40af, 0x918d, 0x40aa, 0x918a, 0x40a4, 0x9186,
  0x409f, 0x9183, 0x4099, 0x9180, 0x4094, 0x917d, 0x408f, 0x917a,
  0x4089, 0x9177, 0x4084, 0x9173, 0x407e, 0x9170, 0x4079, 0x916d,
  0x4073, 0x916a, 0x406e, 0x9167, 0x4069, 0x9164, 0x4063, 0x9160,
  0x405e, 0x915d, 0x4058, 0x915a, 0x4053, 0x9157, 0x404d, 0x9154,
  0x4048, 0x9151, 0x4043, 0x914d, 0x403d, 0x914a, 0x4038, 0x9147,
  0x4032, 0x9144, 0x402d, 0x9141, 0x4027, 0x913e, 0x4022, 0x913a,
  0x401d, 0x9137, 0x4017, 0x9134, 0x4012, 0x9131, 0x400c, 0x912e,
  0x4007, 0x912b, 0x4001, 0x9128, 0x3ffc, 0x9124, 0x3ff6, 0x9121,
  0x3ff1, 0x911e, 0x3fec, 0x911b, 0x3fe6, 0x9118, 0x3fe1, 0x9115,
  0x3fdb, 0x9112, 0x3fd6, 0x910f, 0x3fd0, 0x910b, 0x3fcb, 0x9108,
  0x3fc5, 0x9105, 0x3fc0, 0x9102, 0x3fbb, 0x90ff, 0x3fb5, 0x90fc,
  0x3fb0, 0x90f9, 0x3faa, 0x90f5, 0x3fa5, 0x90f2, 0x3f9f, 0x90ef,
  0x3f9a, 0x90ec, 0x3f94, 0x90e9, 0x3f8f, 0x90e6, 0x3f89, 0x90e3,
  0x3f84, 0x90e0, 0x3f7f, 0x90dd, 0x3f79, 0x90d9, 0x3f74, 0x90d6,
  0x3f6e, 0x90d3, 0x3f69, 0x90d0, 0x3f63, 0x90cd, 0x3f5e, 0x90ca,
  0x3f58, 0x90c7, 0x3f53, 0x90c4, 0x3f4d, 0x90c1, 0x3f48, 0x90bd,
  0x3f43, 0x90ba, 0x3f3d, 0x90b7, 0x3f38, 0x90b4, 0x3f32, 0x90b1,
  0x3f2d, 0x90ae, 0x3f27, 0x90ab, 0x3f22, 0x90a8, 0x3f1c, 0x90a5,
  0x3f17, 0x90a1, 0x3f11, 0x909e, 0x3f0c, 0x909b, 0x3f06, 0x9098,
  0x3f01, 0x9095, 0x3efb, 0x9092, 0x3ef6, 0x908f, 0x3ef1, 0x908c,
  0x3eeb, 0x9089, 0x3ee6, 0x9086, 0x3ee0, 0x9083, 0x3edb, 0x907f,
  0x3ed5, 0x907c, 0x3ed0, 0x9079, 0x3eca, 0x9076, 0x3ec5, 0x9073,
  0x3ebf, 0x9070, 0x3eba, 0x906d, 0x3eb4, 0x906a, 0x3eaf, 0x9067,
  0x3ea9, 0x9064, 0x3ea4, 0x9061, 0x3e9e, 0x905e, 0x3e99, 0x905b,
  0x3e93, 0x9057, 0x3e8e, 0x9054, 0x3e88, 0x9051, 0x3e83, 0x904e,
  0x3e7d, 0x904b, 0x3e78, 0x9048, 0x3e73, 0x9045, 0x3e6d, 0x9042,
  0x3e68, 0x903f, 0x3e62, 0x903c, 0x3e5d, 0x9039, 0x3e57, 0x9036,
  0x3e52, 0x9033, 0x3e4c, 0x9030, 0x3e47, 0x902d, 0x3e41, 0x902a,
  0x3e3c, 0x9026, 0x3e36, 0x9023, 0x3e31, 0x9020, 0x3e2b, 0x901d,
  0x3e26, 0x901a, 0x3e20, 0x9017, 0x3e1b, 0x9014, 0x3e15, 0x9011,
  0x3e10, 0x900e, 0x3e0a, 0x900b, 0x3e05, 0x9008, 0x3dff, 0x9005,
  0x3dfa, 0x9002, 0x3df4, 0x8fff, 0x3def, 0x8ffc, 0x3de9, 0x8ff9,
  0x3de4, 0x8ff6, 0x3dde, 0x8ff3, 0x3dd9, 0x8ff0, 0x3dd3, 0x8fed,
  0x3dce, 0x8fea, 0x3dc8, 0x8fe7, 0x3dc3, 0x8fe3, 0x3dbd, 0x8fe0,
  0x3db8, 0x8fdd, 0x3db2, 0x8fda, 0x3dad, 0x8fd7, 0x3da7, 0x8fd4,
  0x3da2, 0x8fd1, 0x3d9c, 0x8fce, 0x3d97, 0x8fcb, 0x3d91, 0x8fc8,
  0x3d8c, 0x8fc5, 0x3d86, 0x8fc2, 0x3d81, 0x8fbf, 0x3d7b, 0x8fbc,
  0x3d76, 0x8fb9, 0x3d70, 0x8fb6, 0x3d6b, 0x8fb3, 0x3d65, 0x8fb0,
  0x3d60, 0x8fad, 0x3d5a, 0x8faa, 0x3d55, 0x8fa7, 0x3d4f, 0x8fa4,
  0x3d49, 0x8fa1, 0x3d44, 0x8f9e, 0x3d3e, 0x8f9b, 0x3d39, 0x8f98,
  0x3d33, 0x8f95, 0x3d2e, 0x8f92, 0x3d28, 0x8f8f, 0x3d23, 0x8f8c,
  0x3d1d, 0x8f89, 0x3d18, 0x8f86, 0x3d12, 0x8f83, 0x3d0d, 0x8f80,
  0x3d07, 0x8f7d, 0x3d02, 0x8f7a, 0x3cfc, 0x8f77, 0x3cf7, 0x8f74,
  0x3cf1, 0x8f71, 0x3cec, 0x8f6e, 0x3ce6, 0x8f6b, 0x3ce1, 0x8f68,
  0x3cdb, 0x8f65, 0x3cd6, 0x8f62, 0x3cd0, 0x8f5f, 0x3cca, 0x8f5c,
  0x3cc5, 0x8f59, 0x3cbf, 0x8f56, 0x3cba, 0x8f53, 0x3cb4, 0x8f50,
  0x3caf, 0x8f4d, 0x3ca9, 0x8f4a, 0x3ca4, 0x8f47, 0x3c9e, 0x8f44,
  0x3c99, 0x8f41, 0x3c93, 0x8f3e, 0x3c8e, 0x8f3b, 0x3c88, 0x8f38,
  0x3c83, 0x8f35, 0x3c7d, 0x8f32, 0x3c77, 0x8f2f, 0x3c72, 0x8f2d,
  0x3c6c, 0x8f2a, 0x3c67, 0x8f27, 0x3c61, 0x8f24, 0x3c5c, 0x8f21,
  0x3c56, 0x8f1e, 0x3c51, 0x8f1b, 0x3c4b, 0x8f18, 0x3c46, 0x8f15,
  0x3c40, 0x8f12, 0x3c3b, 0x8f0f, 0x3c35, 0x8f0c, 0x3c2f, 0x8f09,
  0x3c2a, 0x8f06, 0x3c24, 0x8f03, 0x3c1f, 0x8f00, 0x3c19, 0x8efd,
  0x3c14, 0x8efa, 0x3c0e, 0x8ef7, 0x3c09, 0x8ef4, 0x3c03, 0x8ef1,
  0x3bfd, 0x8eee, 0x3bf8, 0x8eec, 0x3bf2, 0x8ee9, 0x3bed, 0x8ee6,
  0x3be7, 0x8ee3, 0x3be2, 0x8ee0, 0x3bdc, 0x8edd, 0x3bd7, 0x8eda,
  0x3bd1, 0x8ed7, 0x3bcc, 0x8ed4, 0x3bc6, 0x8ed1, 0x3bc0, 0x8ece,
  0x3bbb, 0x8ecb, 0x3bb5, 0x8ec8, 0x3bb0, 0x8ec5, 0x3baa, 0x8ec2,
  0x3ba5, 0x8ebf, 0x3b9f, 0x8ebd, 0x3b99, 0x8eba, 0x3b94, 0x8eb7,
  0x3b8e, 0x8eb4, 0x3b89, 0x8eb1, 0x3b83, 0x8eae, 0x3b7e, 0x8eab,
  0x3b78, 0x8ea8, 0x3b73, 0x8ea5, 0x3b6d, 0x8ea2, 0x3b67, 0x8e9f,
  0x3b62, 0x8e9c, 0x3b5c, 0x8e99, 0x3b57, 0x8e97, 0x3b51, 0x8e94,
  0x3b4c, 0x8e91, 0x3b46, 0x8e8e, 0x3b40, 0x8e8b, 0x3b3b, 0x8e88,
  0x3b35, 0x8e85, 0x3b30, 0x8e82, 0x3b2a, 0x8e7f, 0x3b25, 0x8e7c,
  0x3b1f, 0x8e7a, 0x3b19, 0x8e77, 0x3b14, 0x8e74, 0x3b0e, 0x8e71,
  0x3b09, 0x8e6e, 0x3b03, 0x8e6b, 0x3afe, 0x8e68, 0x3af8, 0x8e65,
  0x3af2, 0x8e62, 0x3aed, 0x8e5f, 0x3ae7, 0x8e5d, 0x3ae2, 0x8e5a,
  0x3adc, 0x8e57, 0x3ad7, 0x8e54, 0x3ad1, 0x8e51, 0x3acb, 0x8e4e,
  0x3ac6, 0x8e4b, 0x3ac0, 0x8e48, 0x3abb, 0x8e45, 0x3ab5, 0x8e43,
  0x3aaf, 0x8e40, 0x3aaa, 0x8e3d, 0x3aa4, 0x8e3a, 0x3a9f, 0x8e37,
  0x3a99, 0x8e34, 0x3a94, 0x8e31, 0x3a8e, 0x8e2e, 0x3a88, 0x8e2c,
  0x3a83, 0x8e29, 0x3a7d, 0x8e26, 0x3a78, 0x8e23, 0x3a72, 0x8e20,
  0x3a6c, 0x8e1d, 0x3a67, 0x8e1a, 0x3a61, 0x8e17, 0x3a5c, 0x8e15,
  0x3a56, 0x8e12, 0x3a50, 0x8e0f, 0x3a4b, 0x8e0c, 0x3a45, 0x8e09,
  0x3a40, 0x8e06, 0x3a3a, 0x8e03, 0x3a34, 0x8e01, 0x3a2f, 0x8dfe,
  0x3a29, 0x8dfb, 0x3a24, 0x8df8, 0x3a1e, 0x8df5, 0x3a19, 0x8df2,
  0x3a13, 0x8def, 0x3a0d, 0x8ded, 0x3a08, 0x8dea, 0x3a02, 0x8de7,
  0x39fd, 0x8de4, 0x39f7, 0x8de1, 0x39f1, 0x8dde, 0x39ec, 0x8ddc,
  0x39e6, 0x8dd9, 0x39e0, 0x8dd6, 0x39db, 0x8dd3, 0x39d5, 0x8dd0,
  0x39d0, 0x8dcd, 0x39ca, 0x8dca, 0x39c4, 0x8dc8, 0x39bf, 0x8dc5,
  0x39b9, 0x8dc2, 0x39b4, 0x8dbf, 0x39ae, 0x8dbc, 0x39a8, 0x8db9,
  0x39a3, 0x8db7, 0x399d, 0x8db4, 0x3998, 0x8db1, 0x3992, 0x8dae,
  0x398c, 0x8dab, 0x3987, 0x8da9, 0x3981, 0x8da6, 0x397c, 0x8da3,
  0x3976, 0x8da0, 0x3970, 0x8d9d, 0x396b, 0x8d9a, 0x3965, 0x8d98,
  0x395f, 0x8d95, 0x395a, 0x8d92, 0x3954, 0x8d8f, 0x394f, 0x8d8c,
  0x3949, 0x8d8a, 0x3943, 0x8d87, 0x393e, 0x8d84, 0x3938, 0x8d81,
  0x3932, 0x8d7e, 0x392d, 0x8d7b, 0x3927, 0x8d79, 0x3922, 0x8d76,
  0x391c, 0x8d73, 0x3916, 0x8d70, 0x3911, 0x8d6d, 0x390b, 0x8d6b,
  0x3906, 0x8d68, 0x3900, 0x8d65, 0x38fa, 0x8d62, 0x38f5, 0x8d5f,
  0x38ef, 0x8d5d, 0x38e9, 0x8d5a, 0x38e4, 0x8d57, 0x38de, 0x8d54,
  0x38d8, 0x8d51, 0x38d3, 0x8d4f, 0x38cd, 0x8d4c, 0x38c8, 0x8d49,
  0x38c2, 0x8d46, 0x38bc, 0x8d44, 0x38b7, 0x8d41, 0x38b1, 0x8d3e,
  0x38ab, 0x8d3b, 0x38a6, 0x8d38, 0x38a0, 0x8d36, 0x389b, 0x8d33,
  0x3895, 0x8d30, 0x388f, 0x8d2d, 0x388a, 0x8d2b, 0x3884, 0x8d28,
  0x387e, 0x8d25, 0x3879, 0x8d22, 0x3873, 0x8d1f, 0x386d, 0x8d1d,
  0x3868, 0x8d1a, 0x3862, 0x8d17, 0x385d, 0x8d14, 0x3857, 0x8d12,
  0x3851, 0x8d0f, 0x384c, 0x8d0c, 0x3846, 0x8d09, 0x3840, 0x8d07,
  0x383b, 0x8d04, 0x3835, 0x8d01, 0x382f, 0x8cfe, 0x382a, 0x8cfb,
  0x3824, 0x8cf9, 0x381e, 0x8cf6, 0x3819, 0x8cf3, 0x3813, 0x8cf0,
  0x380d, 0x8cee, 0x3808, 0x8ceb, 0x3802, 0x8ce8, 0x37fd, 0x8ce5,
  0x37f7, 0x8ce3, 0x37f1, 0x8ce0, 0x37ec, 0x8cdd, 0x37e6, 0x8cda,
  0x37e0, 0x8cd8, 0x37db, 0x8cd5, 0x37d5, 0x8cd2, 0x37cf, 0x8cd0,
  0x37ca, 0x8ccd, 0x37c4, 0x8cca, 0x37be, 0x8cc7, 0x37b9, 0x8cc5,
  0x37b3, 0x8cc2, 0x37ad, 0x8cbf, 0x37a8, 0x8cbc, 0x37a2, 0x8cba,
  0x379c, 0x8cb7, 0x3797, 0x8cb4, 0x3791, 0x8cb1, 0x378b, 0x8caf,
  0x3786, 0x8cac, 0x3780, 0x8ca9, 0x377a, 0x8ca7, 0x3775, 0x8ca4,
  0x376f, 0x8ca1, 0x3769, 0x8c9e, 0x3764, 0x8c9c, 0x375e, 0x8c99,
  0x3758, 0x8c96, 0x3753, 0x8c94, 0x374d, 0x8c91, 0x3747, 0x8c8e,
  0x3742, 0x8c8b, 0x373c, 0x8c89, 0x3736, 0x8c86, 0x3731, 0x8c83,
  0x372b, 0x8c81, 0x3725, 0x8c7e, 0x3720, 0x8c7b, 0x371a, 0x8c78,
  0x3714, 0x8c76, 0x370f, 0x8c73, 0x3709, 0x8c70, 0x3703, 0x8c6e,
  0x36fe, 0x8c6b, 0x36f8, 0x8c68, 0x36f2, 0x8c65, 0x36ed, 0x8c63,
  0x36e7, 0x8c60, 0x36e1, 0x8c5d, 0x36dc, 0x8c5b, 0x36d6, 0x8c58,
  0x36d0, 0x8c55, 0x36cb, 0x8c53, 0x36c5, 0x8c50, 0x36bf, 0x8c4d,
  0x36ba, 0x8c4b, 0x36b4, 0x8c48, 0x36ae, 0x8c45, 0x36a9, 0x8c43,
  0x36a3, 0x8c40, 0x369d, 0x8c3d, 0x3698, 0x8c3a, 0x3692, 0x8c38,
  0x368c, 0x8c35, 0x3686, 0x8c32, 0x3681, 0x8c30, 0x367b, 0x8c2d,
  0x3675, 0x8c2a, 0x3670, 0x8c28, 0x366a, 0x8c25, 0x3664, 0x8c22,
  0x365f, 0x8c20, 0x3659, 0x8c1d, 0x3653, 0x8c1a, 0x364e, 0x8c18,
  0x3648, 0x8c15, 0x3642, 0x8c12, 0x363d, 0x8c10, 0x3637, 0x8c0d,
  0x3631, 0x8c0a, 0x362b, 0x8c08, 0x3626, 0x8c05, 0x3620, 0x8c02,
  0x361a, 0x8c00, 0x3615, 0x8bfd, 0x360f, 0x8bfa, 0x3609, 0x8bf8,
  0x3604, 0x8bf5, 0x35fe, 0x8bf3, 0x35f8, 0x8bf0, 0x35f3, 0x8bed,
  0x35ed, 0x8beb, 0x35e7, 0x8be8, 0x35e1, 0x8be5, 0x35dc, 0x8be3,
  0x35d6, 0x8be0, 0x35d0, 0x8bdd, 0x35cb, 0x8bdb, 0x35c5, 0x8bd8,
  0x35bf, 0x8bd5, 0x35ba, 0x8bd3, 0x35b4, 0x8bd0, 0x35ae, 0x8bce,
  0x35a8, 0x8bcb, 0x35a3, 0x8bc8, 0x359d, 0x8bc6, 0x3597, 0x8bc3,
  0x3592, 0x8bc0, 0x358c, 0x8bbe, 0x3586, 0x8bbb, 0x3580, 0x8bb8,
  0x357b, 0x8bb6, 0x3575, 0x8bb3, 0x356f, 0x8bb1, 0x356a, 0x8bae,
  0x3564, 0x8bab, 0x355e, 0x8ba9, 0x3558, 0x8ba6, 0x3553, 0x8ba4,
  0x354d, 0x8ba1, 0x3547, 0x8b9e, 0x3542, 0x8b9c, 0x353c, 0x8b99,
  0x3536, 0x8b96, 0x3530, 0x8b94, 0x352b, 0x8b91, 0x3525, 0x8b8f,
  0x351f, 0x8b8c, 0x351a, 0x8b89, 0x3514, 0x8b87, 0x350e, 0x8b84,
  0x3508, 0x8b82, 0x3503, 0x8b7f, 0x34fd, 0x8b7c, 0x34f7, 0x8b7a,
  0x34f2, 0x8b77, 0x34ec, 0x8b75, 0x34e6, 0x8b72, 0x34e0, 0x8b6f,
  0x34db, 0x8b6d, 0x34d5, 0x8b6a, 0x34cf, 0x8b68, 0x34ca, 0x8b65,
  0x34c4, 0x8b62, 0x34be, 0x8b60, 0x34b8, 0x8b5d, 0x34b3, 0x8b5b,
  0x34ad, 0x8b58, 0x34a7, 0x8b55, 0x34a1, 0x8b53, 0x349c, 0x8b50,
  0x3496, 0x8b4e, 0x3490, 0x8b4b, 0x348b, 0x8b49, 0x3485, 0x8b46,
  0x347f, 0x8b43, 0x3479, 0x8b41, 0x3474, 0x8b3e, 0x346e, 0x8b3c,
  0x3468, 0x8b39, 0x3462, 0x8b37, 0x345d, 0x8b34, 0x3457, 0x8b31,
  0x3451, 0x8b2f, 0x344b, 0x8b2c, 0x3446, 0x8b2a, 0x3440, 0x8b27,
  0x343a, 0x8b25, 0x3435, 0x8b22, 0x342f, 0x8b1f, 0x3429, 0x8b1d,
  0x3423, 0x8b1a, 0x341e, 0x8b18, 0x3418, 0x8b15, 0x3412, 0x8b13,
  0x340c, 0x8b10, 0x3407, 0x8b0e, 0x3401, 0x8b0b, 0x33fb, 0x8b08,
  0x33f5, 0x8b06, 0x33f0, 0x8b03, 0x33ea, 0x8b01, 0x33e4, 0x8afe,
  0x33de, 0x8afc, 0x33d9, 0x8af9, 0x33d3, 0x8af7, 0x33cd, 0x8af4,
  0x33c7, 0x8af1, 0x33c2, 0x8aef, 0x33bc, 0x8aec, 0x33b6, 0x8aea,
  0x33b0, 0x8ae7, 0x33ab, 0x8ae5, 0x33a5, 0x8ae2, 0x339f, 0x8ae0,
  0x3399, 0x8add, 0x3394, 0x8adb, 0x338e, 0x8ad8, 0x3388, 0x8ad6,
  0x3382, 0x8ad3, 0x337d, 0x8ad1, 0x3377, 0x8ace, 0x3371, 0x8acb,
  0x336b, 0x8ac9, 0x3366, 0x8ac6, 0x3360, 0x8ac4, 0x335a, 0x8ac1,
  0x3354, 0x8abf, 0x334f, 0x8abc, 0x3349, 0x8aba, 0x3343, 0x8ab7,
  0x333d, 0x8ab5, 0x3338, 0x8ab2, 0x3332, 0x8ab0, 0x332c, 0x8aad,
  0x3326, 0x8aab, 0x3321, 0x8aa8, 0x331b, 0x8aa6, 0x3315, 0x8aa3,
  0x330f, 0x8aa1, 0x330a, 0x8a9e, 0x3304, 0x8a9c, 0x32fe, 0x8a99,
  0x32f8, 0x8a97, 0x32f3, 0x8a94, 0x32ed, 0x8a92, 0x32e7, 0x8a8f,
  0x32e1, 0x8a8d, 0x32db, 0x8a8a, 0x32d6, 0x8a88, 0x32d0, 0x8a85,
  0x32ca, 0x8a83, 0x32c4, 0x8a80, 0x32bf, 0x8a7e, 0x32b9, 0x8a7b,
  0x32b3, 0x8a79, 0x32ad, 0x8a76, 0x32a8, 0x8a74, 0x32a2, 0x8a71,
  0x329c, 0x8a6f, 0x3296, 0x8a6c, 0x3290, 0x8a6a, 0x328b, 0x8a67,
  0x3285, 0x8a65, 0x327f, 0x8a62, 0x3279, 0x8a60, 0x3274, 0x8a5d,
  0x326e, 0x8a5b, 0x3268, 0x8a59, 0x3262, 0x8a56, 0x325d, 0x8a54,
  0x3257, 0x8a51, 0x3251, 0x8a4f, 0x324b, 0x8a4c, 0x3245, 0x8a4a,
  0x3240, 0x8a47, 0x323a, 0x8a45, 0x3234, 0x8a42, 0x322e, 0x8a40,
  0x3228, 0x8a3d, 0x3223, 0x8a3b, 0x321d, 0x8a38, 0x3217, 0x8a36,
  0x3211, 0x8a34, 0x320c, 0x8a31, 0x3206, 0x8a2f, 0x3200, 0x8a2c,
  0x31fa, 0x8a2a, 0x31f4, 0x8a27, 0x31ef, 0x8a25, 0x31e9, 0x8a22,
  0x31e3, 0x8a20, 0x31dd, 0x8a1d, 0x31d8, 0x8a1b, 0x31d2, 0x8a19,
  0x31cc, 0x8a16, 0x31c6, 0x8a14, 0x31c0, 0x8a11, 0x31bb, 0x8a0f,
  0x31b5, 0x8a0c, 0x31af, 0x8a0a, 0x31a9, 0x8a07, 0x31a3, 0x8a05,
  0x319e, 0x8a03, 0x3198, 0x8a00, 0x3192, 0x89fe, 0x318c, 0x89fb,
  0x3186, 0x89f9, 0x3181, 0x89f6, 0x317b, 0x89f4, 0x3175, 0x89f2,
  0x316f, 0x89ef, 0x3169, 0x89ed, 0x3164, 0x89ea, 0x315e, 0x89e8,
  0x3158, 0x89e5, 0x3152, 0x89e3, 0x314c, 0x89e1, 0x3147, 0x89de,
  0x3141, 0x89dc, 0x313b, 0x89d9, 0x3135, 0x89d7, 0x312f, 0x89d5,
  0x312a, 0x89d2, 0x3124, 0x89d0, 0x311e, 0x89cd, 0x3118, 0x89cb,
  0x3112, 0x89c8, 0x310d, 0x89c6, 0x3107, 0x89c4, 0x3101, 0x89c1,
  0x30fb, 0x89bf, 0x30f5, 0x89bc, 0x30f0, 0x89ba, 0x30ea, 0x89b8,
  0x30e4, 0x89b5, 0x30de, 0x89b3, 0x30d8, 0x89b0, 0x30d3, 0x89ae,
  0x30cd, 0x89ac, 0x30c7, 0x89a9, 0x30c1, 0x89a7, 0x30bb, 0x89a4,
  0x30b6, 0x89a2, 0x30b0, 0x89a0, 0x30aa, 0x899d, 0x30a4, 0x899b,
  0x309e, 0x8998, 0x3099, 0x8996, 0x3093, 0x8994, 0x308d, 0x8991,
  0x3087, 0x898f, 0x3081, 0x898d, 0x307b, 0x898a, 0x3076, 0x8988,
  0x3070, 0x8985, 0x306a, 0x8983, 0x3064, 0x8981, 0x305e, 0x897e,
  0x3059, 0x897c, 0x3053, 0x897a, 0x304d, 0x8977, 0x3047, 0x8975,
  0x3041, 0x8972, 0x303b, 0x8970, 0x3036, 0x896e, 0x3030, 0x896b,
  0x302a, 0x8969, 0x3024, 0x8967, 0x301e, 0x8964, 0x3019, 0x8962,
  0x3013, 0x8960, 0x300d, 0x895d, 0x3007, 0x895b, 0x3001, 0x8958,
  0x2ffb, 0x8956, 0x2ff6, 0x8954, 0x2ff0, 0x8951, 0x2fea, 0x894f,
  0x2fe4, 0x894d, 0x2fde, 0x894a, 0x2fd8, 0x8948, 0x2fd3, 0x8946,
  0x2fcd, 0x8943, 0x2fc7, 0x8941, 0x2fc1, 0x893f, 0x2fbb, 0x893c,
  0x2fb5, 0x893a, 0x2fb0, 0x8938, 0x2faa, 0x8935, 0x2fa4, 0x8933,
  0x2f9e, 0x8931, 0x2f98, 0x892e, 0x2f92, 0x892c, 0x2f8d, 0x892a,
  0x2f87, 0x8927, 0x2f81, 0x8925, 0x2f7b, 0x8923, 0x2f75, 0x8920,
  0x2f6f, 0x891e, 0x2f6a, 0x891c, 0x2f64, 0x8919, 0x2f5e, 0x8917,
  0x2f58, 0x8915, 0x2f52, 0x8912, 0x2f4c, 0x8910, 0x2f47, 0x890e,
  0x2f41, 0x890b, 0x2f3b, 0x8909, 0x2f35, 0x8907, 0x2f2f, 0x8904,
  0x2f29, 0x8902, 0x2f24, 0x8900, 0x2f1e, 0x88fd, 0x2f18, 0x88fb,
  0x2f12, 0x88f9, 0x2f0c, 0x88f6, 0x2f06, 0x88f4, 0x2f01, 0x88f2,
  0x2efb, 0x88f0, 0x2ef5, 0x88ed, 0x2eef, 0x88eb, 0x2ee9, 0x88e9,
  0x2ee3, 0x88e6, 0x2edd, 0x88e4, 0x2ed8, 0x88e2, 0x2ed2, 0x88df,
  0x2ecc, 0x88dd, 0x2ec6, 0x88db, 0x2ec0, 0x88d9, 0x2eba, 0x88d6,
  0x2eb5, 0x88d4, 0x2eaf, 0x88d2, 0x2ea9, 0x88cf, 0x2ea3, 0x88cd,
  0x2e9d, 0x88cb, 0x2e97, 0x88c8, 0x2e91, 0x88c6, 0x2e8c, 0x88c4,
  0x2e86, 0x88c2, 0x2e80, 0x88bf, 0x2e7a, 0x88bd, 0x2e74, 0x88bb,
  0x2e6e, 0x88b9, 0x2e68, 0x88b6, 0x2e63, 0x88b4, 0x2e5d, 0x88b2,
  0x2e57, 0x88af, 0x2e51, 0x88ad, 0x2e4b, 0x88ab, 0x2e45, 0x88a9,
  0x2e3f, 0x88a6, 0x2e3a, 0x88a4, 0x2e34, 0x88a2, 0x2e2e, 0x88a0,
  0x2e28, 0x889d, 0x2e22, 0x889b, 0x2e1c, 0x8899, 0x2e16, 0x8896,
  0x2e11, 0x8894, 0x2e0b, 0x8892, 0x2e05, 0x8890, 0x2dff, 0x888d,
  0x2df9, 0x888b, 0x2df3, 0x8889, 0x2ded, 0x8887, 0x2de7, 0x8884,
  0x2de2, 0x8882, 0x2ddc, 0x8880, 0x2dd6, 0x887e, 0x2dd0, 0x887b,
  0x2dca, 0x8879, 0x2dc4, 0x8877, 0x2dbe, 0x8875, 0x2db9, 0x8872,
  0x2db3, 0x8870, 0x2dad, 0x886e, 0x2da7, 0x886c, 0x2da1, 0x8869,
  0x2d9b, 0x8867, 0x2d95, 0x8865, 0x2d8f, 0x8863, 0x2d8a, 0x8860,
  0x2d84, 0x885e, 0x2d7e, 0x885c, 0x2d78, 0x885a, 0x2d72, 0x8858,
  0x2d6c, 0x8855, 0x2d66, 0x8853, 0x2d60, 0x8851, 0x2d5b, 0x884f,
  0x2d55, 0x884c, 0x2d4f, 0x884a, 0x2d49, 0x8848, 0x2d43, 0x8846,
  0x2d3d, 0x8844, 0x2d37, 0x8841, 0x2d31, 0x883f, 0x2d2c, 0x883d,
  0x2d26, 0x883b, 0x2d20, 0x8838, 0x2d1a, 0x8836, 0x2d14, 0x8834,
  0x2d0e, 0x8832, 0x2d08, 0x8830, 0x2d02, 0x882d, 0x2cfd, 0x882b,
  0x2cf7, 0x8829, 0x2cf1, 0x8827, 0x2ceb, 0x8825, 0x2ce5, 0x8822,
  0x2cdf, 0x8820, 0x2cd9, 0x881e, 0x2cd3, 0x881c, 0x2ccd, 0x881a,
  0x2cc8, 0x8817, 0x2cc2, 0x8815, 0x2cbc, 0x8813, 0x2cb6, 0x8811,
  0x2cb0, 0x880f, 0x2caa, 0x880c, 0x2ca4, 0x880a, 0x2c9e, 0x8808,
  0x2c98, 0x8806, 0x2c93, 0x8804, 0x2c8d, 0x8801, 0x2c87, 0x87ff,
  0x2c81, 0x87fd, 0x2c7b, 0x87fb, 0x2c75, 0x87f9, 0x2c6f, 0x87f6,
  0x2c69, 0x87f4, 0x2c63, 0x87f2, 0x2c5e, 0x87f0, 0x2c58, 0x87ee,
  0x2c52, 0x87ec, 0x2c4c, 0x87e9, 0x2c46, 0x87e7, 0x2c40, 0x87e5,
  0x2c3a, 0x87e3, 0x2c34, 0x87e1, 0x2c2e, 0x87df, 0x2c29, 0x87dc,
  0x2c23, 0x87da, 0x2c1d, 0x87d8, 0x2c17, 0x87d6, 0x2c11, 0x87d4,
  0x2c0b, 0x87d2, 0x2c05, 0x87cf, 0x2bff, 0x87cd, 0x2bf9, 0x87cb,
  0x2bf3, 0x87c9, 0x2bee, 0x87c7, 0x2be8, 0x87c5, 0x2be2, 0x87c2,
  0x2bdc, 0x87c0, 0x2bd6, 0x87be, 0x2bd0, 0x87bc, 0x2bca, 0x87ba,
  0x2bc4, 0x87b8, 0x2bbe, 0x87b6, 0x2bb8, 0x87b3, 0x2bb2, 0x87b1,
  0x2bad, 0x87af, 0x2ba7, 0x87ad, 0x2ba1, 0x87ab, 0x2b9b, 0x87a9,
  0x2b95, 0x87a7, 0x2b8f, 0x87a4, 0x2b89, 0x87a2, 0x2b83, 0x87a0,
  0x2b7d, 0x879e, 0x2b77, 0x879c, 0x2b71, 0x879a, 0x2b6c, 0x8798,
  0x2b66, 0x8795, 0x2b60, 0x8793, 0x2b5a, 0x8791, 0x2b54, 0x878f,
  0x2b4e, 0x878d, 0x2b48, 0x878b, 0x2b42, 0x8789, 0x2b3c, 0x8787,
  0x2b36, 0x8784, 0x2b30, 0x8782, 0x2b2b, 0x8780, 0x2b25, 0x877e,
  0x2b1f, 0x877c, 0x2b19, 0x877a, 0x2b13, 0x8778, 0x2b0d, 0x8776,
  0x2b07, 0x8774, 0x2b01, 0x8771, 0x2afb, 0x876f, 0x2af5, 0x876d,
  0x2aef, 0x876b, 0x2ae9, 0x8769, 0x2ae4, 0x8767, 0x2ade, 0x8765,
  0x2ad8, 0x8763, 0x2ad2, 0x8761, 0x2acc, 0x875e, 0x2ac6, 0x875c,
  0x2ac0, 0x875a, 0x2aba, 0x8758, 0x2ab4, 0x8756, 0x2aae, 0x8754,
  0x2aa8, 0x8752, 0x2aa2, 0x8750, 0x2a9c, 0x874e, 0x2a97, 0x874c,
  0x2a91, 0x874a, 0x2a8b, 0x8747, 0x2a85, 0x8745, 0x2a7f, 0x8743,
  0x2a79, 0x8741, 0x2a73, 0x873f, 0x2a6d, 0x873d, 0x2a67, 0x873b,
  0x2a61, 0x8739, 0x2a5b, 0x8737, 0x2a55, 0x8735, 0x2a4f, 0x8733,
  0x2a49, 0x8731, 0x2a44, 0x872e, 0x2a3e, 0x872c, 0x2a38, 0x872a,
  0x2a32, 0x8728, 0x2a2c, 0x8726, 0x2a26, 0x8724, 0x2a20, 0x8722,
  0x2a1a, 0x8720, 0x2a14, 0x871e, 0x2a0e, 0x871c, 0x2a08, 0x871a,
  0x2a02, 0x8718, 0x29fc, 0x8716, 0x29f6, 0x8714, 0x29f0, 0x8712,
  0x29eb, 0x870f, 0x29e5, 0x870d, 0x29df, 0x870b, 0x29d9, 0x8709,
  0x29d3, 0x8707, 0x29cd, 0x8705, 0x29c7, 0x8703, 0x29c1, 0x8701,
  0x29bb, 0x86ff, 0x29b5, 0x86fd, 0x29af, 0x86fb, 0x29a9, 0x86f9,
  0x29a3, 0x86f7, 0x299d, 0x86f5, 0x2997, 0x86f3, 0x2991, 0x86f1,
  0x298b, 0x86ef, 0x2986, 0x86ed, 0x2980, 0x86eb, 0x297a, 0x86e9,
  0x2974, 0x86e7, 0x296e, 0x86e4, 0x2968, 0x86e2, 0x2962, 0x86e0,
  0x295c, 0x86de, 0x2956, 0x86dc, 0x2950, 0x86da, 0x294a, 0x86d8,
  0x2944, 0x86d6, 0x293e, 0x86d4, 0x2938, 0x86d2, 0x2932, 0x86d0,
  0x292c, 0x86ce, 0x2926, 0x86cc, 0x2920, 0x86ca, 0x291b, 0x86c8,
  0x2915, 0x86c6, 0x290f, 0x86c4, 0x2909, 0x86c2, 0x2903, 0x86c0,
  0x28fd, 0x86be, 0x28f7, 0x86bc, 0x28f1, 0x86ba, 0x28eb, 0x86b8,
  0x28e5, 0x86b6, 0x28df, 0x86b4, 0x28d9, 0x86b2, 0x28d3, 0x86b0,
  0x28cd, 0x86ae, 0x28c7, 0x86ac, 0x28c1, 0x86aa, 0x28bb, 0x86a8,
  0x28b5, 0x86a6, 0x28af, 0x86a4, 0x28a9, 0x86a2, 0x28a3, 0x86a0,
  0x289d, 0x869e, 0x2898, 0x869c, 0x2892, 0x869a, 0x288c, 0x8698,
  0x2886, 0x8696, 0x2880, 0x8694, 0x287a, 0x8692, 0x2874, 0x8690,
  0x286e, 0x868e, 0x2868, 0x868c, 0x2862, 0x868a, 0x285c, 0x8688,
  0x2856, 0x8686, 0x2850, 0x8684, 0x284a, 0x8682, 0x2844, 0x8680,
  0x283e, 0x867e, 0x2838, 0x867c, 0x2832, 0x867a, 0x282c, 0x8678,
  0x2826, 0x8676, 0x2820, 0x8674, 0x281a, 0x8672, 0x2814, 0x8670,
  0x280e, 0x866e, 0x2808, 0x866d, 0x2802, 0x866b, 0x27fc, 0x8669,
  0x27f6, 0x8667, 0x27f1, 0x8665, 0x27eb, 0x8663, 0x27e5, 0x8661,
  0x27df, 0x865f, 0x27d9, 0x865d, 0x27d3, 0x865b, 0x27cd, 0x8659,
  0x27c7, 0x8657, 0x27c1, 0x8655, 0x27bb, 0x8653, 0x27b5, 0x8651,
  0x27af, 0x864f, 0x27a9, 0x864d, 0x27a3, 0x864b, 0x279d, 0x8649,
  0x2797, 0x8647, 0x2791, 0x8645, 0x278b, 0x8644, 0x2785, 0x8642,
  0x277f, 0x8640, 0x2779, 0x863e, 0x2773, 0x863c, 0x276d, 0x863a,
  0x2767, 0x8638, 0x2761, 0x8636, 0x275b, 0x8634, 0x2755, 0x8632,
  0x274f, 0x8630, 0x2749, 0x862e, 0x2743, 0x862c, 0x273d, 0x862a,
  0x2737, 0x8628, 0x2731, 0x8627, 0x272b, 0x8625, 0x2725, 0x8623,
  0x271f, 0x8621, 0x2719, 0x861f, 0x2713, 0x861d, 0x270d, 0x861b,
  0x2707, 0x8619, 0x2701, 0x8617, 0x26fb, 0x8615, 0x26f5, 0x8613,
  0x26ef, 0x8611, 0x26e9, 0x8610, 0x26e4, 0x860e, 0x26de, 0x860c,
  0x26d8, 0x860a, 0x26d2, 0x8608, 0x26cc, 0x8606, 0x26c6, 0x8604,
  0x26c0, 0x8602, 0x26ba, 0x8600, 0x26b4, 0x85fe, 0x26ae, 0x85fc,
  0x26a8, 0x85fb, 0x26a2, 0x85f9, 0x269c, 0x85f7, 0x2696, 0x85f5,
  0x2690, 0x85f3, 0x268a, 0x85f1, 0x2684, 0x85ef, 0x267e, 0x85ed,
  0x2678, 0x85eb, 0x2672, 0x85ea, 0x266c, 0x85e8, 0x2666, 0x85e6,
  0x2660, 0x85e4, 0x265a, 0x85e2, 0x2654, 0x85e0, 0x264e, 0x85de,
  0x2648, 0x85dc, 0x2642, 0x85da, 0x263c, 0x85d9, 0x2636, 0x85d7,
  0x2630, 0x85d5, 0x262a, 0x85d3, 0x2624, 0x85d1, 0x261e, 0x85cf,
  0x2618, 0x85cd, 0x2612, 0x85cb, 0x260c, 0x85ca, 0x2606, 0x85c8,
  0x2600, 0x85c6, 0x25fa, 0x85c4, 0x25f4, 0x85c2, 0x25ee, 0x85c0,
  0x25e8, 0x85be, 0x25e2, 0x85bd, 0x25dc, 0x85bb, 0x25d6, 0x85b9,
  0x25d0, 0x85b7, 0x25ca, 0x85b5, 0x25c4, 0x85b3, 0x25be, 0x85b1,
  0x25b8, 0x85b0, 0x25b2, 0x85ae, 0x25ac, 0x85ac, 0x25a6, 0x85aa,
  0x25a0, 0x85a8, 0x259a, 0x85a6, 0x2594, 0x85a4, 0x258e, 0x85a3,
  0x2588, 0x85a1, 0x2582, 0x859f, 0x257c, 0x859d, 0x2576, 0x859b,
  0x2570, 0x8599, 0x256a, 0x8598, 0x2564, 0x8596, 0x255e, 0x8594,
  0x2558, 0x8592, 0x2552, 0x8590, 0x254c, 0x858e, 0x2546, 0x858d,
  0x2540, 0x858b, 0x253a, 0x8589, 0x2534, 0x8587, 0x252e, 0x8585,
  0x2528, 0x8583, 0x2522, 0x8582, 0x251c, 0x8580, 0x2516, 0x857e,
  0x250f, 0x857c, 0x2509, 0x857a, 0x2503, 0x8579, 0x24fd, 0x8577,
  0x24f7, 0x8575, 0x24f1, 0x8573, 0x24eb, 0x8571, 0x24e5, 0x856f,
  0x24df, 0x856e, 0x24d9, 0x856c, 0x24d3, 0x856a, 0x24cd, 0x8568,
  0x24c7, 0x8566, 0x24c1, 0x8565, 0x24bb, 0x8563, 0x24b5, 0x8561,
  0x24af, 0x855f, 0x24a9, 0x855d, 0x24a3, 0x855c, 0x249d, 0x855a,
  0x2497, 0x8558, 0x2491, 0x8556, 0x248b, 0x8554, 0x2485, 0x8553,
  0x247f, 0x8551, 0x2479, 0x854f, 0x2473, 0x854d, 0x246d, 0x854b,
  0x2467, 0x854a, 0x2461, 0x8548, 0x245b, 0x8546, 0x2455, 0x8544,
  0x244f, 0x8543, 0x2449, 0x8541, 0x2443, 0x853f, 0x243d, 0x853d,
  0x2437, 0x853b, 0x2431, 0x853a, 0x242b, 0x8538, 0x2425, 0x8536,
  0x241f, 0x8534, 0x2419, 0x8533, 0x2413, 0x8531, 0x240d, 0x852f,
  0x2407, 0x852d, 0x2401, 0x852b, 0x23fa, 0x852a, 0x23f4, 0x8528,
  0x23ee, 0x8526, 0x23e8, 0x8524, 0x23e2, 0x8523, 0x23dc, 0x8521,
  0x23d6, 0x851f, 0x23d0, 0x851d, 0x23ca, 0x851c, 0x23c4, 0x851a,
  0x23be, 0x8518, 0x23b8, 0x8516, 0x23b2, 0x8515, 0x23ac, 0x8513,
  0x23a6, 0x8511, 0x23a0, 0x850f, 0x239a, 0x850e, 0x2394, 0x850c,
  0x238e, 0x850a, 0x2388, 0x8508, 0x2382, 0x8507, 0x237c, 0x8505,
  0x2376, 0x8503, 0x2370, 0x8501, 0x236a, 0x8500, 0x2364, 0x84fe,
  0x235e, 0x84fc, 0x2358, 0x84fa, 0x2352, 0x84f9, 0x234b, 0x84f7,
  0x2345, 0x84f5, 0x233f, 0x84f4, 0x2339, 0x84f2, 0x2333, 0x84f0,
  0x232d, 0x84ee, 0x2327, 0x84ed, 0x2321, 0x84eb, 0x231b, 0x84e9,
  0x2315, 0x84e7, 0x230f, 0x84e6, 0x2309, 0x84e4, 0x2303, 0x84e2,
  0x22fd, 0x84e1, 0x22f7, 0x84df, 0x22f1, 0x84dd, 0x22eb, 0x84db,
  0x22e5, 0x84da, 0x22df, 0x84d8, 0x22d9, 0x84d6, 0x22d3, 0x84d5,
  0x22cd, 0x84d3, 0x22c7, 0x84d1, 0x22c0, 0x84cf, 0x22ba, 0x84ce,
  0x22b4, 0x84cc, 0x22ae, 0x84ca, 0x22a8, 0x84c9, 0x22a2, 0x84c7,
  0x229c, 0x84c5, 0x2296, 0x84c4, 0x2290, 0x84c2, 0x228a, 0x84c0,
  0x2284, 0x84be, 0x227e, 0x84bd, 0x2278, 0x84bb, 0x2272, 0x84b9,
  0x226c, 0x84b8, 0x2266, 0x84b6, 0x2260, 0x84b4, 0x225a, 0x84b3,
  0x2254, 0x84b1, 0x224e, 0x84af, 0x2247, 0x84ae, 0x2241, 0x84ac,
  0x223b, 0x84aa, 0x2235, 0x84a9, 0x222f, 0x84a7, 0x2229, 0x84a5,
  0x2223, 0x84a3, 0x221d, 0x84a2, 0x2217, 0x84a0, 0x2211, 0x849e,
  0x220b, 0x849d, 0x2205, 0x849b, 0x21ff, 0x8499, 0x21f9, 0x8498,
  0x21f3, 0x8496, 0x21ed, 0x8494, 0x21e7, 0x8493, 0x21e1, 0x8491,
  0x21da, 0x848f, 0x21d4, 0x848e, 0x21ce, 0x848c, 0x21c8, 0x848a,
  0x21c2, 0x8489, 0x21bc, 0x8487, 0x21b6, 0x8486, 0x21b0, 0x8484,
  0x21aa, 0x8482, 0x21a4, 0x8481, 0x219e, 0x847f, 0x2198, 0x847d,
  0x2192, 0x847c, 0x218c, 0x847a, 0x2186, 0x8478, 0x2180, 0x8477,
  0x2179, 0x8475, 0x2173, 0x8473, 0x216d, 0x8472, 0x2167, 0x8470,
  0x2161, 0x846e, 0x215b, 0x846d, 0x2155, 0x846b, 0x214f, 0x846a,
  0x2149, 0x8468, 0x2143, 0x8466, 0x213d, 0x8465, 0x2137, 0x8463,
  0x2131, 0x8461, 0x212b, 0x8460, 0x2125, 0x845e, 0x211e, 0x845d,
  0x2118, 0x845b, 0x2112, 0x8459, 0x210c, 0x8458, 0x2106, 0x8456,
  0x2100, 0x8454, 0x20fa, 0x8453, 0x20f4, 0x8451, 0x20ee, 0x8450,
  0x20e8, 0x844e, 0x20e2, 0x844c, 0x20dc, 0x844b, 0x20d6, 0x8449,
  0x20d0, 0x8447, 0x20c9, 0x8446, 0x20c3, 0x8444, 0x20bd, 0x8443,
  0x20b7, 0x8441, 0x20b1, 0x843f, 0x20ab, 0x843e, 0x20a5, 0x843c,
  0x209f, 0x843b, 0x2099, 0x8439, 0x2093, 0x8437, 0x208d, 0x8436,
  0x2087, 0x8434, 0x2081, 0x8433, 0x207a, 0x8431, 0x2074, 0x842f,
  0x206e, 0x842e, 0x2068, 0x842c, 0x2062, 0x842b, 0x205c, 0x8429,
  0x2056, 0x8427, 0x2050, 0x8426, 0x204a, 0x8424, 0x2044, 0x8423,
  0x203e, 0x8421, 0x2038, 0x8420, 0x2032, 0x841e, 0x202b, 0x841c,
  0x2025, 0x841b, 0x201f, 0x8419, 0x2019, 0x8418, 0x2013, 0x8416,
  0x200d, 0x8415, 0x2007, 0x8413, 0x2001, 0x8411, 0x1ffb, 0x8410,
  0x1ff5, 0x840e, 0x1fef, 0x840d, 0x1fe9, 0x840b, 0x1fe2, 0x840a,
  0x1fdc, 0x8408, 0x1fd6, 0x8406, 0x1fd0, 0x8405, 0x1fca, 0x8403,
  0x1fc4, 0x8402, 0x1fbe, 0x8400, 0x1fb8, 0x83ff, 0x1fb2, 0x83fd,
  0x1fac, 0x83fb, 0x1fa6, 0x83fa, 0x1f9f, 0x83f8, 0x1f99, 0x83f7,
  0x1f93, 0x83f5, 0x1f8d, 0x83f4, 0x1f87, 0x83f2, 0x1f81, 0x83f1,
  0x1f7b, 0x83ef, 0x1f75, 0x83ee, 0x1f6f, 0x83ec, 0x1f69, 0x83ea,
  0x1f63, 0x83e9, 0x1f5d, 0x83e7, 0x1f56, 0x83e6, 0x1f50, 0x83e4,
  0x1f4a, 0x83e3, 0x1f44, 0x83e1, 0x1f3e, 0x83e0, 0x1f38, 0x83de,
  0x1f32, 0x83dd, 0x1f2c, 0x83db, 0x1f26, 0x83da, 0x1f20, 0x83d8,
  0x1f19, 0x83d7, 0x1f13, 0x83d5, 0x1f0d, 0x83d3, 0x1f07, 0x83d2,
  0x1f01, 0x83d0, 0x1efb, 0x83cf, 0x1ef5, 0x83cd, 0x1eef, 0x83cc,
  0x1ee9, 0x83ca, 0x1ee3, 0x83c9, 0x1edd, 0x83c7, 0x1ed6, 0x83c6,
  0x1ed0, 0x83c4, 0x1eca, 0x83c3, 0x1ec4, 0x83c1, 0x1ebe, 0x83c0,
  0x1eb8, 0x83be, 0x1eb2, 0x83bd, 0x1eac, 0x83bb, 0x1ea6, 0x83ba,
  0x1ea0, 0x83b8, 0x1e99, 0x83b7, 0x1e93, 0x83b5, 0x1e8d, 0x83b4,
  0x1e87, 0x83b2, 0x1e81, 0x83b1, 0x1e7b, 0x83af, 0x1e75, 0x83ae,
  0x1e6f, 0x83ac, 0x1e69, 0x83ab, 0x1e62, 0x83a9, 0x1e5c, 0x83a8,
  0x1e56, 0x83a6, 0x1e50, 0x83a5, 0x1e4a, 0x83a3, 0x1e44, 0x83a2,
  0x1e3e, 0x83a0, 0x1e38, 0x839f, 0x1e32, 0x839d, 0x1e2c, 0x839c,
  0x1e25, 0x839a, 0x1e1f, 0x8399, 0x1e19, 0x8397, 0x1e13, 0x8396,
  0x1e0d, 0x8394, 0x1e07, 0x8393, 0x1e01, 0x8392, 0x1dfb, 0x8390,
  0x1df5, 0x838f, 0x1dee, 0x838d, 0x1de8, 0x838c, 0x1de2, 0x838a,
  0x1ddc, 0x8389, 0x1dd6, 0x8387, 0x1dd0, 0x8386, 0x1dca, 0x8384,
  0x1dc4, 0x8383, 0x1dbe, 0x8381, 0x1db7, 0x8380, 0x1db1, 0x837e,
  0x1dab, 0x837d, 0x1da5, 0x837c, 0x1d9f, 0x837a, 0x1d99, 0x8379,
  0x1d93, 0x8377, 0x1d8d, 0x8376, 0x1d87, 0x8374, 0x1d80, 0x8373,
  0x1d7a, 0x8371, 0x1d74, 0x8370, 0x1d6e, 0x836f, 0x1d68, 0x836d,
  0x1d62, 0x836c, 0x1d5c, 0x836a, 0x1d56, 0x8369, 0x1d50, 0x8367,
  0x1d49, 0x8366, 0x1d43, 0x8364, 0x1d3d, 0x8363, 0x1d37, 0x8362,
  0x1d31, 0x8360, 0x1d2b, 0x835f, 0x1d25, 0x835d, 0x1d1f, 0x835c,
  0x1d18, 0x835a, 0x1d12, 0x8359, 0x1d0c, 0x8358, 0x1d06, 0x8356,
  0x1d00, 0x8355, 0x1cfa, 0x8353, 0x1cf4, 0x8352, 0x1cee, 0x8350,
  0x1ce8, 0x834f, 0x1ce1, 0x834e, 0x1cdb, 0x834c, 0x1cd5, 0x834b,
  0x1ccf, 0x8349, 0x1cc9, 0x8348, 0x1cc3, 0x8347, 0x1cbd, 0x8345,
  0x1cb7, 0x8344, 0x1cb0, 0x8342, 0x1caa, 0x8341, 0x1ca4, 0x833f,
  0x1c9e, 0x833e, 0x1c98, 0x833d, 0x1c92, 0x833b, 0x1c8c, 0x833a,
  0x1c86, 0x8338, 0x1c7f, 0x8337, 0x1c79, 0x8336, 0x1c73, 0x8334,
  0x1c6d, 0x8333, 0x1c67, 0x8331, 0x1c61, 0x8330, 0x1c5b, 0x832f,
  0x1c55, 0x832d, 0x1c4e, 0x832c, 0x1c48, 0x832b, 0x1c42, 0x8329,
  0x1c3c, 0x8328, 0x1c36, 0x8326, 0x1c30, 0x8325, 0x1c2a, 0x8324,
  0x1c24, 0x8322, 0x1c1d, 0x8321, 0x1c17, 0x831f, 0x1c11, 0x831e,
  0x1c0b, 0x831d, 0x1c05, 0x831b, 0x1bff, 0x831a, 0x1bf9, 0x8319,
  0x1bf2, 0x8317, 0x1bec, 0x8316, 0x1be6, 0x8314, 0x1be0, 0x8313,
  0x1bda, 0x8312, 0x1bd4, 0x8310, 0x1bce, 0x830f, 0x1bc8, 0x830e,
  0x1bc1, 0x830c, 0x1bbb, 0x830b, 0x1bb5, 0x830a, 0x1baf, 0x8308,
  0x1ba9, 0x8307, 0x1ba3, 0x8305, 0x1b9d, 0x8304, 0x1b96, 0x8303,
  0x1b90, 0x8301, 0x1b8a, 0x8300, 0x1b84, 0x82ff, 0x1b7e, 0x82fd,
  0x1b78, 0x82fc, 0x1b72, 0x82fb, 0x1b6c, 0x82f9, 0x1b65, 0x82f8,
  0x1b5f, 0x82f7, 0x1b59, 0x82f5, 0x1b53, 0x82f4, 0x1b4d, 0x82f3,
  0x1b47, 0x82f1, 0x1b41, 0x82f0, 0x1b3a, 0x82ef, 0x1b34, 0x82ed,
  0x1b2e, 0x82ec, 0x1b28, 0x82eb, 0x1b22, 0x82e9, 0x1b1c, 0x82e8,
  0x1b16, 0x82e7, 0x1b0f, 0x82e5, 0x1b09, 0x82e4, 0x1b03, 0x82e3,
  0x1afd, 0x82e1, 0x1af7, 0x82e0, 0x1af1, 0x82df, 0x1aeb, 0x82dd,
  0x1ae4, 0x82dc, 0x1ade, 0x82db, 0x1ad8, 0x82d9, 0x1ad2, 0x82d8,
  0x1acc, 0x82d7, 0x1ac6, 0x82d5, 0x1ac0, 0x82d4, 0x1ab9, 0x82d3,
  0x1ab3, 0x82d1, 0x1aad, 0x82d0, 0x1aa7, 0x82cf, 0x1aa1, 0x82ce,
  0x1a9b, 0x82cc, 0x1a95, 0x82cb, 0x1a8e, 0x82ca, 0x1a88, 0x82c8,
  0x1a82, 0x82c7, 0x1a7c, 0x82c6, 0x1a76, 0x82c4, 0x1a70, 0x82c3,
  0x1a6a, 0x82c2, 0x1a63, 0x82c1, 0x1a5d, 0x82bf, 0x1a57, 0x82be,
  0x1a51, 0x82bd, 0x1a4b, 0x82bb, 0x1a45, 0x82ba, 0x1a3e, 0x82b9,
  0x1a38, 0x82b7, 0x1a32, 0x82b6, 0x1a2c, 0x82b5, 0x1a26, 0x82b4,
  0x1a20, 0x82b2, 0x1a1a, 0x82b1, 0x1a13, 0x82b0, 0x1a0d, 0x82ae,
  0x1a07, 0x82ad, 0x1a01, 0x82ac, 0x19fb, 0x82ab, 0x19f5, 0x82a9,
  0x19ef, 0x82a8, 0x19e8, 0x82a7, 0x19e2, 0x82a6, 0x19dc, 0x82a4,
  0x19d6, 0x82a3, 0x19d0, 0x82a2, 0x19ca, 0x82a0, 0x19c3, 0x829f,
  0x19bd, 0x829e, 0x19b7, 0x829d, 0x19b1, 0x829b, 0x19ab, 0x829a,
  0x19a5, 0x8299, 0x199f, 0x8298, 0x1998, 0x8296, 0x1992, 0x8295,
  0x198c, 0x8294, 0x1986, 0x8293, 0x1980, 0x8291, 0x197a, 0x8290,
  0x1973, 0x828f, 0x196d, 0x828e, 0x1967, 0x828c, 0x1961, 0x828b,
  0x195b, 0x828a, 0x1955, 0x8289, 0x194e, 0x8287, 0x1948, 0x8286,
  0x1942, 0x8285, 0x193c, 0x8284, 0x1936, 0x8282, 0x1930, 0x8281,
  0x192a, 0x8280, 0x1923, 0x827f, 0x191d, 0x827e, 0x1917, 0x827c,
  0x1911, 0x827b, 0x190b, 0x827a, 0x1905, 0x8279, 0x18fe, 0x8277,
  0x18f8, 0x8276, 0x18f2, 0x8275, 0x18ec, 0x8274, 0x18e6, 0x8272,
  0x18e0, 0x8271, 0x18d9, 0x8270, 0x18d3, 0x826f, 0x18cd, 0x826e,
  0x18c7, 0x826c, 0x18c1, 0x826b, 0x18bb, 0x826a, 0x18b4, 0x8269,
  0x18ae, 0x8268, 0x18a8, 0x8266, 0x18a2, 0x8265, 0x189c, 0x8264,
  0x1896, 0x8263, 0x188f, 0x8261, 0x1889, 0x8260, 0x1883, 0x825f,
  0x187d, 0x825e, 0x1877, 0x825d, 0x1871, 0x825b, 0x186a, 0x825a,
  0x1864, 0x8259, 0x185e, 0x8258, 0x1858, 0x8257, 0x1852, 0x8255,
  0x184c, 0x8254, 0x1845, 0x8253, 0x183f, 0x8252, 0x1839, 0x8251,
  0x1833, 0x8250, 0x182d, 0x824e, 0x1827, 0x824d, 0x1820, 0x824c,
  0x181a, 0x824b, 0x1814, 0x824a, 0x180e, 0x8248, 0x1808, 0x8247,
  0x1802, 0x8246, 0x17fb, 0x8245, 0x17f5, 0x8244, 0x17ef, 0x8243,
  0x17e9, 0x8241, 0x17e3, 0x8240, 0x17dd, 0x823f, 0x17d6, 0x823e,
  0x17d0, 0x823d, 0x17ca, 0x823b, 0x17c4, 0x823a, 0x17be, 0x8239,
  0x17b7, 0x8238, 0x17b1, 0x8237, 0x17ab, 0x8236, 0x17a5, 0x8234,
  0x179f, 0x8233, 0x1799, 0x8232, 0x1792, 0x8231, 0x178c, 0x8230,
  0x1786, 0x822f, 0x1780, 0x822e, 0x177a, 0x822c, 0x1774, 0x822b,
  0x176d, 0x822a, 0x1767, 0x8229, 0x1761, 0x8228, 0x175b, 0x8227,
  0x1755, 0x8226, 0x174e, 0x8224, 0x1748, 0x8223, 0x1742, 0x8222,
  0x173c, 0x8221, 0x1736, 0x8220, 0x1730, 0x821f, 0x1729, 0x821e,
  0x1723, 0x821c, 0x171d, 0x821b, 0x1717, 0x821a, 0x1711, 0x8219,
  0x170a, 0x8218, 0x1704, 0x8217, 0x16fe, 0x8216, 0x16f8, 0x8214,
  0x16f2, 0x8213, 0x16ec, 0x8212, 0x16e5, 0x8211, 0x16df, 0x8210,
  0x16d9, 0x820f, 0x16d3, 0x820e, 0x16cd, 0x820d, 0x16c6, 0x820b,
  0x16c0, 0x820a, 0x16ba, 0x8209, 0x16b4, 0x8208, 0x16ae, 0x8207,
  0x16a8, 0x8206, 0x16a1, 0x8205, 0x169b, 0x8204, 0x1695, 0x8203,
  0x168f, 0x8201, 0x1689, 0x8200, 0x1682, 0x81ff, 0x167c, 0x81fe,
  0x1676, 0x81fd, 0x1670, 0x81fc, 0x166a, 0x81fb, 0x1664, 0x81fa,
  0x165d, 0x81f9, 0x1657, 0x81f8, 0x1651, 0x81f6, 0x164b, 0x81f5,
  0x1645, 0x81f4, 0x163e, 0x81f3, 0x1638, 0x81f2, 0x1632, 0x81f1,
  0x162c, 0x81f0, 0x1626, 0x81ef, 0x161f, 0x81ee, 0x1619, 0x81ed,
  0x1613, 0x81ec, 0x160d, 0x81ea, 0x1607, 0x81e9, 0x1601, 0x81e8,
  0x15fa, 0x81e7, 0x15f4, 0x81e6, 0x15ee, 0x81e5, 0x15e8, 0x81e4,
  0x15e2, 0x81e3, 0x15db, 0x81e2, 0x15d5, 0x81e1, 0x15cf, 0x81e0,
  0x15c9, 0x81df, 0x15c3, 0x81de, 0x15bc, 0x81dc, 0x15b6, 0x81db,
  0x15b0, 0x81da, 0x15aa, 0x81d9, 0x15a4, 0x81d8, 0x159d, 0x81d7,
  0x1597, 0x81d6, 0x1591, 0x81d5, 0x158b, 0x81d4, 0x1585, 0x81d3,
  0x157f, 0x81d2, 0x1578, 0x81d1, 0x1572, 0x81d0, 0x156c, 0x81cf,
  0x1566, 0x81ce, 0x1560, 0x81cd, 0x1559, 0x81cc, 0x1553, 0x81cb,
  0x154d, 0x81c9, 0x1547, 0x81c8, 0x1541, 0x81c7, 0x153a, 0x81c6,
  0x1534, 0x81c5, 0x152e, 0x81c4, 0x1528, 0x81c3, 0x1522, 0x81c2,
  0x151b, 0x81c1, 0x1515, 0x81c0, 0x150f, 0x81bf, 0x1509, 0x81be,
  0x1503, 0x81bd, 0x14fc, 0x81bc, 0x14f6, 0x81bb, 0x14f0, 0x81ba,
  0x14ea, 0x81b9, 0x14e4, 0x81b8, 0x14dd, 0x81b7, 0x14d7, 0x81b6,
  0x14d1, 0x81b5, 0x14cb, 0x81b4, 0x14c5, 0x81b3, 0x14be, 0x81b2,
  0x14b8, 0x81b1, 0x14b2, 0x81b0, 0x14ac, 0x81af, 0x14a6, 0x81ae,
  0x149f, 0x81ad, 0x1499, 0x81ac, 0x1493, 0x81ab, 0x148d, 0x81aa,
  0x1487, 0x81a9, 0x1480, 0x81a8, 0x147a, 0x81a7, 0x1474, 0x81a6,
  0x146e, 0x81a5, 0x1468, 0x81a4, 0x1461, 0x81a3, 0x145b, 0x81a2,
  0x1455, 0x81a1, 0x144f, 0x81a0, 0x1449, 0x819f, 0x1442, 0x819e,
  0x143c, 0x819d, 0x1436, 0x819c, 0x1430, 0x819b, 0x142a, 0x819a,
  0x1423, 0x8199, 0x141d, 0x8198, 0x1417, 0x8197, 0x1411, 0x8196,
  0x140b, 0x8195, 0x1404, 0x8194, 0x13fe, 0x8193, 0x13f8, 0x8192,
  0x13f2, 0x8191, 0x13eb, 0x8190, 0x13e5, 0x818f, 0x13df, 0x818e,
  0x13d9, 0x818d, 0x13d3, 0x818c, 0x13cc, 0x818b, 0x13c6, 0x818a,
  0x13c0, 0x8189, 0x13ba, 0x8188, 0x13b4, 0x8187, 0x13ad, 0x8186,
  0x13a7, 0x8185, 0x13a1, 0x8184, 0x139b, 0x8183, 0x1395, 0x8182,
  0x138e, 0x8181, 0x1388, 0x8180, 0x1382, 0x817f, 0x137c, 0x817e,
  0x1376, 0x817d, 0x136f, 0x817c, 0x1369, 0x817c, 0x1363, 0x817b,
  0x135d, 0x817a, 0x1356, 0x8179, 0x1350, 0x8178, 0x134a, 0x8177,
  0x1344, 0x8176, 0x133e, 0x8175, 0x1337, 0x8174, 0x1331, 0x8173,
  0x132b, 0x8172, 0x1325, 0x8171, 0x131f, 0x8170, 0x1318, 0x816f,
  0x1312, 0x816e, 0x130c, 0x816d, 0x1306, 0x816c, 0x12ff, 0x816c,
  0x12f9, 0x816b, 0x12f3, 0x816a, 0x12ed, 0x8169, 0x12e7, 0x8168,
  0x12e0, 0x8167, 0x12da, 0x8166, 0x12d4, 0x8165, 0x12ce, 0x8164,
  0x12c8, 0x8163, 0x12c1, 0x8162, 0x12bb, 0x8161, 0x12b5, 0x8160,
  0x12af, 0x815f, 0x12a8, 0x815f, 0x12a2, 0x815e, 0x129c, 0x815d,
  0x1296, 0x815c, 0x1290, 0x815b, 0x1289, 0x815a, 0x1283, 0x8159,
  0x127d, 0x8158, 0x1277, 0x8157, 0x1271, 0x8156, 0x126a, 0x8155,
  0x1264, 0x8155, 0x125e, 0x8154, 0x1258, 0x8153, 0x1251, 0x8152,
  0x124b, 0x8151, 0x1245, 0x8150, 0x123f, 0x814f, 0x1239, 0x814e,
  0x1232, 0x814d, 0x122c, 0x814c, 0x1226, 0x814c, 0x1220, 0x814b,
  0x1219, 0x814a, 0x1213, 0x8149, 0x120d, 0x8148, 0x1207, 0x8147,
  0x1201, 0x8146, 0x11fa, 0x8145, 0x11f4, 0x8145, 0x11ee, 0x8144,
  0x11e8, 0x8143, 0x11e1, 0x8142, 0x11db, 0x8141, 0x11d5, 0x8140,
  0x11cf, 0x813f, 0x11c9, 0x813e, 0x11c2, 0x813d, 0x11bc, 0x813d,
  0x11b6, 0x813c, 0x11b0, 0x813b, 0x11a9, 0x813a, 0x11a3, 0x8139,
  0x119d, 0x8138, 0x1197, 0x8137, 0x1191, 0x8137, 0x118a, 0x8136,
  0x1184, 0x8135, 0x117e, 0x8134, 0x1178, 0x8133, 0x1171, 0x8132,
  0x116b, 0x8131, 0x1165, 0x8131, 0x115f, 0x8130, 0x1159, 0x812f,
  0x1152, 0x812e, 0x114c, 0x812d, 0x1146, 0x812c, 0x1140, 0x812b,
  0x1139, 0x812b, 0x1133, 0x812a, 0x112d, 0x8129, 0x1127, 0x8128,
  0x1121, 0x8127, 0x111a, 0x8126, 0x1114, 0x8126, 0x110e, 0x8125,
  0x1108, 0x8124, 0x1101, 0x8123, 0x10fb, 0x8122, 0x10f5, 0x8121,
  0x10ef, 0x8121, 0x10e8, 0x8120, 0x10e2, 0x811f, 0x10dc, 0x811e,
  0x10d6, 0x811d, 0x10d0, 0x811c, 0x10c9, 0x811c, 0x10c3, 0x811b,
  0x10bd, 0x811a, 0x10b7, 0x8119, 0x10b0, 0x8118, 0x10aa, 0x8117,
  0x10a4, 0x8117, 0x109e, 0x8116, 0x1098, 0x8115, 0x1091, 0x8114,
  0x108b, 0x8113, 0x1085, 0x8113, 0x107f, 0x8112, 0x1078, 0x8111,
  0x1072, 0x8110, 0x106c, 0x810f, 0x1066, 0x810f, 0x105f, 0x810e,
  0x1059, 0x810d, 0x1053, 0x810c, 0x104d, 0x810b, 0x1047, 0x810b,
  0x1040, 0x810a, 0x103a, 0x8109, 0x1034, 0x8108, 0x102e, 0x8107,
  0x1027, 0x8107, 0x1021, 0x8106, 0x101b, 0x8105, 0x1015, 0x8104,
  0x100e, 0x8103, 0x1008, 0x8103, 0x1002, 0x8102, 0xffc, 0x8101,
  0xff5, 0x8100, 0xfef, 0x80ff, 0xfe9, 0x80ff, 0xfe3, 0x80fe,
  0xfdd, 0x80fd, 0xfd6, 0x80fc, 0xfd0, 0x80fc, 0xfca, 0x80fb,
  0xfc4, 0x80fa, 0xfbd, 0x80f9, 0xfb7, 0x80f8, 0xfb1, 0x80f8,
  0xfab, 0x80f7, 0xfa4, 0x80f6, 0xf9e, 0x80f5, 0xf98, 0x80f5,
  0xf92, 0x80f4, 0xf8b, 0x80f3, 0xf85, 0x80f2, 0xf7f, 0x80f2,
  0xf79, 0x80f1, 0xf73, 0x80f0, 0xf6c, 0x80ef, 0xf66, 0x80ef,
  0xf60, 0x80ee, 0xf5a, 0x80ed, 0xf53, 0x80ec, 0xf4d, 0x80ec,
  0xf47, 0x80eb, 0xf41, 0x80ea, 0xf3a, 0x80e9, 0xf34, 0x80e9,
  0xf2e, 0x80e8, 0xf28, 0x80e7, 0xf21, 0x80e6, 0xf1b, 0x80e6,
  0xf15, 0x80e5, 0xf0f, 0x80e4, 0xf08, 0x80e3, 0xf02, 0x80e3,
  0xefc, 0x80e2, 0xef6, 0x80e1, 0xef0, 0x80e0, 0xee9, 0x80e0,
  0xee3, 0x80df, 0xedd, 0x80de, 0xed7, 0x80dd, 0xed0, 0x80dd,
  0xeca, 0x80dc, 0xec4, 0x80db, 0xebe, 0x80db, 0xeb7, 0x80da,
  0xeb1, 0x80d9, 0xeab, 0x80d8, 0xea5, 0x80d8, 0xe9e, 0x80d7,
  0xe98, 0x80d6, 0xe92, 0x80d6, 0xe8c, 0x80d5, 0xe85, 0x80d4,
  0xe7f, 0x80d3, 0xe79, 0x80d3, 0xe73, 0x80d2, 0xe6c, 0x80d1,
  0xe66, 0x80d1, 0xe60, 0x80d0, 0xe5a, 0x80cf, 0xe53, 0x80ce,
  0xe4d, 0x80ce, 0xe47, 0x80cd, 0xe41, 0x80cc, 0xe3a, 0x80cc,
  0xe34, 0x80cb, 0xe2e, 0x80ca, 0xe28, 0x80ca, 0xe22, 0x80c9,
  0xe1b, 0x80c8, 0xe15, 0x80c7, 0xe0f, 0x80c7, 0xe09, 0x80c6,
  0xe02, 0x80c5, 0xdfc, 0x80c5, 0xdf6, 0x80c4, 0xdf0, 0x80c3,
  0xde9, 0x80c3, 0xde3, 0x80c2, 0xddd, 0x80c1, 0xdd7, 0x80c1,
  0xdd0, 0x80c0, 0xdca, 0x80bf, 0xdc4, 0x80bf, 0xdbe, 0x80be,
  0xdb7, 0x80bd, 0xdb1, 0x80bd, 0xdab, 0x80bc, 0xda5, 0x80bb,
  0xd9e, 0x80bb, 0xd98, 0x80ba, 0xd92, 0x80b9, 0xd8c, 0x80b9,
  0xd85, 0x80b8, 0xd7f, 0x80b7, 0xd79, 0x80b7, 0xd73, 0x80b6,
  0xd6c, 0x80b5, 0xd66, 0x80b5, 0xd60, 0x80b4, 0xd5a, 0x80b3,
  0xd53, 0x80b3, 0xd4d, 0x80b2, 0xd47, 0x80b1, 0xd41, 0x80b1,
  0xd3a, 0x80b0, 0xd34, 0x80af, 0xd2e, 0x80af, 0xd28, 0x80ae,
  0xd21, 0x80ad, 0xd1b, 0x80ad, 0xd15, 0x80ac, 0xd0f, 0x80ab,
  0xd08, 0x80ab, 0xd02, 0x80aa, 0xcfc, 0x80aa, 0xcf6, 0x80a9,
  0xcef, 0x80a8, 0xce9, 0x80a8, 0xce3, 0x80a7, 0xcdd, 0x80a6,
  0xcd6, 0x80a6, 0xcd0, 0x80a5, 0xcca, 0x80a5, 0xcc4, 0x80a4,
  0xcbd, 0x80a3, 0xcb7, 0x80a3, 0xcb1, 0x80a2, 0xcab, 0x80a1,
  0xca4, 0x80a1, 0xc9e, 0x80a0, 0xc98, 0x80a0, 0xc92, 0x809f,
  0xc8b, 0x809e, 0xc85, 0x809e, 0xc7f, 0x809d, 0xc79, 0x809c,
  0xc72, 0x809c, 0xc6c, 0x809b, 0xc66, 0x809b, 0xc60, 0x809a,
  0xc59, 0x8099, 0xc53, 0x8099, 0xc4d, 0x8098, 0xc47, 0x8098,
  0xc40, 0x8097, 0xc3a, 0x8096, 0xc34, 0x8096, 0xc2e, 0x8095,
  0xc27, 0x8095, 0xc21, 0x8094, 0xc1b, 0x8093, 0xc14, 0x8093,
  0xc0e, 0x8092, 0xc08, 0x8092, 0xc02, 0x8091, 0xbfb, 0x8090,
  0xbf5, 0x8090, 0xbef, 0x808f, 0xbe9, 0x808f, 0xbe2, 0x808e,
  0xbdc, 0x808e, 0xbd6, 0x808d, 0xbd0, 0x808c, 0xbc9, 0x808c,
  0xbc3, 0x808b, 0xbbd, 0x808b, 0xbb7, 0x808a, 0xbb0, 0x8089,
  0xbaa, 0x8089, 0xba4, 0x8088, 0xb9e, 0x8088, 0xb97, 0x8087,
  0xb91, 0x8087, 0xb8b, 0x8086, 0xb85, 0x8085, 0xb7e, 0x8085,
  0xb78, 0x8084, 0xb72, 0x8084, 0xb6c, 0x8083, 0xb65, 0x8083,
  0xb5f, 0x8082, 0xb59, 0x8082, 0xb53, 0x8081, 0xb4c, 0x8080,
  0xb46, 0x8080, 0xb40, 0x807f, 0xb3a, 0x807f, 0xb33, 0x807e,
  0xb2d, 0x807e, 0xb27, 0x807d, 0xb20, 0x807d, 0xb1a, 0x807c,
  0xb14, 0x807b, 0xb0e, 0x807b, 0xb07, 0x807a, 0xb01, 0x807a,
  0xafb, 0x8079, 0xaf5, 0x8079, 0xaee, 0x8078, 0xae8, 0x8078,
  0xae2, 0x8077, 0xadc, 0x8077, 0xad5, 0x8076, 0xacf, 0x8076,
  0xac9, 0x8075, 0xac3, 0x8075, 0xabc, 0x8074, 0xab6, 0x8073,
  0xab0, 0x8073, 0xaaa, 0x8072, 0xaa3, 0x8072, 0xa9d, 0x8071,
  0xa97, 0x8071, 0xa90, 0x8070, 0xa8a, 0x8070, 0xa84, 0x806f,
  0xa7e, 0x806f, 0xa77, 0x806e, 0xa71, 0x806e, 0xa6b, 0x806d,
  0xa65, 0x806d, 0xa5e, 0x806c, 0xa58, 0x806c, 0xa52, 0x806b,
  0xa4c, 0x806b, 0xa45, 0x806a, 0xa3f, 0x806a, 0xa39, 0x8069,
  0xa33, 0x8069, 0xa2c, 0x8068, 0xa26, 0x8068, 0xa20, 0x8067,
  0xa19, 0x8067, 0xa13, 0x8066, 0xa0d, 0x8066, 0xa07, 0x8065,
  0xa00, 0x8065, 0x9fa, 0x8064, 0x9f4, 0x8064, 0x9ee, 0x8063,
  0x9e7, 0x8063, 0x9e1, 0x8062, 0x9db, 0x8062, 0x9d5, 0x8061,
  0x9ce, 0x8061, 0x9c8, 0x8060, 0x9c2, 0x8060, 0x9bc, 0x805f,
  0x9b5, 0x805f, 0x9af, 0x805e, 0x9a9, 0x805e, 0x9a2, 0x805d,
  0x99c, 0x805d, 0x996, 0x805d, 0x990, 0x805c, 0x989, 0x805c,
  0x983, 0x805b, 0x97d, 0x805b, 0x977, 0x805a, 0x970, 0x805a,
  0x96a, 0x8059, 0x964, 0x8059, 0x95e, 0x8058, 0x957, 0x8058,
  0x951, 0x8057, 0x94b, 0x8057, 0x944, 0x8057, 0x93e, 0x8056,
  0x938, 0x8056, 0x932, 0x8055, 0x92b, 0x8055, 0x925, 0x8054,
  0x91f, 0x8054, 0x919, 0x8053, 0x912, 0x8053, 0x90c, 0x8052,
  0x906, 0x8052, 0x900, 0x8052, 0x8f9, 0x8051, 0x8f3, 0x8051,
  0x8ed, 0x8050, 0x8e6, 0x8050, 0x8e0, 0x804f, 0x8da, 0x804f,
  0x8d4, 0x804f, 0x8cd, 0x804e, 0x8c7, 0x804e, 0x8c1, 0x804d,
  0x8bb, 0x804d, 0x8b4, 0x804c, 0x8ae, 0x804c, 0x8a8, 0x804c,
  0x8a2, 0x804b, 0x89b, 0x804b, 0x895, 0x804a, 0x88f, 0x804a,
  0x888, 0x8049, 0x882, 0x8049, 0x87c, 0x8049, 0x876, 0x8048,
  0x86f, 0x8048, 0x869, 0x8047, 0x863, 0x8047, 0x85d, 0x8047,
  0x856, 0x8046, 0x850, 0x8046, 0x84a, 0x8045, 0x843, 0x8045,
  0x83d, 0x8044, 0x837, 0x8044, 0x831, 0x8044, 0x82a, 0x8043,
  0x824, 0x8043, 0x81e, 0x8042, 0x818, 0x8042, 0x811, 0x8042,
  0x80b, 0x8041, 0x805, 0x8041, 0x7fe, 0x8040, 0x7f8, 0x8040,
  0x7f2, 0x8040, 0x7ec, 0x803f, 0x7e5, 0x803f, 0x7df, 0x803f,
  0x7d9, 0x803e, 0x7d3, 0x803e, 0x7cc, 0x803d, 0x7c6, 0x803d,
  0x7c0, 0x803d, 0x7ba, 0x803c, 0x7b3, 0x803c, 0x7ad, 0x803b,
  0x7a7, 0x803b, 0x7a0, 0x803b, 0x79a, 0x803a, 0x794, 0x803a,
  0x78e, 0x803a, 0x787, 0x8039, 0x781, 0x8039, 0x77b, 0x8039,
  0x775, 0x8038, 0x76e, 0x8038, 0x768, 0x8037, 0x762, 0x8037,
  0x75b, 0x8037, 0x755, 0x8036, 0x74f, 0x8036, 0x749, 0x8036,
  0x742, 0x8035, 0x73c, 0x8035, 0x736, 0x8035, 0x730, 0x8034,
  0x729, 0x8034, 0x723, 0x8033, 0x71d, 0x8033, 0x716, 0x8033,
  0x710, 0x8032, 0x70a, 0x8032, 0x704, 0x8032, 0x6fd, 0x8031,
  0x6f7, 0x8031, 0x6f1, 0x8031, 0x6ea, 0x8030, 0x6e4, 0x8030,
  0x6de, 0x8030, 0x6d8, 0x802f, 0x6d1, 0x802f, 0x6cb, 0x802f,
  0x6c5, 0x802e, 0x6bf, 0x802e, 0x6b8, 0x802e, 0x6b2, 0x802d,
  0x6ac, 0x802d, 0x6a5, 0x802d, 0x69f, 0x802c, 0x699, 0x802c,
  0x693, 0x802c, 0x68c, 0x802b, 0x686, 0x802b, 0x680, 0x802b,
  0x67a, 0x802a, 0x673, 0x802a, 0x66d, 0x802a, 0x667, 0x802a,
  0x660, 0x8029, 0x65a, 0x8029, 0x654, 0x8029, 0x64e, 0x8028,
  0x647, 0x8028, 0x641, 0x8028, 0x63b, 0x8027, 0x635, 0x8027,
  0x62e, 0x8027, 0x628, 0x8026, 0x622, 0x8026, 0x61b, 0x8026,
  0x615, 0x8026, 0x60f, 0x8025, 0x609, 0x8025, 0x602, 0x8025,
  0x5fc, 0x8024, 0x5f6, 0x8024, 0x5ef, 0x8024, 0x5e9, 0x8023,
  0x5e3, 0x8023, 0x5dd, 0x8023, 0x5d6, 0x8023, 0x5d0, 0x8022,
  0x5ca, 0x8022, 0x5c4, 0x8022, 0x5bd, 0x8021, 0x5b7, 0x8021,
  0x5b1, 0x8021, 0x5aa, 0x8021, 0x5a4, 0x8020, 0x59e, 0x8020,
  0x598, 0x8020, 0x591, 0x8020, 0x58b, 0x801f, 0x585, 0x801f,
  0x57f, 0x801f, 0x578, 0x801e, 0x572, 0x801e, 0x56c, 0x801e,
  0x565, 0x801e, 0x55f, 0x801d, 0x559, 0x801d, 0x553, 0x801d,
  0x54c, 0x801d, 0x546, 0x801c, 0x540, 0x801c, 0x539, 0x801c,
  0x533, 0x801c, 0x52d, 0x801b, 0x527, 0x801b, 0x520, 0x801b,
  0x51a, 0x801b, 0x514, 0x801a, 0x50d, 0x801a, 0x507, 0x801a,
  0x501, 0x801a, 0x4fb, 0x8019, 0x4f4, 0x8019, 0x4ee, 0x8019,
  0x4e8, 0x8019, 0x4e2, 0x8018, 0x4db, 0x8018, 0x4d5, 0x8018,
  0x4cf, 0x8018, 0x4c8, 0x8017, 0x4c2, 0x8017, 0x4bc, 0x8017,
  0x4b6, 0x8017, 0x4af, 0x8016, 0x4a9, 0x8016, 0x4a3, 0x8016,
  0x49c, 0x8016, 0x496, 0x8016, 0x490, 0x8015, 0x48a, 0x8015,
  0x483, 0x8015, 0x47d, 0x8015, 0x477, 0x8014, 0x471, 0x8014,
  0x46a, 0x8014, 0x464, 0x8014, 0x45e, 0x8014, 0x457, 0x8013,
  0x451, 0x8013, 0x44b, 0x8013, 0x445, 0x8013, 0x43e, 0x8013,
  0x438, 0x8012, 0x432, 0x8012, 0x42b, 0x8012, 0x425, 0x8012,
  0x41f, 0x8012, 0x419, 0x8011, 0x412, 0x8011, 0x40c, 0x8011,
  0x406, 0x8011, 0x3ff, 0x8011, 0x3f9, 0x8010, 0x3f3, 0x8010,
  0x3ed, 0x8010, 0x3e6, 0x8010, 0x3e0, 0x8010, 0x3da, 0x800f,
  0x3d4, 0x800f, 0x3cd, 0x800f, 0x3c7, 0x800f, 0x3c1, 0x800f,
  0x3ba, 0x800e, 0x3b4, 0x800e, 0x3ae, 0x800e, 0x3a8, 0x800e,
  0x3a1, 0x800e, 0x39b, 0x800e, 0x395, 0x800d, 0x38e, 0x800d,
  0x388, 0x800d, 0x382, 0x800d, 0x37c, 0x800d, 0x375, 0x800c,
  0x36f, 0x800c, 0x369, 0x800c, 0x362, 0x800c, 0x35c, 0x800c,
  0x356, 0x800c, 0x350, 0x800b, 0x349, 0x800b, 0x343, 0x800b,
  0x33d, 0x800b, 0x337, 0x800b, 0x330, 0x800b, 0x32a, 0x800b,
  0x324, 0x800a, 0x31d, 0x800a, 0x317, 0x800a, 0x311, 0x800a,
  0x30b, 0x800a, 0x304, 0x800a, 0x2fe, 0x8009, 0x2f8, 0x8009,
  0x2f1, 0x8009, 0x2eb, 0x8009, 0x2e5, 0x8009, 0x2df, 0x8009,
  0x2d8, 0x8009, 0x2d2, 0x8008, 0x2cc, 0x8008, 0x2c5, 0x8008,
  0x2bf, 0x8008, 0x2b9, 0x8008, 0x2b3, 0x8008, 0x2ac, 0x8008,
  0x2a6, 0x8008, 0x2a0, 0x8007, 0x299, 0x8007, 0x293, 0x8007,
  0x28d, 0x8007, 0x287, 0x8007, 0x280, 0x8007, 0x27a, 0x8007,
  0x274, 0x8007, 0x26d, 0x8006, 0x267, 0x8006, 0x261, 0x8006,
  0x25b, 0x8006, 0x254, 0x8006, 0x24e, 0x8006, 0x248, 0x8006,
  0x242, 0x8006, 0x23b, 0x8005, 0x235, 0x8005, 0x22f, 0x8005,
  0x228, 0x8005, 0x222, 0x8005, 0x21c, 0x8005, 0x216, 0x8005,
  0x20f, 0x8005, 0x209, 0x8005, 0x203, 0x8005, 0x1fc, 0x8004,
  0x1f6, 0x8004, 0x1f0, 0x8004, 0x1ea, 0x8004, 0x1e3, 0x8004,
  0x1dd, 0x8004, 0x1d7, 0x8004, 0x1d0, 0x8004, 0x1ca, 0x8004,
  0x1c4, 0x8004, 0x1be, 0x8004, 0x1b7, 0x8003, 0x1b1, 0x8003,
  0x1ab, 0x8003, 0x1a4, 0x8003, 0x19e, 0x8003, 0x198, 0x8003,
  0x192, 0x8003, 0x18b, 0x8003, 0x185, 0x8003, 0x17f, 0x8003,
  0x178, 0x8003, 0x172, 0x8003, 0x16c, 0x8003, 0x166, 0x8002,
  0x15f, 0x8002, 0x159, 0x8002, 0x153, 0x8002, 0x14d, 0x8002,
  0x146, 0x8002, 0x140, 0x8002, 0x13a, 0x8002, 0x133, 0x8002,
  0x12d, 0x8002, 0x127, 0x8002, 0x121, 0x8002, 0x11a, 0x8002,
  0x114, 0x8002, 0x10e, 0x8002, 0x107, 0x8002, 0x101, 0x8002,
  0xfb, 0x8001, 0xf5, 0x8001, 0xee, 0x8001, 0xe8, 0x8001,
  0xe2, 0x8001, 0xdb, 0x8001, 0xd5, 0x8001, 0xcf, 0x8001,
  0xc9, 0x8001, 0xc2, 0x8001, 0xbc, 0x8001, 0xb6, 0x8001,
  0xaf, 0x8001, 0xa9, 0x8001, 0xa3, 0x8001, 0x9d, 0x8001,
  0x96, 0x8001, 0x90, 0x8001, 0x8a, 0x8001, 0x83, 0x8001,
  0x7d, 0x8001, 0x77, 0x8001, 0x71, 0x8001, 0x6a, 0x8001,
  0x64, 0x8001, 0x5e, 0x8001, 0x57, 0x8001, 0x51, 0x8001,
  0x4b, 0x8001, 0x45, 0x8001, 0x3e, 0x8001, 0x38, 0x8001,
  0x32, 0x8001, 0x2b, 0x8001, 0x25, 0x8001, 0x1f, 0x8001,
  0x19, 0x8001, 0x12, 0x8001, 0xc, 0x8001, 0x6, 0x8001,
};


/**    
* \par    
* cosFactor tables are generated using the formula : <pre> cos_factors[n] = 2 * cos((2n+1)*pi/(4*N)) </pre>    
* \par    
* C command to generate the table    
* <pre>    
* for(i = 0; i< N; i++)    
* {    
*   cos_factors[i]= 2 * cos((2*i+1)*c/2);    
* } </pre>    
* \par    
* where <code>N</code> is the number of factors to generate and <code>c</code> is <code>pi/(2*N)</code>    
* \par    
* Then converted to q15 format by multiplying with 2^31 and saturated if required.    
    
*/

static const q15_t ALIGN4 cos_factorsQ15_128[128] = {
  0x7fff, 0x7ffa, 0x7ff0, 0x7fe1, 0x7fce, 0x7fb5, 0x7f97, 0x7f75,
  0x7f4d, 0x7f21, 0x7ef0, 0x7eba, 0x7e7f, 0x7e3f, 0x7dfa, 0x7db0,
  0x7d62, 0x7d0f, 0x7cb7, 0x7c5a, 0x7bf8, 0x7b92, 0x7b26, 0x7ab6,
  0x7a42, 0x79c8, 0x794a, 0x78c7, 0x7840, 0x77b4, 0x7723, 0x768e,
  0x75f4, 0x7555, 0x74b2, 0x740b, 0x735f, 0x72af, 0x71fa, 0x7141,
  0x7083, 0x6fc1, 0x6efb, 0x6e30, 0x6d62, 0x6c8f, 0x6bb8, 0x6adc,
  0x69fd, 0x6919, 0x6832, 0x6746, 0x6657, 0x6563, 0x646c, 0x6371,
  0x6271, 0x616f, 0x6068, 0x5f5e, 0x5e50, 0x5d3e, 0x5c29, 0x5b10,
  0x59f3, 0x58d4, 0x57b0, 0x568a, 0x5560, 0x5433, 0x5302, 0x51ce,
  0x5097, 0x4f5e, 0x4e21, 0x4ce1, 0x4b9e, 0x4a58, 0x490f, 0x47c3,
  0x4675, 0x4524, 0x43d0, 0x427a, 0x4121, 0x3fc5, 0x3e68, 0x3d07,
  0x3ba5, 0x3a40, 0x38d8, 0x376f, 0x3604, 0x3496, 0x3326, 0x31b5,
  0x3041, 0x2ecc, 0x2d55, 0x2bdc, 0x2a61, 0x28e5, 0x2767, 0x25e8,
  0x2467, 0x22e5, 0x2161, 0x1fdc, 0x1e56, 0x1ccf, 0x1b47, 0x19bd,
  0x1833, 0x16a8, 0x151b, 0x138e, 0x1201, 0x1072, 0xee3, 0xd53,
  0xbc3, 0xa33, 0x8a2, 0x710, 0x57f, 0x3ed, 0x25b, 0xc9
};

static const q15_t ALIGN4 cos_factorsQ15_512[512] = {
  0x7fff, 0x7fff, 0x7fff, 0x7ffe, 0x7ffc, 0x7ffb, 0x7ff9, 0x7ff7,
  0x7ff4, 0x7ff2, 0x7fee, 0x7feb, 0x7fe7, 0x7fe3, 0x7fdf, 0x7fda,
  0x7fd6, 0x7fd0, 0x7fcb, 0x7fc5, 0x7fbf, 0x7fb8, 0x7fb1, 0x7faa,
  0x7fa3, 0x7f9b, 0x7f93, 0x7f8b, 0x7f82, 0x7f79, 0x7f70, 0x7f67,
  0x7f5d, 0x7f53, 0x7f48, 0x7f3d, 0x7f32, 0x7f27, 0x7f1b, 0x7f0f,
  0x7f03, 0x7ef6, 0x7ee9, 0x7edc, 0x7ecf, 0x7ec1, 0x7eb3, 0x7ea4,
  0x7e95, 0x7e86, 0x7e77, 0x7e67, 0x7e57, 0x7e47, 0x7e37, 0x7e26,
  0x7e14, 0x7e03, 0x7df1, 0x7ddf, 0x7dcd, 0x7dba, 0x7da7, 0x7d94,
  0x7d80, 0x7d6c, 0x7d58, 0x7d43, 0x7d2f, 0x7d19, 0x7d04, 0x7cee,
  0x7cd8, 0x7cc2, 0x7cab, 0x7c94, 0x7c7d, 0x7c66, 0x7c4e, 0x7c36,
  0x7c1d, 0x7c05, 0x7beb, 0x7bd2, 0x7bb9, 0x7b9f, 0x7b84, 0x7b6a,
  0x7b4f, 0x7b34, 0x7b19, 0x7afd, 0x7ae1, 0x7ac5, 0x7aa8, 0x7a8b,
  0x7a6e, 0x7a50, 0x7a33, 0x7a15, 0x79f6, 0x79d8, 0x79b9, 0x7999,
  0x797a, 0x795a, 0x793a, 0x7919, 0x78f9, 0x78d8, 0x78b6, 0x7895,
  0x7873, 0x7851, 0x782e, 0x780c, 0x77e9, 0x77c5, 0x77a2, 0x777e,
  0x775a, 0x7735, 0x7710, 0x76eb, 0x76c6, 0x76a0, 0x767b, 0x7654,
  0x762e, 0x7607, 0x75e0, 0x75b9, 0x7591, 0x7569, 0x7541, 0x7519,
  0x74f0, 0x74c7, 0x749e, 0x7474, 0x744a, 0x7420, 0x73f6, 0x73cb,
  0x73a0, 0x7375, 0x7349, 0x731d, 0x72f1, 0x72c5, 0x7298, 0x726b,
  0x723e, 0x7211, 0x71e3, 0x71b5, 0x7186, 0x7158, 0x7129, 0x70fa,
  0x70cb, 0x709b, 0x706b, 0x703b, 0x700a, 0x6fda, 0x6fa9, 0x6f77,
  0x6f46, 0x6f14, 0x6ee2, 0x6eaf, 0x6e7d, 0x6e4a, 0x6e17, 0x6de3,
  0x6db0, 0x6d7c, 0x6d48, 0x6d13, 0x6cde, 0x6ca9, 0x6c74, 0x6c3f,
  0x6c09, 0x6bd3, 0x6b9c, 0x6b66, 0x6b2f, 0x6af8, 0x6ac1, 0x6a89,
  0x6a51, 0x6a19, 0x69e1, 0x69a8, 0x696f, 0x6936, 0x68fd, 0x68c3,
  0x6889, 0x684f, 0x6815, 0x67da, 0x679f, 0x6764, 0x6729, 0x66ed,
  0x66b1, 0x6675, 0x6639, 0x65fc, 0x65bf, 0x6582, 0x6545, 0x6507,
  0x64c9, 0x648b, 0x644d, 0x640e, 0x63cf, 0x6390, 0x6351, 0x6311,
  0x62d2, 0x6292, 0x6251, 0x6211, 0x61d0, 0x618f, 0x614e, 0x610d,
  0x60cb, 0x6089, 0x6047, 0x6004, 0x5fc2, 0x5f7f, 0x5f3c, 0x5ef9,
  0x5eb5, 0x5e71, 0x5e2d, 0x5de9, 0x5da5, 0x5d60, 0x5d1b, 0x5cd6,
  0x5c91, 0x5c4b, 0x5c06, 0x5bc0, 0x5b79, 0x5b33, 0x5aec, 0x5aa5,
  0x5a5e, 0x5a17, 0x59d0, 0x5988, 0x5940, 0x58f8, 0x58af, 0x5867,
  0x581e, 0x57d5, 0x578c, 0x5742, 0x56f9, 0x56af, 0x5665, 0x561a,
  0x55d0, 0x5585, 0x553a, 0x54ef, 0x54a4, 0x5458, 0x540d, 0x53c1,
  0x5375, 0x5328, 0x52dc, 0x528f, 0x5242, 0x51f5, 0x51a8, 0x515a,
  0x510c, 0x50bf, 0x5070, 0x5022, 0x4fd4, 0x4f85, 0x4f36, 0x4ee7,
  0x4e98, 0x4e48, 0x4df9, 0x4da9, 0x4d59, 0x4d09, 0x4cb8, 0x4c68,
  0x4c17, 0x4bc6, 0x4b75, 0x4b24, 0x4ad2, 0x4a81, 0x4a2f, 0x49dd,
  0x498a, 0x4938, 0x48e6, 0x4893, 0x4840, 0x47ed, 0x479a, 0x4746,
  0x46f3, 0x469f, 0x464b, 0x45f7, 0x45a3, 0x454e, 0x44fa, 0x44a5,
  0x4450, 0x43fb, 0x43a5, 0x4350, 0x42fa, 0x42a5, 0x424f, 0x41f9,
  0x41a2, 0x414c, 0x40f6, 0x409f, 0x4048, 0x3ff1, 0x3f9a, 0x3f43,
  0x3eeb, 0x3e93, 0x3e3c, 0x3de4, 0x3d8c, 0x3d33, 0x3cdb, 0x3c83,
  0x3c2a, 0x3bd1, 0x3b78, 0x3b1f, 0x3ac6, 0x3a6c, 0x3a13, 0x39b9,
  0x395f, 0x3906, 0x38ab, 0x3851, 0x37f7, 0x379c, 0x3742, 0x36e7,
  0x368c, 0x3631, 0x35d6, 0x357b, 0x351f, 0x34c4, 0x3468, 0x340c,
  0x33b0, 0x3354, 0x32f8, 0x329c, 0x3240, 0x31e3, 0x3186, 0x312a,
  0x30cd, 0x3070, 0x3013, 0x2fb5, 0x2f58, 0x2efb, 0x2e9d, 0x2e3f,
  0x2de2, 0x2d84, 0x2d26, 0x2cc8, 0x2c69, 0x2c0b, 0x2bad, 0x2b4e,
  0x2aef, 0x2a91, 0x2a32, 0x29d3, 0x2974, 0x2915, 0x28b5, 0x2856,
  0x27f6, 0x2797, 0x2737, 0x26d8, 0x2678, 0x2618, 0x25b8, 0x2558,
  0x24f7, 0x2497, 0x2437, 0x23d6, 0x2376, 0x2315, 0x22b4, 0x2254,
  0x21f3, 0x2192, 0x2131, 0x20d0, 0x206e, 0x200d, 0x1fac, 0x1f4a,
  0x1ee9, 0x1e87, 0x1e25, 0x1dc4, 0x1d62, 0x1d00, 0x1c9e, 0x1c3c,
  0x1bda, 0x1b78, 0x1b16, 0x1ab3, 0x1a51, 0x19ef, 0x198c, 0x192a,
  0x18c7, 0x1864, 0x1802, 0x179f, 0x173c, 0x16d9, 0x1676, 0x1613,
  0x15b0, 0x154d, 0x14ea, 0x1487, 0x1423, 0x13c0, 0x135d, 0x12f9,
  0x1296, 0x1232, 0x11cf, 0x116b, 0x1108, 0x10a4, 0x1040, 0xfdd,
  0xf79, 0xf15, 0xeb1, 0xe4d, 0xde9, 0xd85, 0xd21, 0xcbd,
  0xc59, 0xbf5, 0xb91, 0xb2d, 0xac9, 0xa65, 0xa00, 0x99c,
  0x938, 0x8d4, 0x86f, 0x80b, 0x7a7, 0x742, 0x6de, 0x67a,
  0x615, 0x5b1, 0x54c, 0x4e8, 0x483, 0x41f, 0x3ba, 0x356,
  0x2f1, 0x28d, 0x228, 0x1c4, 0x15f, 0xfb, 0x96, 0x32,
};

static const q15_t ALIGN4 cos_factorsQ15_2048[2048] = {
  0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff,
  0x7fff, 0x7fff, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffd, 0x7ffd,
  0x7ffd, 0x7ffd, 0x7ffc, 0x7ffc, 0x7ffb, 0x7ffb, 0x7ffb, 0x7ffa,
  0x7ffa, 0x7ff9, 0x7ff9, 0x7ff8, 0x7ff8, 0x7ff7, 0x7ff7, 0x7ff6,
  0x7ff5, 0x7ff5, 0x7ff4, 0x7ff3, 0x7ff3, 0x7ff2, 0x7ff1, 0x7ff0,
  0x7ff0, 0x7fef, 0x7fee, 0x7fed, 0x7fec, 0x7fec, 0x7feb, 0x7fea,
  0x7fe9, 0x7fe8, 0x7fe7, 0x7fe6, 0x7fe5, 0x7fe4, 0x7fe3, 0x7fe2,
  0x7fe1, 0x7fe0, 0x7fdf, 0x7fdd, 0x7fdc, 0x7fdb, 0x7fda, 0x7fd9,
  0x7fd7, 0x7fd6, 0x7fd5, 0x7fd4, 0x7fd2, 0x7fd1, 0x7fd0, 0x7fce,
  0x7fcd, 0x7fcb, 0x7fca, 0x7fc9, 0x7fc7, 0x7fc6, 0x7fc4, 0x7fc3,
  0x7fc1, 0x7fc0, 0x7fbe, 0x7fbc, 0x7fbb, 0x7fb9, 0x7fb7, 0x7fb6,
  0x7fb4, 0x7fb2, 0x7fb1, 0x7faf, 0x7fad, 0x7fab, 0x7fa9, 0x7fa8,
  0x7fa6, 0x7fa4, 0x7fa2, 0x7fa0, 0x7f9e, 0x7f9c, 0x7f9a, 0x7f98,
  0x7f96, 0x7f94, 0x7f92, 0x7f90, 0x7f8e, 0x7f8c, 0x7f8a, 0x7f88,
  0x7f86, 0x7f83, 0x7f81, 0x7f7f, 0x7f7d, 0x7f7b, 0x7f78, 0x7f76,
  0x7f74, 0x7f71, 0x7f6f, 0x7f6d, 0x7f6a, 0x7f68, 0x7f65, 0x7f63,
  0x7f60, 0x7f5e, 0x7f5b, 0x7f59, 0x7f56, 0x7f54, 0x7f51, 0x7f4f,
  0x7f4c, 0x7f49, 0x7f47, 0x7f44, 0x7f41, 0x7f3f, 0x7f3c, 0x7f39,
  0x7f36, 0x7f34, 0x7f31, 0x7f2e, 0x7f2b, 0x7f28, 0x7f25, 0x7f23,
  0x7f20, 0x7f1d, 0x7f1a, 0x7f17, 0x7f14, 0x7f11, 0x7f0e, 0x7f0b,
  0x7f08, 0x7f04, 0x7f01, 0x7efe, 0x7efb, 0x7ef8, 0x7ef5, 0x7ef1,
  0x7eee, 0x7eeb, 0x7ee8, 0x7ee4, 0x7ee1, 0x7ede, 0x7eda, 0x7ed7,
  0x7ed4, 0x7ed0, 0x7ecd, 0x7ec9, 0x7ec6, 0x7ec3, 0x7ebf, 0x7ebb,
  0x7eb8, 0x7eb4, 0x7eb1, 0x7ead, 0x7eaa, 0x7ea6, 0x7ea2, 0x7e9f,
  0x7e9b, 0x7e97, 0x7e94, 0x7e90, 0x7e8c, 0x7e88, 0x7e84, 0x7e81,
  0x7e7d, 0x7e79, 0x7e75, 0x7e71, 0x7e6d, 0x7e69, 0x7e65, 0x7e61,
  0x7e5d, 0x7e59, 0x7e55, 0x7e51, 0x7e4d, 0x7e49, 0x7e45, 0x7e41,
  0x7e3d, 0x7e39, 0x7e34, 0x7e30, 0x7e2c, 0x7e28, 0x7e24, 0x7e1f,
  0x7e1b, 0x7e17, 0x7e12, 0x7e0e, 0x7e0a, 0x7e05, 0x7e01, 0x7dfc,
  0x7df8, 0x7df3, 0x7def, 0x7dea, 0x7de6, 0x7de1, 0x7ddd, 0x7dd8,
  0x7dd4, 0x7dcf, 0x7dca, 0x7dc6, 0x7dc1, 0x7dbc, 0x7db8, 0x7db3,
  0x7dae, 0x7da9, 0x7da5, 0x7da0, 0x7d9b, 0x7d96, 0x7d91, 0x7d8c,
  0x7d87, 0x7d82, 0x7d7e, 0x7d79, 0x7d74, 0x7d6f, 0x7d6a, 0x7d65,
  0x7d60, 0x7d5a, 0x7d55, 0x7d50, 0x7d4b, 0x7d46, 0x7d41, 0x7d3c,
  0x7d36, 0x7d31, 0x7d2c, 0x7d27, 0x7d21, 0x7d1c, 0x7d17, 0x7d11,
  0x7d0c, 0x7d07, 0x7d01, 0x7cfc, 0x7cf6, 0x7cf1, 0x7cec, 0x7ce6,
  0x7ce1, 0x7cdb, 0x7cd5, 0x7cd0, 0x7cca, 0x7cc5, 0x7cbf, 0x7cb9,
  0x7cb4, 0x7cae, 0x7ca8, 0x7ca3, 0x7c9d, 0x7c97, 0x7c91, 0x7c8c,
  0x7c86, 0x7c80, 0x7c7a, 0x7c74, 0x7c6e, 0x7c69, 0x7c63, 0x7c5d,
  0x7c57, 0x7c51, 0x7c4b, 0x7c45, 0x7c3f, 0x7c39, 0x7c33, 0x7c2d,
  0x7c26, 0x7c20, 0x7c1a, 0x7c14, 0x7c0e, 0x7c08, 0x7c01, 0x7bfb,
  0x7bf5, 0x7bef, 0x7be8, 0x7be2, 0x7bdc, 0x7bd5, 0x7bcf, 0x7bc9,
  0x7bc2, 0x7bbc, 0x7bb5, 0x7baf, 0x7ba8, 0x7ba2, 0x7b9b, 0x7b95,
  0x7b8e, 0x7b88, 0x7b81, 0x7b7a, 0x7b74, 0x7b6d, 0x7b67, 0x7b60,
  0x7b59, 0x7b52, 0x7b4c, 0x7b45, 0x7b3e, 0x7b37, 0x7b31, 0x7b2a,
  0x7b23, 0x7b1c, 0x7b15, 0x7b0e, 0x7b07, 0x7b00, 0x7af9, 0x7af2,
  0x7aeb, 0x7ae4, 0x7add, 0x7ad6, 0x7acf, 0x7ac8, 0x7ac1, 0x7aba,
  0x7ab3, 0x7aac, 0x7aa4, 0x7a9d, 0x7a96, 0x7a8f, 0x7a87, 0x7a80,
  0x7a79, 0x7a72, 0x7a6a, 0x7a63, 0x7a5c, 0x7a54, 0x7a4d, 0x7a45,
  0x7a3e, 0x7a36, 0x7a2f, 0x7a27, 0x7a20, 0x7a18, 0x7a11, 0x7a09,
  0x7a02, 0x79fa, 0x79f2, 0x79eb, 0x79e3, 0x79db, 0x79d4, 0x79cc,
  0x79c4, 0x79bc, 0x79b5, 0x79ad, 0x79a5, 0x799d, 0x7995, 0x798e,
  0x7986, 0x797e, 0x7976, 0x796e, 0x7966, 0x795e, 0x7956, 0x794e,
  0x7946, 0x793e, 0x7936, 0x792e, 0x7926, 0x791e, 0x7915, 0x790d,
  0x7905, 0x78fd, 0x78f5, 0x78ec, 0x78e4, 0x78dc, 0x78d4, 0x78cb,
  0x78c3, 0x78bb, 0x78b2, 0x78aa, 0x78a2, 0x7899, 0x7891, 0x7888,
  0x7880, 0x7877, 0x786f, 0x7866, 0x785e, 0x7855, 0x784d, 0x7844,
  0x783b, 0x7833, 0x782a, 0x7821, 0x7819, 0x7810, 0x7807, 0x77ff,
  0x77f6, 0x77ed, 0x77e4, 0x77db, 0x77d3, 0x77ca, 0x77c1, 0x77b8,
  0x77af, 0x77a6, 0x779d, 0x7794, 0x778b, 0x7782, 0x7779, 0x7770,
  0x7767, 0x775e, 0x7755, 0x774c, 0x7743, 0x773a, 0x7731, 0x7727,
  0x771e, 0x7715, 0x770c, 0x7703, 0x76f9, 0x76f0, 0x76e7, 0x76dd,
  0x76d4, 0x76cb, 0x76c1, 0x76b8, 0x76af, 0x76a5, 0x769c, 0x7692,
  0x7689, 0x767f, 0x7676, 0x766c, 0x7663, 0x7659, 0x7650, 0x7646,
  0x763c, 0x7633, 0x7629, 0x761f, 0x7616, 0x760c, 0x7602, 0x75f9,
  0x75ef, 0x75e5, 0x75db, 0x75d1, 0x75c8, 0x75be, 0x75b4, 0x75aa,
  0x75a0, 0x7596, 0x758c, 0x7582, 0x7578, 0x756e, 0x7564, 0x755a,
  0x7550, 0x7546, 0x753c, 0x7532, 0x7528, 0x751e, 0x7514, 0x7509,
  0x74ff, 0x74f5, 0x74eb, 0x74e1, 0x74d6, 0x74cc, 0x74c2, 0x74b7,
  0x74ad, 0x74a3, 0x7498, 0x748e, 0x7484, 0x7479, 0x746f, 0x7464,
  0x745a, 0x744f, 0x7445, 0x743a, 0x7430, 0x7425, 0x741b, 0x7410,
  0x7406, 0x73fb, 0x73f0, 0x73e6, 0x73db, 0x73d0, 0x73c6, 0x73bb,
  0x73b0, 0x73a5, 0x739b, 0x7390, 0x7385, 0x737a, 0x736f, 0x7364,
  0x7359, 0x734f, 0x7344, 0x7339, 0x732e, 0x7323, 0x7318, 0x730d,
  0x7302, 0x72f7, 0x72ec, 0x72e1, 0x72d5, 0x72ca, 0x72bf, 0x72b4,
  0x72a9, 0x729e, 0x7293, 0x7287, 0x727c, 0x7271, 0x7266, 0x725a,
  0x724f, 0x7244, 0x7238, 0x722d, 0x7222, 0x7216, 0x720b, 0x71ff,
  0x71f4, 0x71e9, 0x71dd, 0x71d2, 0x71c6, 0x71bb, 0x71af, 0x71a3,
  0x7198, 0x718c, 0x7181, 0x7175, 0x7169, 0x715e, 0x7152, 0x7146,
  0x713b, 0x712f, 0x7123, 0x7117, 0x710c, 0x7100, 0x70f4, 0x70e8,
  0x70dc, 0x70d1, 0x70c5, 0x70b9, 0x70ad, 0x70a1, 0x7095, 0x7089,
  0x707d, 0x7071, 0x7065, 0x7059, 0x704d, 0x7041, 0x7035, 0x7029,
  0x701d, 0x7010, 0x7004, 0x6ff8, 0x6fec, 0x6fe0, 0x6fd3, 0x6fc7,
  0x6fbb, 0x6faf, 0x6fa2, 0x6f96, 0x6f8a, 0x6f7d, 0x6f71, 0x6f65,
  0x6f58, 0x6f4c, 0x6f3f, 0x6f33, 0x6f27, 0x6f1a, 0x6f0e, 0x6f01,
  0x6ef5, 0x6ee8, 0x6edc, 0x6ecf, 0x6ec2, 0x6eb6, 0x6ea9, 0x6e9c,
  0x6e90, 0x6e83, 0x6e76, 0x6e6a, 0x6e5d, 0x6e50, 0x6e44, 0x6e37,
  0x6e2a, 0x6e1d, 0x6e10, 0x6e04, 0x6df7, 0x6dea, 0x6ddd, 0x6dd0,
  0x6dc3, 0x6db6, 0x6da9, 0x6d9c, 0x6d8f, 0x6d82, 0x6d75, 0x6d68,
  0x6d5b, 0x6d4e, 0x6d41, 0x6d34, 0x6d27, 0x6d1a, 0x6d0c, 0x6cff,
  0x6cf2, 0x6ce5, 0x6cd8, 0x6cca, 0x6cbd, 0x6cb0, 0x6ca3, 0x6c95,
  0x6c88, 0x6c7b, 0x6c6d, 0x6c60, 0x6c53, 0x6c45, 0x6c38, 0x6c2a,
  0x6c1d, 0x6c0f, 0x6c02, 0x6bf5, 0x6be7, 0x6bd9, 0x6bcc, 0x6bbe,
  0x6bb1, 0x6ba3, 0x6b96, 0x6b88, 0x6b7a, 0x6b6d, 0x6b5f, 0x6b51,
  0x6b44, 0x6b36, 0x6b28, 0x6b1a, 0x6b0d, 0x6aff, 0x6af1, 0x6ae3,
  0x6ad5, 0x6ac8, 0x6aba, 0x6aac, 0x6a9e, 0x6a90, 0x6a82, 0x6a74,
  0x6a66, 0x6a58, 0x6a4a, 0x6a3c, 0x6a2e, 0x6a20, 0x6a12, 0x6a04,
  0x69f6, 0x69e8, 0x69da, 0x69cb, 0x69bd, 0x69af, 0x69a1, 0x6993,
  0x6985, 0x6976, 0x6968, 0x695a, 0x694b, 0x693d, 0x692f, 0x6921,
  0x6912, 0x6904, 0x68f5, 0x68e7, 0x68d9, 0x68ca, 0x68bc, 0x68ad,
  0x689f, 0x6890, 0x6882, 0x6873, 0x6865, 0x6856, 0x6848, 0x6839,
  0x682b, 0x681c, 0x680d, 0x67ff, 0x67f0, 0x67e1, 0x67d3, 0x67c4,
  0x67b5, 0x67a6, 0x6798, 0x6789, 0x677a, 0x676b, 0x675d, 0x674e,
  0x673f, 0x6730, 0x6721, 0x6712, 0x6703, 0x66f4, 0x66e5, 0x66d6,
  0x66c8, 0x66b9, 0x66aa, 0x669b, 0x668b, 0x667c, 0x666d, 0x665e,
  0x664f, 0x6640, 0x6631, 0x6622, 0x6613, 0x6603, 0x65f4, 0x65e5,
  0x65d6, 0x65c7, 0x65b7, 0x65a8, 0x6599, 0x658a, 0x657a, 0x656b,
  0x655c, 0x654c, 0x653d, 0x652d, 0x651e, 0x650f, 0x64ff, 0x64f0,
  0x64e0, 0x64d1, 0x64c1, 0x64b2, 0x64a2, 0x6493, 0x6483, 0x6474,
  0x6464, 0x6454, 0x6445, 0x6435, 0x6426, 0x6416, 0x6406, 0x63f7,
  0x63e7, 0x63d7, 0x63c7, 0x63b8, 0x63a8, 0x6398, 0x6388, 0x6378,
  0x6369, 0x6359, 0x6349, 0x6339, 0x6329, 0x6319, 0x6309, 0x62f9,
  0x62ea, 0x62da, 0x62ca, 0x62ba, 0x62aa, 0x629a, 0x628a, 0x627a,
  0x6269, 0x6259, 0x6249, 0x6239, 0x6229, 0x6219, 0x6209, 0x61f9,
  0x61e8, 0x61d8, 0x61c8, 0x61b8, 0x61a8, 0x6197, 0x6187, 0x6177,
  0x6166, 0x6156, 0x6146, 0x6135, 0x6125, 0x6115, 0x6104, 0x60f4,
  0x60e4, 0x60d3, 0x60c3, 0x60b2, 0x60a2, 0x6091, 0x6081, 0x6070,
  0x6060, 0x604f, 0x603f, 0x602e, 0x601d, 0x600d, 0x5ffc, 0x5fec,
  0x5fdb, 0x5fca, 0x5fba, 0x5fa9, 0x5f98, 0x5f87, 0x5f77, 0x5f66,
  0x5f55, 0x5f44, 0x5f34, 0x5f23, 0x5f12, 0x5f01, 0x5ef0, 0x5edf,
  0x5ecf, 0x5ebe, 0x5ead, 0x5e9c, 0x5e8b, 0x5e7a, 0x5e69, 0x5e58,
  0x5e47, 0x5e36, 0x5e25, 0x5e14, 0x5e03, 0x5df2, 0x5de1, 0x5dd0,
  0x5dbf, 0x5dad, 0x5d9c, 0x5d8b, 0x5d7a, 0x5d69, 0x5d58, 0x5d46,
  0x5d35, 0x5d24, 0x5d13, 0x5d01, 0x5cf0, 0x5cdf, 0x5cce, 0x5cbc,
  0x5cab, 0x5c9a, 0x5c88, 0x5c77, 0x5c66, 0x5c54, 0x5c43, 0x5c31,
  0x5c20, 0x5c0e, 0x5bfd, 0x5beb, 0x5bda, 0x5bc8, 0x5bb7, 0x5ba5,
  0x5b94, 0x5b82, 0x5b71, 0x5b5f, 0x5b4d, 0x5b3c, 0x5b2a, 0x5b19,
  0x5b07, 0x5af5, 0x5ae4, 0x5ad2, 0x5ac0, 0x5aae, 0x5a9d, 0x5a8b,
  0x5a79, 0x5a67, 0x5a56, 0x5a44, 0x5a32, 0x5a20, 0x5a0e, 0x59fc,
  0x59ea, 0x59d9, 0x59c7, 0x59b5, 0x59a3, 0x5991, 0x597f, 0x596d,
  0x595b, 0x5949, 0x5937, 0x5925, 0x5913, 0x5901, 0x58ef, 0x58dd,
  0x58cb, 0x58b8, 0x58a6, 0x5894, 0x5882, 0x5870, 0x585e, 0x584b,
  0x5839, 0x5827, 0x5815, 0x5803, 0x57f0, 0x57de, 0x57cc, 0x57b9,
  0x57a7, 0x5795, 0x5783, 0x5770, 0x575e, 0x574b, 0x5739, 0x5727,
  0x5714, 0x5702, 0x56ef, 0x56dd, 0x56ca, 0x56b8, 0x56a5, 0x5693,
  0x5680, 0x566e, 0x565b, 0x5649, 0x5636, 0x5624, 0x5611, 0x55fe,
  0x55ec, 0x55d9, 0x55c7, 0x55b4, 0x55a1, 0x558f, 0x557c, 0x5569,
  0x5556, 0x5544, 0x5531, 0x551e, 0x550b, 0x54f9, 0x54e6, 0x54d3,
  0x54c0, 0x54ad, 0x549a, 0x5488, 0x5475, 0x5462, 0x544f, 0x543c,
  0x5429, 0x5416, 0x5403, 0x53f0, 0x53dd, 0x53ca, 0x53b7, 0x53a4,
  0x5391, 0x537e, 0x536b, 0x5358, 0x5345, 0x5332, 0x531f, 0x530c,
  0x52f8, 0x52e5, 0x52d2, 0x52bf, 0x52ac, 0x5299, 0x5285, 0x5272,
  0x525f, 0x524c, 0x5238, 0x5225, 0x5212, 0x51ff, 0x51eb, 0x51d8,
  0x51c5, 0x51b1, 0x519e, 0x518b, 0x5177, 0x5164, 0x5150, 0x513d,
  0x512a, 0x5116, 0x5103, 0x50ef, 0x50dc, 0x50c8, 0x50b5, 0x50a1,
  0x508e, 0x507a, 0x5067, 0x5053, 0x503f, 0x502c, 0x5018, 0x5005,
  0x4ff1, 0x4fdd, 0x4fca, 0x4fb6, 0x4fa2, 0x4f8f, 0x4f7b, 0x4f67,
  0x4f54, 0x4f40, 0x4f2c, 0x4f18, 0x4f05, 0x4ef1, 0x4edd, 0x4ec9,
  0x4eb6, 0x4ea2, 0x4e8e, 0x4e7a, 0x4e66, 0x4e52, 0x4e3e, 0x4e2a,
  0x4e17, 0x4e03, 0x4def, 0x4ddb, 0x4dc7, 0x4db3, 0x4d9f, 0x4d8b,
  0x4d77, 0x4d63, 0x4d4f, 0x4d3b, 0x4d27, 0x4d13, 0x4cff, 0x4ceb,
  0x4cd6, 0x4cc2, 0x4cae, 0x4c9a, 0x4c86, 0x4c72, 0x4c5e, 0x4c49,
  0x4c35, 0x4c21, 0x4c0d, 0x4bf9, 0x4be4, 0x4bd0, 0x4bbc, 0x4ba8,
  0x4b93, 0x4b7f, 0x4b6b, 0x4b56, 0x4b42, 0x4b2e, 0x4b19, 0x4b05,
  0x4af1, 0x4adc, 0x4ac8, 0x4ab4, 0x4a9f, 0x4a8b, 0x4a76, 0x4a62,
  0x4a4d, 0x4a39, 0x4a24, 0x4a10, 0x49fb, 0x49e7, 0x49d2, 0x49be,
  0x49a9, 0x4995, 0x4980, 0x496c, 0x4957, 0x4942, 0x492e, 0x4919,
  0x4905, 0x48f0, 0x48db, 0x48c7, 0x48b2, 0x489d, 0x4888, 0x4874,
  0x485f, 0x484a, 0x4836, 0x4821, 0x480c, 0x47f7, 0x47e2, 0x47ce,
  0x47b9, 0x47a4, 0x478f, 0x477a, 0x4765, 0x4751, 0x473c, 0x4727,
  0x4712, 0x46fd, 0x46e8, 0x46d3, 0x46be, 0x46a9, 0x4694, 0x467f,
  0x466a, 0x4655, 0x4640, 0x462b, 0x4616, 0x4601, 0x45ec, 0x45d7,
  0x45c2, 0x45ad, 0x4598, 0x4583, 0x456e, 0x4559, 0x4544, 0x452e,
  0x4519, 0x4504, 0x44ef, 0x44da, 0x44c5, 0x44af, 0x449a, 0x4485,
  0x4470, 0x445a, 0x4445, 0x4430, 0x441b, 0x4405, 0x43f0, 0x43db,
  0x43c5, 0x43b0, 0x439b, 0x4385, 0x4370, 0x435b, 0x4345, 0x4330,
  0x431b, 0x4305, 0x42f0, 0x42da, 0x42c5, 0x42af, 0x429a, 0x4284,
  0x426f, 0x425a, 0x4244, 0x422f, 0x4219, 0x4203, 0x41ee, 0x41d8,
  0x41c3, 0x41ad, 0x4198, 0x4182, 0x416d, 0x4157, 0x4141, 0x412c,
  0x4116, 0x4100, 0x40eb, 0x40d5, 0x40bf, 0x40aa, 0x4094, 0x407e,
  0x4069, 0x4053, 0x403d, 0x4027, 0x4012, 0x3ffc, 0x3fe6, 0x3fd0,
  0x3fbb, 0x3fa5, 0x3f8f, 0x3f79, 0x3f63, 0x3f4d, 0x3f38, 0x3f22,
  0x3f0c, 0x3ef6, 0x3ee0, 0x3eca, 0x3eb4, 0x3e9e, 0x3e88, 0x3e73,
  0x3e5d, 0x3e47, 0x3e31, 0x3e1b, 0x3e05, 0x3def, 0x3dd9, 0x3dc3,
  0x3dad, 0x3d97, 0x3d81, 0x3d6b, 0x3d55, 0x3d3e, 0x3d28, 0x3d12,
  0x3cfc, 0x3ce6, 0x3cd0, 0x3cba, 0x3ca4, 0x3c8e, 0x3c77, 0x3c61,
  0x3c4b, 0x3c35, 0x3c1f, 0x3c09, 0x3bf2, 0x3bdc, 0x3bc6, 0x3bb0,
  0x3b99, 0x3b83, 0x3b6d, 0x3b57, 0x3b40, 0x3b2a, 0x3b14, 0x3afe,
  0x3ae7, 0x3ad1, 0x3abb, 0x3aa4, 0x3a8e, 0x3a78, 0x3a61, 0x3a4b,
  0x3a34, 0x3a1e, 0x3a08, 0x39f1, 0x39db, 0x39c4, 0x39ae, 0x3998,
  0x3981, 0x396b, 0x3954, 0x393e, 0x3927, 0x3911, 0x38fa, 0x38e4,
  0x38cd, 0x38b7, 0x38a0, 0x388a, 0x3873, 0x385d, 0x3846, 0x382f,
  0x3819, 0x3802, 0x37ec, 0x37d5, 0x37be, 0x37a8, 0x3791, 0x377a,
  0x3764, 0x374d, 0x3736, 0x3720, 0x3709, 0x36f2, 0x36dc, 0x36c5,
  0x36ae, 0x3698, 0x3681, 0x366a, 0x3653, 0x363d, 0x3626, 0x360f,
  0x35f8, 0x35e1, 0x35cb, 0x35b4, 0x359d, 0x3586, 0x356f, 0x3558,
  0x3542, 0x352b, 0x3514, 0x34fd, 0x34e6, 0x34cf, 0x34b8, 0x34a1,
  0x348b, 0x3474, 0x345d, 0x3446, 0x342f, 0x3418, 0x3401, 0x33ea,
  0x33d3, 0x33bc, 0x33a5, 0x338e, 0x3377, 0x3360, 0x3349, 0x3332,
  0x331b, 0x3304, 0x32ed, 0x32d6, 0x32bf, 0x32a8, 0x3290, 0x3279,
  0x3262, 0x324b, 0x3234, 0x321d, 0x3206, 0x31ef, 0x31d8, 0x31c0,
  0x31a9, 0x3192, 0x317b, 0x3164, 0x314c, 0x3135, 0x311e, 0x3107,
  0x30f0, 0x30d8, 0x30c1, 0x30aa, 0x3093, 0x307b, 0x3064, 0x304d,
  0x3036, 0x301e, 0x3007, 0x2ff0, 0x2fd8, 0x2fc1, 0x2faa, 0x2f92,
  0x2f7b, 0x2f64, 0x2f4c, 0x2f35, 0x2f1e, 0x2f06, 0x2eef, 0x2ed8,
  0x2ec0, 0x2ea9, 0x2e91, 0x2e7a, 0x2e63, 0x2e4b, 0x2e34, 0x2e1c,
  0x2e05, 0x2ded, 0x2dd6, 0x2dbe, 0x2da7, 0x2d8f, 0x2d78, 0x2d60,
  0x2d49, 0x2d31, 0x2d1a, 0x2d02, 0x2ceb, 0x2cd3, 0x2cbc, 0x2ca4,
  0x2c8d, 0x2c75, 0x2c5e, 0x2c46, 0x2c2e, 0x2c17, 0x2bff, 0x2be8,
  0x2bd0, 0x2bb8, 0x2ba1, 0x2b89, 0x2b71, 0x2b5a, 0x2b42, 0x2b2b,
  0x2b13, 0x2afb, 0x2ae4, 0x2acc, 0x2ab4, 0x2a9c, 0x2a85, 0x2a6d,
  0x2a55, 0x2a3e, 0x2a26, 0x2a0e, 0x29f6, 0x29df, 0x29c7, 0x29af,
  0x2997, 0x2980, 0x2968, 0x2950, 0x2938, 0x2920, 0x2909, 0x28f1,
  0x28d9, 0x28c1, 0x28a9, 0x2892, 0x287a, 0x2862, 0x284a, 0x2832,
  0x281a, 0x2802, 0x27eb, 0x27d3, 0x27bb, 0x27a3, 0x278b, 0x2773,
  0x275b, 0x2743, 0x272b, 0x2713, 0x26fb, 0x26e4, 0x26cc, 0x26b4,
  0x269c, 0x2684, 0x266c, 0x2654, 0x263c, 0x2624, 0x260c, 0x25f4,
  0x25dc, 0x25c4, 0x25ac, 0x2594, 0x257c, 0x2564, 0x254c, 0x2534,
  0x251c, 0x2503, 0x24eb, 0x24d3, 0x24bb, 0x24a3, 0x248b, 0x2473,
  0x245b, 0x2443, 0x242b, 0x2413, 0x23fa, 0x23e2, 0x23ca, 0x23b2,
  0x239a, 0x2382, 0x236a, 0x2352, 0x2339, 0x2321, 0x2309, 0x22f1,
  0x22d9, 0x22c0, 0x22a8, 0x2290, 0x2278, 0x2260, 0x2247, 0x222f,
  0x2217, 0x21ff, 0x21e7, 0x21ce, 0x21b6, 0x219e, 0x2186, 0x216d,
  0x2155, 0x213d, 0x2125, 0x210c, 0x20f4, 0x20dc, 0x20c3, 0x20ab,
  0x2093, 0x207a, 0x2062, 0x204a, 0x2032, 0x2019, 0x2001, 0x1fe9,
  0x1fd0, 0x1fb8, 0x1f9f, 0x1f87, 0x1f6f, 0x1f56, 0x1f3e, 0x1f26,
  0x1f0d, 0x1ef5, 0x1edd, 0x1ec4, 0x1eac, 0x1e93, 0x1e7b, 0x1e62,
  0x1e4a, 0x1e32, 0x1e19, 0x1e01, 0x1de8, 0x1dd0, 0x1db7, 0x1d9f,
  0x1d87, 0x1d6e, 0x1d56, 0x1d3d, 0x1d25, 0x1d0c, 0x1cf4, 0x1cdb,
  0x1cc3, 0x1caa, 0x1c92, 0x1c79, 0x1c61, 0x1c48, 0x1c30, 0x1c17,
  0x1bff, 0x1be6, 0x1bce, 0x1bb5, 0x1b9d, 0x1b84, 0x1b6c, 0x1b53,
  0x1b3a, 0x1b22, 0x1b09, 0x1af1, 0x1ad8, 0x1ac0, 0x1aa7, 0x1a8e,
  0x1a76, 0x1a5d, 0x1a45, 0x1a2c, 0x1a13, 0x19fb, 0x19e2, 0x19ca,
  0x19b1, 0x1998, 0x1980, 0x1967, 0x194e, 0x1936, 0x191d, 0x1905,
  0x18ec, 0x18d3, 0x18bb, 0x18a2, 0x1889, 0x1871, 0x1858, 0x183f,
  0x1827, 0x180e, 0x17f5, 0x17dd, 0x17c4, 0x17ab, 0x1792, 0x177a,
  0x1761, 0x1748, 0x1730, 0x1717, 0x16fe, 0x16e5, 0x16cd, 0x16b4,
  0x169b, 0x1682, 0x166a, 0x1651, 0x1638, 0x161f, 0x1607, 0x15ee,
  0x15d5, 0x15bc, 0x15a4, 0x158b, 0x1572, 0x1559, 0x1541, 0x1528,
  0x150f, 0x14f6, 0x14dd, 0x14c5, 0x14ac, 0x1493, 0x147a, 0x1461,
  0x1449, 0x1430, 0x1417, 0x13fe, 0x13e5, 0x13cc, 0x13b4, 0x139b,
  0x1382, 0x1369, 0x1350, 0x1337, 0x131f, 0x1306, 0x12ed, 0x12d4,
  0x12bb, 0x12a2, 0x1289, 0x1271, 0x1258, 0x123f, 0x1226, 0x120d,
  0x11f4, 0x11db, 0x11c2, 0x11a9, 0x1191, 0x1178, 0x115f, 0x1146,
  0x112d, 0x1114, 0x10fb, 0x10e2, 0x10c9, 0x10b0, 0x1098, 0x107f,
  0x1066, 0x104d, 0x1034, 0x101b, 0x1002, 0xfe9, 0xfd0, 0xfb7,
  0xf9e, 0xf85, 0xf6c, 0xf53, 0xf3a, 0xf21, 0xf08, 0xef0,
  0xed7, 0xebe, 0xea5, 0xe8c, 0xe73, 0xe5a, 0xe41, 0xe28,
  0xe0f, 0xdf6, 0xddd, 0xdc4, 0xdab, 0xd92, 0xd79, 0xd60,
  0xd47, 0xd2e, 0xd15, 0xcfc, 0xce3, 0xcca, 0xcb1, 0xc98,
  0xc7f, 0xc66, 0xc4d, 0xc34, 0xc1b, 0xc02, 0xbe9, 0xbd0,
  0xbb7, 0xb9e, 0xb85, 0xb6c, 0xb53, 0xb3a, 0xb20, 0xb07,
  0xaee, 0xad5, 0xabc, 0xaa3, 0xa8a, 0xa71, 0xa58, 0xa3f,
  0xa26, 0xa0d, 0x9f4, 0x9db, 0x9c2, 0x9a9, 0x990, 0x977,
  0x95e, 0x944, 0x92b, 0x912, 0x8f9, 0x8e0, 0x8c7, 0x8ae,
  0x895, 0x87c, 0x863, 0x84a, 0x831, 0x818, 0x7fe, 0x7e5,
  0x7cc, 0x7b3, 0x79a, 0x781, 0x768, 0x74f, 0x736, 0x71d,
  0x704, 0x6ea, 0x6d1, 0x6b8, 0x69f, 0x686, 0x66d, 0x654,
  0x63b, 0x622, 0x609, 0x5ef, 0x5d6, 0x5bd, 0x5a4, 0x58b,
  0x572, 0x559, 0x540, 0x527, 0x50d, 0x4f4, 0x4db, 0x4c2,
  0x4a9, 0x490, 0x477, 0x45e, 0x445, 0x42b, 0x412, 0x3f9,
  0x3e0, 0x3c7, 0x3ae, 0x395, 0x37c, 0x362, 0x349, 0x330,
  0x317, 0x2fe, 0x2e5, 0x2cc, 0x2b3, 0x299, 0x280, 0x267,
  0x24e, 0x235, 0x21c, 0x203, 0x1ea, 0x1d0, 0x1b7, 0x19e,
  0x185, 0x16c, 0x153, 0x13a, 0x121, 0x107, 0xee, 0xd5,
  0xbc, 0xa3, 0x8a, 0x71, 0x57, 0x3e, 0x25, 0xc,

};

static const q15_t ALIGN4 cos_factorsQ15_8192[8192] = {
  0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff,
  0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff,
  0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff,
  0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff,
  0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff, 0x7fff,
  0x7fff, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe,
  0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe, 0x7ffe,
  0x7ffe, 0x7ffe, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd,
  0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffd, 0x7ffc,
  0x7ffc, 0x7ffc, 0x7ffc, 0x7ffc, 0x7ffc, 0x7ffc, 0x7ffc, 0x7ffc,
  0x7ffc, 0x7ffb, 0x7ffb, 0x7ffb, 0x7ffb, 0x7ffb, 0x7ffb, 0x7ffb,
  0x7ffb, 0x7ffb, 0x7ffb, 0x7ffa, 0x7ffa, 0x7ffa, 0x7ffa, 0x7ffa,
  0x7ffa, 0x7ffa, 0x7ffa, 0x7ffa, 0x7ff9, 0x7ff9, 0x7ff9, 0x7ff9,
  0x7ff9, 0x7ff9, 0x7ff9, 0x7ff9, 0x7ff8, 0x7ff8, 0x7ff8, 0x7ff8,
  0x7ff8, 0x7ff8, 0x7ff8, 0x7ff7, 0x7ff7, 0x7ff7, 0x7ff7, 0x7ff7,
  0x7ff7, 0x7ff7, 0x7ff6, 0x7ff6, 0x7ff6, 0x7ff6, 0x7ff6, 0x7ff6,
  0x7ff6, 0x7ff5, 0x7ff5, 0x7ff5, 0x7ff5, 0x7ff5, 0x7ff5, 0x7ff4,
  0x7ff4, 0x7ff4, 0x7ff4, 0x7ff4, 0x7ff4, 0x7ff3, 0x7ff3, 0x7ff3,
  0x7ff3, 0x7ff3, 0x7ff3, 0x7ff2, 0x7ff2, 0x7ff2, 0x7ff2, 0x7ff2,
  0x7ff1, 0x7ff1, 0x7ff1, 0x7ff1, 0x7ff1, 0x7ff1, 0x7ff0, 0x7ff0,
  0x7ff0, 0x7ff0, 0x7ff0, 0x7fef, 0x7fef, 0x7fef, 0x7fef, 0x7fef,
  0x7fee, 0x7fee, 0x7fee, 0x7fee, 0x7fee, 0x7fed, 0x7fed, 0x7fed,
  0x7fed, 0x7fed, 0x7fec, 0x7fec, 0x7fec, 0x7fec, 0x7feb, 0x7feb,
  0x7feb, 0x7feb, 0x7feb, 0x7fea, 0x7fea, 0x7fea, 0x7fea, 0x7fe9,
  0x7fe9, 0x7fe9, 0x7fe9, 0x7fe8, 0x7fe8, 0x7fe8, 0x7fe8, 0x7fe8,
  0x7fe7, 0x7fe7, 0x7fe7, 0x7fe7, 0x7fe6, 0x7fe6, 0x7fe6, 0x7fe6,
  0x7fe5, 0x7fe5, 0x7fe5, 0x7fe5, 0x7fe4, 0x7fe4, 0x7fe4, 0x7fe4,
  0x7fe3, 0x7fe3, 0x7fe3, 0x7fe2, 0x7fe2, 0x7fe2, 0x7fe2, 0x7fe1,
  0x7fe1, 0x7fe1, 0x7fe1, 0x7fe0, 0x7fe0, 0x7fe0, 0x7fdf, 0x7fdf,
  0x7fdf, 0x7fdf, 0x7fde, 0x7fde, 0x7fde, 0x7fde, 0x7fdd, 0x7fdd,
  0x7fdd, 0x7fdc, 0x7fdc, 0x7fdc, 0x7fdb, 0x7fdb, 0x7fdb, 0x7fdb,
  0x7fda, 0x7fda, 0x7fda, 0x7fd9, 0x7fd9, 0x7fd9, 0x7fd8, 0x7fd8,
  0x7fd8, 0x7fd8, 0x7fd7, 0x7fd7, 0x7fd7, 0x7fd6, 0x7fd6, 0x7fd6,
  0x7fd5, 0x7fd5, 0x7fd5, 0x7fd4, 0x7fd4, 0x7fd4, 0x7fd3, 0x7fd3,
  0x7fd3, 0x7fd2, 0x7fd2, 0x7fd2, 0x7fd1, 0x7fd1, 0x7fd1, 0x7fd0,
  0x7fd0, 0x7fd0, 0x7fcf, 0x7fcf, 0x7fcf, 0x7fce, 0x7fce, 0x7fce,
  0x7fcd, 0x7fcd, 0x7fcd, 0x7fcc, 0x7fcc, 0x7fcc, 0x7fcb, 0x7fcb,
  0x7fcb, 0x7fca, 0x7fca, 0x7fc9, 0x7fc9, 0x7fc9, 0x7fc8, 0x7fc8,
  0x7fc8, 0x7fc7, 0x7fc7, 0x7fc7, 0x7fc6, 0x7fc6, 0x7fc5, 0x7fc5,
  0x7fc5, 0x7fc4, 0x7fc4, 0x7fc4, 0x7fc3, 0x7fc3, 0x7fc2, 0x7fc2,
  0x7fc2, 0x7fc1, 0x7fc1, 0x7fc0, 0x7fc0, 0x7fc0, 0x7fbf, 0x7fbf,
  0x7fbf, 0x7fbe, 0x7fbe, 0x7fbd, 0x7fbd, 0x7fbd, 0x7fbc, 0x7fbc,
  0x7fbb, 0x7fbb, 0x7fbb, 0x7fba, 0x7fba, 0x7fb9, 0x7fb9, 0x7fb8,
  0x7fb8, 0x7fb8, 0x7fb7, 0x7fb7, 0x7fb6, 0x7fb6, 0x7fb6, 0x7fb5,
  0x7fb5, 0x7fb4, 0x7fb4, 0x7fb3, 0x7fb3, 0x7fb3, 0x7fb2, 0x7fb2,
  0x7fb1, 0x7fb1, 0x7fb0, 0x7fb0, 0x7faf, 0x7faf, 0x7faf, 0x7fae,
  0x7fae, 0x7fad, 0x7fad, 0x7fac, 0x7fac, 0x7fac, 0x7fab, 0x7fab,
  0x7faa, 0x7faa, 0x7fa9, 0x7fa9, 0x7fa8, 0x7fa8, 0x7fa7, 0x7fa7,
  0x7fa6, 0x7fa6, 0x7fa6, 0x7fa5, 0x7fa5, 0x7fa4, 0x7fa4, 0x7fa3,
  0x7fa3, 0x7fa2, 0x7fa2, 0x7fa1, 0x7fa1, 0x7fa0, 0x7fa0, 0x7f9f,
  0x7f9f, 0x7f9e, 0x7f9e, 0x7f9d, 0x7f9d, 0x7f9c, 0x7f9c, 0x7f9c,
  0x7f9b, 0x7f9b, 0x7f9a, 0x7f9a, 0x7f99, 0x7f99, 0x7f98, 0x7f98,
  0x7f97, 0x7f97, 0x7f96, 0x7f96, 0x7f95, 0x7f95, 0x7f94, 0x7f94,
  0x7f93, 0x7f92, 0x7f92, 0x7f91, 0x7f91, 0x7f90, 0x7f90, 0x7f8f,
  0x7f8f, 0x7f8e, 0x7f8e, 0x7f8d, 0x7f8d, 0x7f8c, 0x7f8c, 0x7f8b,
  0x7f8b, 0x7f8a, 0x7f8a, 0x7f89, 0x7f89, 0x7f88, 0x7f87, 0x7f87,
  0x7f86, 0x7f86, 0x7f85, 0x7f85, 0x7f84, 0x7f84, 0x7f83, 0x7f83,
  0x7f82, 0x7f81, 0x7f81, 0x7f80, 0x7f80, 0x7f7f, 0x7f7f, 0x7f7e,
  0x7f7e, 0x7f7d, 0x7f7c, 0x7f7c, 0x7f7b, 0x7f7b, 0x7f7a, 0x7f7a,
  0x7f79, 0x7f79, 0x7f78, 0x7f77, 0x7f77, 0x7f76, 0x7f76, 0x7f75,
  0x7f75, 0x7f74, 0x7f73, 0x7f73, 0x7f72, 0x7f72, 0x7f71, 0x7f70,
  0x7f70, 0x7f6f, 0x7f6f, 0x7f6e, 0x7f6d, 0x7f6d, 0x7f6c, 0x7f6c,
  0x7f6b, 0x7f6b, 0x7f6a, 0x7f69, 0x7f69, 0x7f68, 0x7f68, 0x7f67,
  0x7f66, 0x7f66, 0x7f65, 0x7f64, 0x7f64, 0x7f63, 0x7f63, 0x7f62,
  0x7f61, 0x7f61, 0x7f60, 0x7f60, 0x7f5f, 0x7f5e, 0x7f5e, 0x7f5d,
  0x7f5c, 0x7f5c, 0x7f5b, 0x7f5b, 0x7f5a, 0x7f59, 0x7f59, 0x7f58,
  0x7f57, 0x7f57, 0x7f56, 0x7f55, 0x7f55, 0x7f54, 0x7f54, 0x7f53,
  0x7f52, 0x7f52, 0x7f51, 0x7f50, 0x7f50, 0x7f4f, 0x7f4e, 0x7f4e,
  0x7f4d, 0x7f4c, 0x7f4c, 0x7f4b, 0x7f4a, 0x7f4a, 0x7f49, 0x7f48,
  0x7f48, 0x7f47, 0x7f46, 0x7f46, 0x7f45, 0x7f44, 0x7f44, 0x7f43,
  0x7f42, 0x7f42, 0x7f41, 0x7f40, 0x7f40, 0x7f3f, 0x7f3e, 0x7f3e,
  0x7f3d, 0x7f3c, 0x7f3c, 0x7f3b, 0x7f3a, 0x7f3a, 0x7f39, 0x7f38,
  0x7f37, 0x7f37, 0x7f36, 0x7f35, 0x7f35, 0x7f34, 0x7f33, 0x7f33,
  0x7f32, 0x7f31, 0x7f31, 0x7f30, 0x7f2f, 0x7f2e, 0x7f2e, 0x7f2d,
  0x7f2c, 0x7f2c, 0x7f2b, 0x7f2a, 0x7f29, 0x7f29, 0x7f28, 0x7f27,
  0x7f27, 0x7f26, 0x7f25, 0x7f24, 0x7f24, 0x7f23, 0x7f22, 0x7f21,
  0x7f21, 0x7f20, 0x7f1f, 0x7f1f, 0x7f1e, 0x7f1d, 0x7f1c, 0x7f1c,
  0x7f1b, 0x7f1a, 0x7f19, 0x7f19, 0x7f18, 0x7f17, 0x7f16, 0x7f16,
  0x7f15, 0x7f14, 0x7f13, 0x7f13, 0x7f12, 0x7f11, 0x7f10, 0x7f10,
  0x7f0f, 0x7f0e, 0x7f0d, 0x7f0d, 0x7f0c, 0x7f0b, 0x7f0a, 0x7f09,
  0x7f09, 0x7f08, 0x7f07, 0x7f06, 0x7f06, 0x7f05, 0x7f04, 0x7f03,
  0x7f02, 0x7f02, 0x7f01, 0x7f00, 0x7eff, 0x7eff, 0x7efe, 0x7efd,
  0x7efc, 0x7efb, 0x7efb, 0x7efa, 0x7ef9, 0x7ef8, 0x7ef7, 0x7ef7,
  0x7ef6, 0x7ef5, 0x7ef4, 0x7ef3, 0x7ef3, 0x7ef2, 0x7ef1, 0x7ef0,
  0x7eef, 0x7eef, 0x7eee, 0x7eed, 0x7eec, 0x7eeb, 0x7eeb, 0x7eea,
  0x7ee9, 0x7ee8, 0x7ee7, 0x7ee6, 0x7ee6, 0x7ee5, 0x7ee4, 0x7ee3,
  0x7ee2, 0x7ee2, 0x7ee1, 0x7ee0, 0x7edf, 0x7ede, 0x7edd, 0x7edd,
  0x7edc, 0x7edb, 0x7eda, 0x7ed9, 0x7ed8, 0x7ed8, 0x7ed7, 0x7ed6,
  0x7ed5, 0x7ed4, 0x7ed3, 0x7ed2, 0x7ed2, 0x7ed1, 0x7ed0, 0x7ecf,
  0x7ece, 0x7ecd, 0x7ecc, 0x7ecc, 0x7ecb, 0x7eca, 0x7ec9, 0x7ec8,
  0x7ec7, 0x7ec6, 0x7ec6, 0x7ec5, 0x7ec4, 0x7ec3, 0x7ec2, 0x7ec1,
  0x7ec0, 0x7ebf, 0x7ebf, 0x7ebe, 0x7ebd, 0x7ebc, 0x7ebb, 0x7eba,
  0x7eb9, 0x7eb8, 0x7eb8, 0x7eb7, 0x7eb6, 0x7eb5, 0x7eb4, 0x7eb3,
  0x7eb2, 0x7eb1, 0x7eb0, 0x7eaf, 0x7eaf, 0x7eae, 0x7ead, 0x7eac,
  0x7eab, 0x7eaa, 0x7ea9, 0x7ea8, 0x7ea7, 0x7ea6, 0x7ea6, 0x7ea5,
  0x7ea4, 0x7ea3, 0x7ea2, 0x7ea1, 0x7ea0, 0x7e9f, 0x7e9e, 0x7e9d,
  0x7e9c, 0x7e9b, 0x7e9b, 0x7e9a, 0x7e99, 0x7e98, 0x7e97, 0x7e96,
  0x7e95, 0x7e94, 0x7e93, 0x7e92, 0x7e91, 0x7e90, 0x7e8f, 0x7e8e,
  0x7e8d, 0x7e8d, 0x7e8c, 0x7e8b, 0x7e8a, 0x7e89, 0x7e88, 0x7e87,
  0x7e86, 0x7e85, 0x7e84, 0x7e83, 0x7e82, 0x7e81, 0x7e80, 0x7e7f,
  0x7e7e, 0x7e7d, 0x7e7c, 0x7e7b, 0x7e7a, 0x7e79, 0x7e78, 0x7e77,
  0x7e77, 0x7e76, 0x7e75, 0x7e74, 0x7e73, 0x7e72, 0x7e71, 0x7e70,
  0x7e6f, 0x7e6e, 0x7e6d, 0x7e6c, 0x7e6b, 0x7e6a, 0x7e69, 0x7e68,
  0x7e67, 0x7e66, 0x7e65, 0x7e64, 0x7e63, 0x7e62, 0x7e61, 0x7e60,
  0x7e5f, 0x7e5e, 0x7e5d, 0x7e5c, 0x7e5b, 0x7e5a, 0x7e59, 0x7e58,
  0x7e57, 0x7e56, 0x7e55, 0x7e54, 0x7e53, 0x7e52, 0x7e51, 0x7e50,
  0x7e4f, 0x7e4e, 0x7e4d, 0x7e4c, 0x7e4b, 0x7e4a, 0x7e49, 0x7e48,
  0x7e47, 0x7e46, 0x7e45, 0x7e43, 0x7e42, 0x7e41, 0x7e40, 0x7e3f,
  0x7e3e, 0x7e3d, 0x7e3c, 0x7e3b, 0x7e3a, 0x7e39, 0x7e38, 0x7e37,
  0x7e36, 0x7e35, 0x7e34, 0x7e33, 0x7e32, 0x7e31, 0x7e30, 0x7e2f,
  0x7e2e, 0x7e2d, 0x7e2b, 0x7e2a, 0x7e29, 0x7e28, 0x7e27, 0x7e26,
  0x7e25, 0x7e24, 0x7e23, 0x7e22, 0x7e21, 0x7e20, 0x7e1f, 0x7e1e,
  0x7e1d, 0x7e1b, 0x7e1a, 0x7e19, 0x7e18, 0x7e17, 0x7e16, 0x7e15,
  0x7e14, 0x7e13, 0x7e12, 0x7e11, 0x7e10, 0x7e0e, 0x7e0d, 0x7e0c,
  0x7e0b, 0x7e0a, 0x7e09, 0x7e08, 0x7e07, 0x7e06, 0x7e05, 0x7e04,
  0x7e02, 0x7e01, 0x7e00, 0x7dff, 0x7dfe, 0x7dfd, 0x7dfc, 0x7dfb,
  0x7dfa, 0x7df8, 0x7df7, 0x7df6, 0x7df5, 0x7df4, 0x7df3, 0x7df2,
  0x7df1, 0x7def, 0x7dee, 0x7ded, 0x7dec, 0x7deb, 0x7dea, 0x7de9,
  0x7de8, 0x7de6, 0x7de5, 0x7de4, 0x7de3, 0x7de2, 0x7de1, 0x7de0,
  0x7dde, 0x7ddd, 0x7ddc, 0x7ddb, 0x7dda, 0x7dd9, 0x7dd8, 0x7dd6,
  0x7dd5, 0x7dd4, 0x7dd3, 0x7dd2, 0x7dd1, 0x7dd0, 0x7dce, 0x7dcd,
  0x7dcc, 0x7dcb, 0x7dca, 0x7dc9, 0x7dc7, 0x7dc6, 0x7dc5, 0x7dc4,
  0x7dc3, 0x7dc2, 0x7dc0, 0x7dbf, 0x7dbe, 0x7dbd, 0x7dbc, 0x7dbb,
  0x7db9, 0x7db8, 0x7db7, 0x7db6, 0x7db5, 0x7db3, 0x7db2, 0x7db1,
  0x7db0, 0x7daf, 0x7dae, 0x7dac, 0x7dab, 0x7daa, 0x7da9, 0x7da8,
  0x7da6, 0x7da5, 0x7da4, 0x7da3, 0x7da2, 0x7da0, 0x7d9f, 0x7d9e,
  0x7d9d, 0x7d9c, 0x7d9a, 0x7d99, 0x7d98, 0x7d97, 0x7d95, 0x7d94,
  0x7d93, 0x7d92, 0x7d91, 0x7d8f, 0x7d8e, 0x7d8d, 0x7d8c, 0x7d8a,
  0x7d89, 0x7d88, 0x7d87, 0x7d86, 0x7d84, 0x7d83, 0x7d82, 0x7d81,
  0x7d7f, 0x7d7e, 0x7d7d, 0x7d7c, 0x7d7a, 0x7d79, 0x7d78, 0x7d77,
  0x7d75, 0x7d74, 0x7d73, 0x7d72, 0x7d70, 0x7d6f, 0x7d6e, 0x7d6d,
  0x7d6b, 0x7d6a, 0x7d69, 0x7d68, 0x7d66, 0x7d65, 0x7d64, 0x7d63,
  0x7d61, 0x7d60, 0x7d5f, 0x7d5e, 0x7d5c, 0x7d5b, 0x7d5a, 0x7d59,
  0x7d57, 0x7d56, 0x7d55, 0x7d53, 0x7d52, 0x7d51, 0x7d50, 0x7d4e,
  0x7d4d, 0x7d4c, 0x7d4a, 0x7d49, 0x7d48, 0x7d47, 0x7d45, 0x7d44,
  0x7d43, 0x7d41, 0x7d40, 0x7d3f, 0x7d3e, 0x7d3c, 0x7d3b, 0x7d3a,
  0x7d38, 0x7d37, 0x7d36, 0x7d34, 0x7d33, 0x7d32, 0x7d31, 0x7d2f,
  0x7d2e, 0x7d2d, 0x7d2b, 0x7d2a, 0x7d29, 0x7d27, 0x7d26, 0x7d25,
  0x7d23, 0x7d22, 0x7d21, 0x7d1f, 0x7d1e, 0x7d1d, 0x7d1b, 0x7d1a,
  0x7d19, 0x7d17, 0x7d16, 0x7d15, 0x7d13, 0x7d12, 0x7d11, 0x7d0f,
  0x7d0e, 0x7d0d, 0x7d0b, 0x7d0a, 0x7d09, 0x7d07, 0x7d06, 0x7d05,
  0x7d03, 0x7d02, 0x7d01, 0x7cff, 0x7cfe, 0x7cfd, 0x7cfb, 0x7cfa,
  0x7cf9, 0x7cf7, 0x7cf6, 0x7cf4, 0x7cf3, 0x7cf2, 0x7cf0, 0x7cef,
  0x7cee, 0x7cec, 0x7ceb, 0x7ce9, 0x7ce8, 0x7ce7, 0x7ce5, 0x7ce4,
  0x7ce3, 0x7ce1, 0x7ce0, 0x7cde, 0x7cdd, 0x7cdc, 0x7cda, 0x7cd9,
  0x7cd8, 0x7cd6, 0x7cd5, 0x7cd3, 0x7cd2, 0x7cd1, 0x7ccf, 0x7cce,
  0x7ccc, 0x7ccb, 0x7cca, 0x7cc8, 0x7cc7, 0x7cc5, 0x7cc4, 0x7cc3,
  0x7cc1, 0x7cc0, 0x7cbe, 0x7cbd, 0x7cbc, 0x7cba, 0x7cb9, 0x7cb7,
  0x7cb6, 0x7cb5, 0x7cb3, 0x7cb2, 0x7cb0, 0x7caf, 0x7cad, 0x7cac,
  0x7cab, 0x7ca9, 0x7ca8, 0x7ca6, 0x7ca5, 0x7ca3, 0x7ca2, 0x7ca1,
  0x7c9f, 0x7c9e, 0x7c9c, 0x7c9b, 0x7c99, 0x7c98, 0x7c97, 0x7c95,
  0x7c94, 0x7c92, 0x7c91, 0x7c8f, 0x7c8e, 0x7c8c, 0x7c8b, 0x7c8a,
  0x7c88, 0x7c87, 0x7c85, 0x7c84, 0x7c82, 0x7c81, 0x7c7f, 0x7c7e,
  0x7c7c, 0x7c7b, 0x7c79, 0x7c78, 0x7c77, 0x7c75, 0x7c74, 0x7c72,
  0x7c71, 0x7c6f, 0x7c6e, 0x7c6c, 0x7c6b, 0x7c69, 0x7c68, 0x7c66,
  0x7c65, 0x7c63, 0x7c62, 0x7c60, 0x7c5f, 0x7c5d, 0x7c5c, 0x7c5a,
  0x7c59, 0x7c58, 0x7c56, 0x7c55, 0x7c53, 0x7c52, 0x7c50, 0x7c4f,
  0x7c4d, 0x7c4c, 0x7c4a, 0x7c49, 0x7c47, 0x7c46, 0x7c44, 0x7c43,
  0x7c41, 0x7c3f, 0x7c3e, 0x7c3c, 0x7c3b, 0x7c39, 0x7c38, 0x7c36,
  0x7c35, 0x7c33, 0x7c32, 0x7c30, 0x7c2f, 0x7c2d, 0x7c2c, 0x7c2a,
  0x7c29, 0x7c27, 0x7c26, 0x7c24, 0x7c23, 0x7c21, 0x7c20, 0x7c1e,
  0x7c1c, 0x7c1b, 0x7c19, 0x7c18, 0x7c16, 0x7c15, 0x7c13, 0x7c12,
  0x7c10, 0x7c0f, 0x7c0d, 0x7c0b, 0x7c0a, 0x7c08, 0x7c07, 0x7c05,
  0x7c04, 0x7c02, 0x7c01, 0x7bff, 0x7bfd, 0x7bfc, 0x7bfa, 0x7bf9,
  0x7bf7, 0x7bf6, 0x7bf4, 0x7bf3, 0x7bf1, 0x7bef, 0x7bee, 0x7bec,
  0x7beb, 0x7be9, 0x7be8, 0x7be6, 0x7be4, 0x7be3, 0x7be1, 0x7be0,
  0x7bde, 0x7bdc, 0x7bdb, 0x7bd9, 0x7bd8, 0x7bd6, 0x7bd5, 0x7bd3,
  0x7bd1, 0x7bd0, 0x7bce, 0x7bcd, 0x7bcb, 0x7bc9, 0x7bc8, 0x7bc6,
  0x7bc5, 0x7bc3, 0x7bc1, 0x7bc0, 0x7bbe, 0x7bbd, 0x7bbb, 0x7bb9,
  0x7bb8, 0x7bb6, 0x7bb5, 0x7bb3, 0x7bb1, 0x7bb0, 0x7bae, 0x7bac,
  0x7bab, 0x7ba9, 0x7ba8, 0x7ba6, 0x7ba4, 0x7ba3, 0x7ba1, 0x7b9f,
  0x7b9e, 0x7b9c, 0x7b9b, 0x7b99, 0x7b97, 0x7b96, 0x7b94, 0x7b92,
  0x7b91, 0x7b8f, 0x7b8d, 0x7b8c, 0x7b8a, 0x7b89, 0x7b87, 0x7b85,
  0x7b84, 0x7b82, 0x7b80, 0x7b7f, 0x7b7d, 0x7b7b, 0x7b7a, 0x7b78,
  0x7b76, 0x7b75, 0x7b73, 0x7b71, 0x7b70, 0x7b6e, 0x7b6c, 0x7b6b,
  0x7b69, 0x7b67, 0x7b66, 0x7b64, 0x7b62, 0x7b61, 0x7b5f, 0x7b5d,
  0x7b5c, 0x7b5a, 0x7b58, 0x7b57, 0x7b55, 0x7b53, 0x7b52, 0x7b50,
  0x7b4e, 0x7b4d, 0x7b4b, 0x7b49, 0x7b47, 0x7b46, 0x7b44, 0x7b42,
  0x7b41, 0x7b3f, 0x7b3d, 0x7b3c, 0x7b3a, 0x7b38, 0x7b37, 0x7b35,
  0x7b33, 0x7b31, 0x7b30, 0x7b2e, 0x7b2c, 0x7b2b, 0x7b29, 0x7b27,
  0x7b25, 0x7b24, 0x7b22, 0x7b20, 0x7b1f, 0x7b1d, 0x7b1b, 0x7b19,
  0x7b18, 0x7b16, 0x7b14, 0x7b13, 0x7b11, 0x7b0f, 0x7b0d, 0x7b0c,
  0x7b0a, 0x7b08, 0x7b06, 0x7b05, 0x7b03, 0x7b01, 0x7aff, 0x7afe,
  0x7afc, 0x7afa, 0x7af8, 0x7af7, 0x7af5, 0x7af3, 0x7af2, 0x7af0,
  0x7aee, 0x7aec, 0x7aeb, 0x7ae9, 0x7ae7, 0x7ae5, 0x7ae3, 0x7ae2,
  0x7ae0, 0x7ade, 0x7adc, 0x7adb, 0x7ad9, 0x7ad7, 0x7ad5, 0x7ad4,
  0x7ad2, 0x7ad0, 0x7ace, 0x7acd, 0x7acb, 0x7ac9, 0x7ac7, 0x7ac5,
  0x7ac4, 0x7ac2, 0x7ac0, 0x7abe, 0x7abd, 0x7abb, 0x7ab9, 0x7ab7,
  0x7ab5, 0x7ab4, 0x7ab2, 0x7ab0, 0x7aae, 0x7aac, 0x7aab, 0x7aa9,
  0x7aa7, 0x7aa5, 0x7aa3, 0x7aa2, 0x7aa0, 0x7a9e, 0x7a9c, 0x7a9a,
  0x7a99, 0x7a97, 0x7a95, 0x7a93, 0x7a91, 0x7a90, 0x7a8e, 0x7a8c,
  0x7a8a, 0x7a88, 0x7a87, 0x7a85, 0x7a83, 0x7a81, 0x7a7f, 0x7a7d,
  0x7a7c, 0x7a7a, 0x7a78, 0x7a76, 0x7a74, 0x7a72, 0x7a71, 0x7a6f,
  0x7a6d, 0x7a6b, 0x7a69, 0x7a67, 0x7a66, 0x7a64, 0x7a62, 0x7a60,
  0x7a5e, 0x7a5c, 0x7a5b, 0x7a59, 0x7a57, 0x7a55, 0x7a53, 0x7a51,
  0x7a4f, 0x7a4e, 0x7a4c, 0x7a4a, 0x7a48, 0x7a46, 0x7a44, 0x7a42,
  0x7a41, 0x7a3f, 0x7a3d, 0x7a3b, 0x7a39, 0x7a37, 0x7a35, 0x7a34,
  0x7a32, 0x7a30, 0x7a2e, 0x7a2c, 0x7a2a, 0x7a28, 0x7a26, 0x7a25,
  0x7a23, 0x7a21, 0x7a1f, 0x7a1d, 0x7a1b, 0x7a19, 0x7a17, 0x7a16,
  0x7a14, 0x7a12, 0x7a10, 0x7a0e, 0x7a0c, 0x7a0a, 0x7a08, 0x7a06,
  0x7a04, 0x7a03, 0x7a01, 0x79ff, 0x79fd, 0x79fb, 0x79f9, 0x79f7,
  0x79f5, 0x79f3, 0x79f1, 0x79f0, 0x79ee, 0x79ec, 0x79ea, 0x79e8,
  0x79e6, 0x79e4, 0x79e2, 0x79e0, 0x79de, 0x79dc, 0x79da, 0x79d9,
  0x79d7, 0x79d5, 0x79d3, 0x79d1, 0x79cf, 0x79cd, 0x79cb, 0x79c9,
  0x79c7, 0x79c5, 0x79c3, 0x79c1, 0x79bf, 0x79bd, 0x79bc, 0x79ba,
  0x79b8, 0x79b6, 0x79b4, 0x79b2, 0x79b0, 0x79ae, 0x79ac, 0x79aa,
  0x79a8, 0x79a6, 0x79a4, 0x79a2, 0x79a0, 0x799e, 0x799c, 0x799a,
  0x7998, 0x7996, 0x7994, 0x7992, 0x7991, 0x798f, 0x798d, 0x798b,
  0x7989, 0x7987, 0x7985, 0x7983, 0x7981, 0x797f, 0x797d, 0x797b,
  0x7979, 0x7977, 0x7975, 0x7973, 0x7971, 0x796f, 0x796d, 0x796b,
  0x7969, 0x7967, 0x7965, 0x7963, 0x7961, 0x795f, 0x795d, 0x795b,
  0x7959, 0x7957, 0x7955, 0x7953, 0x7951, 0x794f, 0x794d, 0x794b,
  0x7949, 0x7947, 0x7945, 0x7943, 0x7941, 0x793f, 0x793d, 0x793b,
  0x7939, 0x7937, 0x7935, 0x7933, 0x7931, 0x792f, 0x792d, 0x792b,
  0x7929, 0x7927, 0x7925, 0x7923, 0x7921, 0x791f, 0x791d, 0x791a,
  0x7918, 0x7916, 0x7914, 0x7912, 0x7910, 0x790e, 0x790c, 0x790a,
  0x7908, 0x7906, 0x7904, 0x7902, 0x7900, 0x78fe, 0x78fc, 0x78fa,
  0x78f8, 0x78f6, 0x78f4, 0x78f2, 0x78f0, 0x78ed, 0x78eb, 0x78e9,
  0x78e7, 0x78e5, 0x78e3, 0x78e1, 0x78df, 0x78dd, 0x78db, 0x78d9,
  0x78d7, 0x78d5, 0x78d3, 0x78d1, 0x78ce, 0x78cc, 0x78ca, 0x78c8,
  0x78c6, 0x78c4, 0x78c2, 0x78c0, 0x78be, 0x78bc, 0x78ba, 0x78b8,
  0x78b5, 0x78b3, 0x78b1, 0x78af, 0x78ad, 0x78ab, 0x78a9, 0x78a7,
  0x78a5, 0x78a3, 0x78a0, 0x789e, 0x789c, 0x789a, 0x7898, 0x7896,
  0x7894, 0x7892, 0x7890, 0x788e, 0x788b, 0x7889, 0x7887, 0x7885,
  0x7883, 0x7881, 0x787f, 0x787d, 0x787a, 0x7878, 0x7876, 0x7874,
  0x7872, 0x7870, 0x786e, 0x786c, 0x7869, 0x7867, 0x7865, 0x7863,
  0x7861, 0x785f, 0x785d, 0x785b, 0x7858, 0x7856, 0x7854, 0x7852,
  0x7850, 0x784e, 0x784c, 0x7849, 0x7847, 0x7845, 0x7843, 0x7841,
  0x783f, 0x783c, 0x783a, 0x7838, 0x7836, 0x7834, 0x7832, 0x7830,
  0x782d, 0x782b, 0x7829, 0x7827, 0x7825, 0x7823, 0x7820, 0x781e,
  0x781c, 0x781a, 0x7818, 0x7816, 0x7813, 0x7811, 0x780f, 0x780d,
  0x780b, 0x7808, 0x7806, 0x7804, 0x7802, 0x7800, 0x77fe, 0x77fb,
  0x77f9, 0x77f7, 0x77f5, 0x77f3, 0x77f0, 0x77ee, 0x77ec, 0x77ea,
  0x77e8, 0x77e5, 0x77e3, 0x77e1, 0x77df, 0x77dd, 0x77da, 0x77d8,
  0x77d6, 0x77d4, 0x77d2, 0x77cf, 0x77cd, 0x77cb, 0x77c9, 0x77c6,
  0x77c4, 0x77c2, 0x77c0, 0x77be, 0x77bb, 0x77b9, 0x77b7, 0x77b5,
  0x77b2, 0x77b0, 0x77ae, 0x77ac, 0x77aa, 0x77a7, 0x77a5, 0x77a3,
  0x77a1, 0x779e, 0x779c, 0x779a, 0x7798, 0x7795, 0x7793, 0x7791,
  0x778f, 0x778c, 0x778a, 0x7788, 0x7786, 0x7783, 0x7781, 0x777f,
  0x777d, 0x777a, 0x7778, 0x7776, 0x7774, 0x7771, 0x776f, 0x776d,
  0x776b, 0x7768, 0x7766, 0x7764, 0x7762, 0x775f, 0x775d, 0x775b,
  0x7759, 0x7756, 0x7754, 0x7752, 0x774f, 0x774d, 0x774b, 0x7749,
  0x7746, 0x7744, 0x7742, 0x773f, 0x773d, 0x773b, 0x7739, 0x7736,
  0x7734, 0x7732, 0x772f, 0x772d, 0x772b, 0x7729, 0x7726, 0x7724,
  0x7722, 0x771f, 0x771d, 0x771b, 0x7719, 0x7716, 0x7714, 0x7712,
  0x770f, 0x770d, 0x770b, 0x7708, 0x7706, 0x7704, 0x7701, 0x76ff,
  0x76fd, 0x76fa, 0x76f8, 0x76f6, 0x76f4, 0x76f1, 0x76ef, 0x76ed,
  0x76ea, 0x76e8, 0x76e6, 0x76e3, 0x76e1, 0x76df, 0x76dc, 0x76da,
  0x76d8, 0x76d5, 0x76d3, 0x76d1, 0x76ce, 0x76cc, 0x76ca, 0x76c7,
  0x76c5, 0x76c3, 0x76c0, 0x76be, 0x76bc, 0x76b9, 0x76b7, 0x76b4,
  0x76b2, 0x76b0, 0x76ad, 0x76ab, 0x76a9, 0x76a6, 0x76a4, 0x76a2,
  0x769f, 0x769d, 0x769b, 0x7698, 0x7696, 0x7693, 0x7691, 0x768f,
  0x768c, 0x768a, 0x7688, 0x7685, 0x7683, 0x7681, 0x767e, 0x767c,
  0x7679, 0x7677, 0x7675, 0x7672, 0x7670, 0x766d, 0x766b, 0x7669,
  0x7666, 0x7664, 0x7662, 0x765f, 0x765d, 0x765a, 0x7658, 0x7656,
  0x7653, 0x7651, 0x764e, 0x764c, 0x764a, 0x7647, 0x7645, 0x7642,
  0x7640, 0x763e, 0x763b, 0x7639, 0x7636, 0x7634, 0x7632, 0x762f,
  0x762d, 0x762a, 0x7628, 0x7625, 0x7623, 0x7621, 0x761e, 0x761c,
  0x7619, 0x7617, 0x7615, 0x7612, 0x7610, 0x760d, 0x760b, 0x7608,
  0x7606, 0x7604, 0x7601, 0x75ff, 0x75fc, 0x75fa, 0x75f7, 0x75f5,
  0x75f2, 0x75f0, 0x75ee, 0x75eb, 0x75e9, 0x75e6, 0x75e4, 0x75e1,
  0x75df, 0x75dc, 0x75da, 0x75d8, 0x75d5, 0x75d3, 0x75d0, 0x75ce,
  0x75cb, 0x75c9, 0x75c6, 0x75c4, 0x75c1, 0x75bf, 0x75bc, 0x75ba,
  0x75b8, 0x75b5, 0x75b3, 0x75b0, 0x75ae, 0x75ab, 0x75a9, 0x75a6,
  0x75a4, 0x75a1, 0x759f, 0x759c, 0x759a, 0x7597, 0x7595, 0x7592,
  0x7590, 0x758d, 0x758b, 0x7588, 0x7586, 0x7584, 0x7581, 0x757f,
  0x757c, 0x757a, 0x7577, 0x7575, 0x7572, 0x7570, 0x756d, 0x756b,
  0x7568, 0x7566, 0x7563, 0x7561, 0x755e, 0x755c, 0x7559, 0x7556,
  0x7554, 0x7551, 0x754f, 0x754c, 0x754a, 0x7547, 0x7545, 0x7542,
  0x7540, 0x753d, 0x753b, 0x7538, 0x7536, 0x7533, 0x7531, 0x752e,
  0x752c, 0x7529, 0x7527, 0x7524, 0x7522, 0x751f, 0x751c, 0x751a,
  0x7517, 0x7515, 0x7512, 0x7510, 0x750d, 0x750b, 0x7508, 0x7506,
  0x7503, 0x7501, 0x74fe, 0x74fb, 0x74f9, 0x74f6, 0x74f4, 0x74f1,
  0x74ef, 0x74ec, 0x74ea, 0x74e7, 0x74e4, 0x74e2, 0x74df, 0x74dd,
  0x74da, 0x74d8, 0x74d5, 0x74d2, 0x74d0, 0x74cd, 0x74cb, 0x74c8,
  0x74c6, 0x74c3, 0x74c0, 0x74be, 0x74bb, 0x74b9, 0x74b6, 0x74b4,
  0x74b1, 0x74ae, 0x74ac, 0x74a9, 0x74a7, 0x74a4, 0x74a1, 0x749f,
  0x749c, 0x749a, 0x7497, 0x7495, 0x7492, 0x748f, 0x748d, 0x748a,
  0x7488, 0x7485, 0x7482, 0x7480, 0x747d, 0x747b, 0x7478, 0x7475,
  0x7473, 0x7470, 0x746d, 0x746b, 0x7468, 0x7466, 0x7463, 0x7460,
  0x745e, 0x745b, 0x7459, 0x7456, 0x7453, 0x7451, 0x744e, 0x744b,
  0x7449, 0x7446, 0x7444, 0x7441, 0x743e, 0x743c, 0x7439, 0x7436,
  0x7434, 0x7431, 0x742f, 0x742c, 0x7429, 0x7427, 0x7424, 0x7421,
  0x741f, 0x741c, 0x7419, 0x7417, 0x7414, 0x7411, 0x740f, 0x740c,
  0x740a, 0x7407, 0x7404, 0x7402, 0x73ff, 0x73fc, 0x73fa, 0x73f7,
  0x73f4, 0x73f2, 0x73ef, 0x73ec, 0x73ea, 0x73e7, 0x73e4, 0x73e2,
  0x73df, 0x73dc, 0x73da, 0x73d7, 0x73d4, 0x73d2, 0x73cf, 0x73cc,
  0x73ca, 0x73c7, 0x73c4, 0x73c1, 0x73bf, 0x73bc, 0x73b9, 0x73b7,
  0x73b4, 0x73b1, 0x73af, 0x73ac, 0x73a9, 0x73a7, 0x73a4, 0x73a1,
  0x739f, 0x739c, 0x7399, 0x7396, 0x7394, 0x7391, 0x738e, 0x738c,
  0x7389, 0x7386, 0x7384, 0x7381, 0x737e, 0x737b, 0x7379, 0x7376,
  0x7373, 0x7371, 0x736e, 0x736b, 0x7368, 0x7366, 0x7363, 0x7360,
  0x735e, 0x735b, 0x7358, 0x7355, 0x7353, 0x7350, 0x734d, 0x734a,
  0x7348, 0x7345, 0x7342, 0x7340, 0x733d, 0x733a, 0x7337, 0x7335,
  0x7332, 0x732f, 0x732c, 0x732a, 0x7327, 0x7324, 0x7321, 0x731f,
  0x731c, 0x7319, 0x7316, 0x7314, 0x7311, 0x730e, 0x730b, 0x7309,
  0x7306, 0x7303, 0x7300, 0x72fe, 0x72fb, 0x72f8, 0x72f5, 0x72f3,
  0x72f0, 0x72ed, 0x72ea, 0x72e8, 0x72e5, 0x72e2, 0x72df, 0x72dc,
  0x72da, 0x72d7, 0x72d4, 0x72d1, 0x72cf, 0x72cc, 0x72c9, 0x72c6,
  0x72c3, 0x72c1, 0x72be, 0x72bb, 0x72b8, 0x72b5, 0x72b3, 0x72b0,
  0x72ad, 0x72aa, 0x72a8, 0x72a5, 0x72a2, 0x729f, 0x729c, 0x729a,
  0x7297, 0x7294, 0x7291, 0x728e, 0x728c, 0x7289, 0x7286, 0x7283,
  0x7280, 0x727e, 0x727b, 0x7278, 0x7275, 0x7272, 0x726f, 0x726d,
  0x726a, 0x7267, 0x7264, 0x7261, 0x725f, 0x725c, 0x7259, 0x7256,
  0x7253, 0x7250, 0x724e, 0x724b, 0x7248, 0x7245, 0x7242, 0x723f,
  0x723d, 0x723a, 0x7237, 0x7234, 0x7231, 0x722e, 0x722c, 0x7229,
  0x7226, 0x7223, 0x7220, 0x721d, 0x721b, 0x7218, 0x7215, 0x7212,
  0x720f, 0x720c, 0x7209, 0x7207, 0x7204, 0x7201, 0x71fe, 0x71fb,
  0x71f8, 0x71f5, 0x71f3, 0x71f0, 0x71ed, 0x71ea, 0x71e7, 0x71e4,
  0x71e1, 0x71df, 0x71dc, 0x71d9, 0x71d6, 0x71d3, 0x71d0, 0x71cd,
  0x71ca, 0x71c8, 0x71c5, 0x71c2, 0x71bf, 0x71bc, 0x71b9, 0x71b6,
  0x71b3, 0x71b0, 0x71ae, 0x71ab, 0x71a8, 0x71a5, 0x71a2, 0x719f,
  0x719c, 0x7199, 0x7196, 0x7194, 0x7191, 0x718e, 0x718b, 0x7188,
  0x7185, 0x7182, 0x717f, 0x717c, 0x7179, 0x7177, 0x7174, 0x7171,
  0x716e, 0x716b, 0x7168, 0x7165, 0x7162, 0x715f, 0x715c, 0x7159,
  0x7156, 0x7154, 0x7151, 0x714e, 0x714b, 0x7148, 0x7145, 0x7142,
  0x713f, 0x713c, 0x7139, 0x7136, 0x7133, 0x7130, 0x712d, 0x712b,
  0x7128, 0x7125, 0x7122, 0x711f, 0x711c, 0x7119, 0x7116, 0x7113,
  0x7110, 0x710d, 0x710a, 0x7107, 0x7104, 0x7101, 0x70fe, 0x70fb,
  0x70f8, 0x70f6, 0x70f3, 0x70f0, 0x70ed, 0x70ea, 0x70e7, 0x70e4,
  0x70e1, 0x70de, 0x70db, 0x70d8, 0x70d5, 0x70d2, 0x70cf, 0x70cc,
  0x70c9, 0x70c6, 0x70c3, 0x70c0, 0x70bd, 0x70ba, 0x70b7, 0x70b4,
  0x70b1, 0x70ae, 0x70ab, 0x70a8, 0x70a5, 0x70a2, 0x709f, 0x709c,
  0x7099, 0x7096, 0x7093, 0x7090, 0x708d, 0x708a, 0x7087, 0x7084,
  0x7081, 0x707e, 0x707b, 0x7078, 0x7075, 0x7072, 0x706f, 0x706c,
  0x7069, 0x7066, 0x7063, 0x7060, 0x705d, 0x705a, 0x7057, 0x7054,
  0x7051, 0x704e, 0x704b, 0x7048, 0x7045, 0x7042, 0x703f, 0x703c,
  0x7039, 0x7036, 0x7033, 0x7030, 0x702d, 0x702a, 0x7027, 0x7024,
  0x7021, 0x701e, 0x701b, 0x7018, 0x7015, 0x7012, 0x700f, 0x700c,
  0x7009, 0x7006, 0x7003, 0x7000, 0x6ffd, 0x6ffa, 0x6ff7, 0x6ff3,
  0x6ff0, 0x6fed, 0x6fea, 0x6fe7, 0x6fe4, 0x6fe1, 0x6fde, 0x6fdb,
  0x6fd8, 0x6fd5, 0x6fd2, 0x6fcf, 0x6fcc, 0x6fc9, 0x6fc6, 0x6fc3,
  0x6fc0, 0x6fbc, 0x6fb9, 0x6fb6, 0x6fb3, 0x6fb0, 0x6fad, 0x6faa,
  0x6fa7, 0x6fa4, 0x6fa1, 0x6f9e, 0x6f9b, 0x6f98, 0x6f95, 0x6f91,
  0x6f8e, 0x6f8b, 0x6f88, 0x6f85, 0x6f82, 0x6f7f, 0x6f7c, 0x6f79,
  0x6f76, 0x6f73, 0x6f70, 0x6f6c, 0x6f69, 0x6f66, 0x6f63, 0x6f60,
  0x6f5d, 0x6f5a, 0x6f57, 0x6f54, 0x6f51, 0x6f4d, 0x6f4a, 0x6f47,
  0x6f44, 0x6f41, 0x6f3e, 0x6f3b, 0x6f38, 0x6f35, 0x6f31, 0x6f2e,
  0x6f2b, 0x6f28, 0x6f25, 0x6f22, 0x6f1f, 0x6f1c, 0x6f19, 0x6f15,
  0x6f12, 0x6f0f, 0x6f0c, 0x6f09, 0x6f06, 0x6f03, 0x6f00, 0x6efc,
  0x6ef9, 0x6ef6, 0x6ef3, 0x6ef0, 0x6eed, 0x6eea, 0x6ee7, 0x6ee3,
  0x6ee0, 0x6edd, 0x6eda, 0x6ed7, 0x6ed4, 0x6ed1, 0x6ecd, 0x6eca,
  0x6ec7, 0x6ec4, 0x6ec1, 0x6ebe, 0x6eba, 0x6eb7, 0x6eb4, 0x6eb1,
  0x6eae, 0x6eab, 0x6ea8, 0x6ea4, 0x6ea1, 0x6e9e, 0x6e9b, 0x6e98,
  0x6e95, 0x6e91, 0x6e8e, 0x6e8b, 0x6e88, 0x6e85, 0x6e82, 0x6e7e,
  0x6e7b, 0x6e78, 0x6e75, 0x6e72, 0x6e6f, 0x6e6b, 0x6e68, 0x6e65,
  0x6e62, 0x6e5f, 0x6e5b, 0x6e58, 0x6e55, 0x6e52, 0x6e4f, 0x6e4c,
  0x6e48, 0x6e45, 0x6e42, 0x6e3f, 0x6e3c, 0x6e38, 0x6e35, 0x6e32,
  0x6e2f, 0x6e2c, 0x6e28, 0x6e25, 0x6e22, 0x6e1f, 0x6e1c, 0x6e18,
  0x6e15, 0x6e12, 0x6e0f, 0x6e0c, 0x6e08, 0x6e05, 0x6e02, 0x6dff,
  0x6dfb, 0x6df8, 0x6df5, 0x6df2, 0x6def, 0x6deb, 0x6de8, 0x6de5,
  0x6de2, 0x6ddf, 0x6ddb, 0x6dd8, 0x6dd5, 0x6dd2, 0x6dce, 0x6dcb,
  0x6dc8, 0x6dc5, 0x6dc1, 0x6dbe, 0x6dbb, 0x6db8, 0x6db5, 0x6db1,
  0x6dae, 0x6dab, 0x6da8, 0x6da4, 0x6da1, 0x6d9e, 0x6d9b, 0x6d97,
  0x6d94, 0x6d91, 0x6d8e, 0x6d8a, 0x6d87, 0x6d84, 0x6d81, 0x6d7d,
  0x6d7a, 0x6d77, 0x6d74, 0x6d70, 0x6d6d, 0x6d6a, 0x6d67, 0x6d63,
  0x6d60, 0x6d5d, 0x6d59, 0x6d56, 0x6d53, 0x6d50, 0x6d4c, 0x6d49,
  0x6d46, 0x6d43, 0x6d3f, 0x6d3c, 0x6d39, 0x6d36, 0x6d32, 0x6d2f,
  0x6d2c, 0x6d28, 0x6d25, 0x6d22, 0x6d1f, 0x6d1b, 0x6d18, 0x6d15,
  0x6d11, 0x6d0e, 0x6d0b, 0x6d08, 0x6d04, 0x6d01, 0x6cfe, 0x6cfa,
  0x6cf7, 0x6cf4, 0x6cf0, 0x6ced, 0x6cea, 0x6ce7, 0x6ce3, 0x6ce0,
  0x6cdd, 0x6cd9, 0x6cd6, 0x6cd3, 0x6ccf, 0x6ccc, 0x6cc9, 0x6cc5,
  0x6cc2, 0x6cbf, 0x6cbc, 0x6cb8, 0x6cb5, 0x6cb2, 0x6cae, 0x6cab,
  0x6ca8, 0x6ca4, 0x6ca1, 0x6c9e, 0x6c9a, 0x6c97, 0x6c94, 0x6c90,
  0x6c8d, 0x6c8a, 0x6c86, 0x6c83, 0x6c80, 0x6c7c, 0x6c79, 0x6c76,
  0x6c72, 0x6c6f, 0x6c6c, 0x6c68, 0x6c65, 0x6c62, 0x6c5e, 0x6c5b,
  0x6c58, 0x6c54, 0x6c51, 0x6c4e, 0x6c4a, 0x6c47, 0x6c44, 0x6c40,
  0x6c3d, 0x6c39, 0x6c36, 0x6c33, 0x6c2f, 0x6c2c, 0x6c29, 0x6c25,
  0x6c22, 0x6c1f, 0x6c1b, 0x6c18, 0x6c15, 0x6c11, 0x6c0e, 0x6c0a,
  0x6c07, 0x6c04, 0x6c00, 0x6bfd, 0x6bfa, 0x6bf6, 0x6bf3, 0x6bef,
  0x6bec, 0x6be9, 0x6be5, 0x6be2, 0x6bdf, 0x6bdb, 0x6bd8, 0x6bd4,
  0x6bd1, 0x6bce, 0x6bca, 0x6bc7, 0x6bc3, 0x6bc0, 0x6bbd, 0x6bb9,
  0x6bb6, 0x6bb2, 0x6baf, 0x6bac, 0x6ba8, 0x6ba5, 0x6ba1, 0x6b9e,
  0x6b9b, 0x6b97, 0x6b94, 0x6b90, 0x6b8d, 0x6b8a, 0x6b86, 0x6b83,
  0x6b7f, 0x6b7c, 0x6b79, 0x6b75, 0x6b72, 0x6b6e, 0x6b6b, 0x6b68,
  0x6b64, 0x6b61, 0x6b5d, 0x6b5a, 0x6b56, 0x6b53, 0x6b50, 0x6b4c,
  0x6b49, 0x6b45, 0x6b42, 0x6b3e, 0x6b3b, 0x6b38, 0x6b34, 0x6b31,
  0x6b2d, 0x6b2a, 0x6b26, 0x6b23, 0x6b20, 0x6b1c, 0x6b19, 0x6b15,
  0x6b12, 0x6b0e, 0x6b0b, 0x6b07, 0x6b04, 0x6b01, 0x6afd, 0x6afa,
  0x6af6, 0x6af3, 0x6aef, 0x6aec, 0x6ae8, 0x6ae5, 0x6ae1, 0x6ade,
  0x6adb, 0x6ad7, 0x6ad4, 0x6ad0, 0x6acd, 0x6ac9, 0x6ac6, 0x6ac2,
  0x6abf, 0x6abb, 0x6ab8, 0x6ab4, 0x6ab1, 0x6aae, 0x6aaa, 0x6aa7,
  0x6aa3, 0x6aa0, 0x6a9c, 0x6a99, 0x6a95, 0x6a92, 0x6a8e, 0x6a8b,
  0x6a87, 0x6a84, 0x6a80, 0x6a7d, 0x6a79, 0x6a76, 0x6a72, 0x6a6f,
  0x6a6b, 0x6a68, 0x6a64, 0x6a61, 0x6a5d, 0x6a5a, 0x6a56, 0x6a53,
  0x6a4f, 0x6a4c, 0x6a48, 0x6a45, 0x6a41, 0x6a3e, 0x6a3a, 0x6a37,
  0x6a33, 0x6a30, 0x6a2c, 0x6a29, 0x6a25, 0x6a22, 0x6a1e, 0x6a1b,
  0x6a17, 0x6a14, 0x6a10, 0x6a0d, 0x6a09, 0x6a06, 0x6a02, 0x69ff,
  0x69fb, 0x69f8, 0x69f4, 0x69f1, 0x69ed, 0x69e9, 0x69e6, 0x69e2,
  0x69df, 0x69db, 0x69d8, 0x69d4, 0x69d1, 0x69cd, 0x69ca, 0x69c6,
  0x69c3, 0x69bf, 0x69bc, 0x69b8, 0x69b4, 0x69b1, 0x69ad, 0x69aa,
  0x69a6, 0x69a3, 0x699f, 0x699c, 0x6998, 0x6995, 0x6991, 0x698d,
  0x698a, 0x6986, 0x6983, 0x697f, 0x697c, 0x6978, 0x6975, 0x6971,
  0x696d, 0x696a, 0x6966, 0x6963, 0x695f, 0x695c, 0x6958, 0x6954,
  0x6951, 0x694d, 0x694a, 0x6946, 0x6943, 0x693f, 0x693b, 0x6938,
  0x6934, 0x6931, 0x692d, 0x692a, 0x6926, 0x6922, 0x691f, 0x691b,
  0x6918, 0x6914, 0x6910, 0x690d, 0x6909, 0x6906, 0x6902, 0x68fe,
  0x68fb, 0x68f7, 0x68f4, 0x68f0, 0x68ec, 0x68e9, 0x68e5, 0x68e2,
  0x68de, 0x68da, 0x68d7, 0x68d3, 0x68d0, 0x68cc, 0x68c8, 0x68c5,
  0x68c1, 0x68be, 0x68ba, 0x68b6, 0x68b3, 0x68af, 0x68ac, 0x68a8,
  0x68a4, 0x68a1, 0x689d, 0x6899, 0x6896, 0x6892, 0x688f, 0x688b,
  0x6887, 0x6884, 0x6880, 0x687c, 0x6879, 0x6875, 0x6872, 0x686e,
  0x686a, 0x6867, 0x6863, 0x685f, 0x685c, 0x6858, 0x6854, 0x6851,
  0x684d, 0x684a, 0x6846, 0x6842, 0x683f, 0x683b, 0x6837, 0x6834,
  0x6830, 0x682c, 0x6829, 0x6825, 0x6821, 0x681e, 0x681a, 0x6816,
  0x6813, 0x680f, 0x680b, 0x6808, 0x6804, 0x6800, 0x67fd, 0x67f9,
  0x67f5, 0x67f2, 0x67ee, 0x67ea, 0x67e7, 0x67e3, 0x67df, 0x67dc,
  0x67d8, 0x67d4, 0x67d1, 0x67cd, 0x67c9, 0x67c6, 0x67c2, 0x67be,
  0x67bb, 0x67b7, 0x67b3, 0x67b0, 0x67ac, 0x67a8, 0x67a5, 0x67a1,
  0x679d, 0x679a, 0x6796, 0x6792, 0x678e, 0x678b, 0x6787, 0x6783,
  0x6780, 0x677c, 0x6778, 0x6775, 0x6771, 0x676d, 0x6769, 0x6766,
  0x6762, 0x675e, 0x675b, 0x6757, 0x6753, 0x6750, 0x674c, 0x6748,
  0x6744, 0x6741, 0x673d, 0x6739, 0x6736, 0x6732, 0x672e, 0x672a,
  0x6727, 0x6723, 0x671f, 0x671c, 0x6718, 0x6714, 0x6710, 0x670d,
  0x6709, 0x6705, 0x6701, 0x66fe, 0x66fa, 0x66f6, 0x66f3, 0x66ef,
  0x66eb, 0x66e7, 0x66e4, 0x66e0, 0x66dc, 0x66d8, 0x66d5, 0x66d1,
  0x66cd, 0x66c9, 0x66c6, 0x66c2, 0x66be, 0x66ba, 0x66b7, 0x66b3,
  0x66af, 0x66ab, 0x66a8, 0x66a4, 0x66a0, 0x669c, 0x6699, 0x6695,
  0x6691, 0x668d, 0x668a, 0x6686, 0x6682, 0x667e, 0x667b, 0x6677,
  0x6673, 0x666f, 0x666b, 0x6668, 0x6664, 0x6660, 0x665c, 0x6659,
  0x6655, 0x6651, 0x664d, 0x664a, 0x6646, 0x6642, 0x663e, 0x663a,
  0x6637, 0x6633, 0x662f, 0x662b, 0x6627, 0x6624, 0x6620, 0x661c,
  0x6618, 0x6615, 0x6611, 0x660d, 0x6609, 0x6605, 0x6602, 0x65fe,
  0x65fa, 0x65f6, 0x65f2, 0x65ef, 0x65eb, 0x65e7, 0x65e3, 0x65df,
  0x65dc, 0x65d8, 0x65d4, 0x65d0, 0x65cc, 0x65c9, 0x65c5, 0x65c1,
  0x65bd, 0x65b9, 0x65b5, 0x65b2, 0x65ae, 0x65aa, 0x65a6, 0x65a2,
  0x659f, 0x659b, 0x6597, 0x6593, 0x658f, 0x658b, 0x6588, 0x6584,
  0x6580, 0x657c, 0x6578, 0x6574, 0x6571, 0x656d, 0x6569, 0x6565,
  0x6561, 0x655d, 0x655a, 0x6556, 0x6552, 0x654e, 0x654a, 0x6546,
  0x6543, 0x653f, 0x653b, 0x6537, 0x6533, 0x652f, 0x652c, 0x6528,
  0x6524, 0x6520, 0x651c, 0x6518, 0x6514, 0x6511, 0x650d, 0x6509,
  0x6505, 0x6501, 0x64fd, 0x64f9, 0x64f6, 0x64f2, 0x64ee, 0x64ea,
  0x64e6, 0x64e2, 0x64de, 0x64db, 0x64d7, 0x64d3, 0x64cf, 0x64cb,
  0x64c7, 0x64c3, 0x64bf, 0x64bc, 0x64b8, 0x64b4, 0x64b0, 0x64ac,
  0x64a8, 0x64a4, 0x64a0, 0x649c, 0x6499, 0x6495, 0x6491, 0x648d,
  0x6489, 0x6485, 0x6481, 0x647d, 0x6479, 0x6476, 0x6472, 0x646e,
  0x646a, 0x6466, 0x6462, 0x645e, 0x645a, 0x6456, 0x6453, 0x644f,
  0x644b, 0x6447, 0x6443, 0x643f, 0x643b, 0x6437, 0x6433, 0x642f,
  0x642b, 0x6428, 0x6424, 0x6420, 0x641c, 0x6418, 0x6414, 0x6410,
  0x640c, 0x6408, 0x6404, 0x6400, 0x63fc, 0x63f9, 0x63f5, 0x63f1,
  0x63ed, 0x63e9, 0x63e5, 0x63e1, 0x63dd, 0x63d9, 0x63d5, 0x63d1,
  0x63cd, 0x63c9, 0x63c5, 0x63c1, 0x63be, 0x63ba, 0x63b6, 0x63b2,
  0x63ae, 0x63aa, 0x63a6, 0x63a2, 0x639e, 0x639a, 0x6396, 0x6392,
  0x638e, 0x638a, 0x6386, 0x6382, 0x637e, 0x637a, 0x6377, 0x6373,
  0x636f, 0x636b, 0x6367, 0x6363, 0x635f, 0x635b, 0x6357, 0x6353,
  0x634f, 0x634b, 0x6347, 0x6343, 0x633f, 0x633b, 0x6337, 0x6333,
  0x632f, 0x632b, 0x6327, 0x6323, 0x631f, 0x631b, 0x6317, 0x6313,
  0x630f, 0x630b, 0x6307, 0x6303, 0x62ff, 0x62fb, 0x62f7, 0x62f3,
  0x62f0, 0x62ec, 0x62e8, 0x62e4, 0x62e0, 0x62dc, 0x62d8, 0x62d4,
  0x62d0, 0x62cc, 0x62c8, 0x62c4, 0x62c0, 0x62bc, 0x62b8, 0x62b4,
  0x62b0, 0x62ac, 0x62a8, 0x62a4, 0x62a0, 0x629c, 0x6298, 0x6294,
  0x6290, 0x628c, 0x6288, 0x6284, 0x6280, 0x627c, 0x6278, 0x6273,
  0x626f, 0x626b, 0x6267, 0x6263, 0x625f, 0x625b, 0x6257, 0x6253,
  0x624f, 0x624b, 0x6247, 0x6243, 0x623f, 0x623b, 0x6237, 0x6233,
  0x622f, 0x622b, 0x6227, 0x6223, 0x621f, 0x621b, 0x6217, 0x6213,
  0x620f, 0x620b, 0x6207, 0x6203, 0x61ff, 0x61fb, 0x61f7, 0x61f3,
  0x61ee, 0x61ea, 0x61e6, 0x61e2, 0x61de, 0x61da, 0x61d6, 0x61d2,
  0x61ce, 0x61ca, 0x61c6, 0x61c2, 0x61be, 0x61ba, 0x61b6, 0x61b2,
  0x61ae, 0x61aa, 0x61a6, 0x61a1, 0x619d, 0x6199, 0x6195, 0x6191,
  0x618d, 0x6189, 0x6185, 0x6181, 0x617d, 0x6179, 0x6175, 0x6171,
  0x616d, 0x6168, 0x6164, 0x6160, 0x615c, 0x6158, 0x6154, 0x6150,
  0x614c, 0x6148, 0x6144, 0x6140, 0x613c, 0x6137, 0x6133, 0x612f,
  0x612b, 0x6127, 0x6123, 0x611f, 0x611b, 0x6117, 0x6113, 0x610f,
  0x610a, 0x6106, 0x6102, 0x60fe, 0x60fa, 0x60f6, 0x60f2, 0x60ee,
  0x60ea, 0x60e6, 0x60e1, 0x60dd, 0x60d9, 0x60d5, 0x60d1, 0x60cd,
  0x60c9, 0x60c5, 0x60c1, 0x60bc, 0x60b8, 0x60b4, 0x60b0, 0x60ac,
  0x60a8, 0x60a4, 0x60a0, 0x609c, 0x6097, 0x6093, 0x608f, 0x608b,
  0x6087, 0x6083, 0x607f, 0x607b, 0x6076, 0x6072, 0x606e, 0x606a,
  0x6066, 0x6062, 0x605e, 0x6059, 0x6055, 0x6051, 0x604d, 0x6049,
  0x6045, 0x6041, 0x603c, 0x6038, 0x6034, 0x6030, 0x602c, 0x6028,
  0x6024, 0x601f, 0x601b, 0x6017, 0x6013, 0x600f, 0x600b, 0x6007,
  0x6002, 0x5ffe, 0x5ffa, 0x5ff6, 0x5ff2, 0x5fee, 0x5fe9, 0x5fe5,
  0x5fe1, 0x5fdd, 0x5fd9, 0x5fd5, 0x5fd0, 0x5fcc, 0x5fc8, 0x5fc4,
  0x5fc0, 0x5fbc, 0x5fb7, 0x5fb3, 0x5faf, 0x5fab, 0x5fa7, 0x5fa3,
  0x5f9e, 0x5f9a, 0x5f96, 0x5f92, 0x5f8e, 0x5f8a, 0x5f85, 0x5f81,
  0x5f7d, 0x5f79, 0x5f75, 0x5f70, 0x5f6c, 0x5f68, 0x5f64, 0x5f60,
  0x5f5b, 0x5f57, 0x5f53, 0x5f4f, 0x5f4b, 0x5f46, 0x5f42, 0x5f3e,
  0x5f3a, 0x5f36, 0x5f31, 0x5f2d, 0x5f29, 0x5f25, 0x5f21, 0x5f1c,
  0x5f18, 0x5f14, 0x5f10, 0x5f0c, 0x5f07, 0x5f03, 0x5eff, 0x5efb,
  0x5ef7, 0x5ef2, 0x5eee, 0x5eea, 0x5ee6, 0x5ee2, 0x5edd, 0x5ed9,
  0x5ed5, 0x5ed1, 0x5ecc, 0x5ec8, 0x5ec4, 0x5ec0, 0x5ebc, 0x5eb7,
  0x5eb3, 0x5eaf, 0x5eab, 0x5ea6, 0x5ea2, 0x5e9e, 0x5e9a, 0x5e95,
  0x5e91, 0x5e8d, 0x5e89, 0x5e85, 0x5e80, 0x5e7c, 0x5e78, 0x5e74,
  0x5e6f, 0x5e6b, 0x5e67, 0x5e63, 0x5e5e, 0x5e5a, 0x5e56, 0x5e52,
  0x5e4d, 0x5e49, 0x5e45, 0x5e41, 0x5e3c, 0x5e38, 0x5e34, 0x5e30,
  0x5e2b, 0x5e27, 0x5e23, 0x5e1f, 0x5e1a, 0x5e16, 0x5e12, 0x5e0e,
  0x5e09, 0x5e05, 0x5e01, 0x5dfd, 0x5df8, 0x5df4, 0x5df0, 0x5deb,
  0x5de7, 0x5de3, 0x5ddf, 0x5dda, 0x5dd6, 0x5dd2, 0x5dce, 0x5dc9,
  0x5dc5, 0x5dc1, 0x5dbc, 0x5db8, 0x5db4, 0x5db0, 0x5dab, 0x5da7,
  0x5da3, 0x5d9e, 0x5d9a, 0x5d96, 0x5d92, 0x5d8d, 0x5d89, 0x5d85,
  0x5d80, 0x5d7c, 0x5d78, 0x5d74, 0x5d6f, 0x5d6b, 0x5d67, 0x5d62,
  0x5d5e, 0x5d5a, 0x5d55, 0x5d51, 0x5d4d, 0x5d49, 0x5d44, 0x5d40,
  0x5d3c, 0x5d37, 0x5d33, 0x5d2f, 0x5d2a, 0x5d26, 0x5d22, 0x5d1e,
  0x5d19, 0x5d15, 0x5d11, 0x5d0c, 0x5d08, 0x5d04, 0x5cff, 0x5cfb,
  0x5cf7, 0x5cf2, 0x5cee, 0x5cea, 0x5ce5, 0x5ce1, 0x5cdd, 0x5cd8,
  0x5cd4, 0x5cd0, 0x5ccb, 0x5cc7, 0x5cc3, 0x5cbe, 0x5cba, 0x5cb6,
  0x5cb1, 0x5cad, 0x5ca9, 0x5ca4, 0x5ca0, 0x5c9c, 0x5c97, 0x5c93,
  0x5c8f, 0x5c8a, 0x5c86, 0x5c82, 0x5c7d, 0x5c79, 0x5c75, 0x5c70,
  0x5c6c, 0x5c68, 0x5c63, 0x5c5f, 0x5c5b, 0x5c56, 0x5c52, 0x5c4e,
  0x5c49, 0x5c45, 0x5c41, 0x5c3c, 0x5c38, 0x5c33, 0x5c2f, 0x5c2b,
  0x5c26, 0x5c22, 0x5c1e, 0x5c19, 0x5c15, 0x5c11, 0x5c0c, 0x5c08,
  0x5c03, 0x5bff, 0x5bfb, 0x5bf6, 0x5bf2, 0x5bee, 0x5be9, 0x5be5,
  0x5be0, 0x5bdc, 0x5bd8, 0x5bd3, 0x5bcf, 0x5bcb, 0x5bc6, 0x5bc2,
  0x5bbd, 0x5bb9, 0x5bb5, 0x5bb0, 0x5bac, 0x5ba8, 0x5ba3, 0x5b9f,
  0x5b9a, 0x5b96, 0x5b92, 0x5b8d, 0x5b89, 0x5b84, 0x5b80, 0x5b7c,
  0x5b77, 0x5b73, 0x5b6e, 0x5b6a, 0x5b66, 0x5b61, 0x5b5d, 0x5b58,
  0x5b54, 0x5b50, 0x5b4b, 0x5b47, 0x5b42, 0x5b3e, 0x5b3a, 0x5b35,
  0x5b31, 0x5b2c, 0x5b28, 0x5b24, 0x5b1f, 0x5b1b, 0x5b16, 0x5b12,
  0x5b0e, 0x5b09, 0x5b05, 0x5b00, 0x5afc, 0x5af7, 0x5af3, 0x5aef,
  0x5aea, 0x5ae6, 0x5ae1, 0x5add, 0x5ad8, 0x5ad4, 0x5ad0, 0x5acb,
  0x5ac7, 0x5ac2, 0x5abe, 0x5ab9, 0x5ab5, 0x5ab1, 0x5aac, 0x5aa8,
  0x5aa3, 0x5a9f, 0x5a9a, 0x5a96, 0x5a92, 0x5a8d, 0x5a89, 0x5a84,
  0x5a80, 0x5a7b, 0x5a77, 0x5a72, 0x5a6e, 0x5a6a, 0x5a65, 0x5a61,
  0x5a5c, 0x5a58, 0x5a53, 0x5a4f, 0x5a4a, 0x5a46, 0x5a41, 0x5a3d,
  0x5a39, 0x5a34, 0x5a30, 0x5a2b, 0x5a27, 0x5a22, 0x5a1e, 0x5a19,
  0x5a15, 0x5a10, 0x5a0c, 0x5a07, 0x5a03, 0x59ff, 0x59fa, 0x59f6,
  0x59f1, 0x59ed, 0x59e8, 0x59e4, 0x59df, 0x59db, 0x59d6, 0x59d2,
  0x59cd, 0x59c9, 0x59c4, 0x59c0, 0x59bb, 0x59b7, 0x59b2, 0x59ae,
  0x59a9, 0x59a5, 0x59a1, 0x599c, 0x5998, 0x5993, 0x598f, 0x598a,
  0x5986, 0x5981, 0x597d, 0x5978, 0x5974, 0x596f, 0x596b, 0x5966,
  0x5962, 0x595d, 0x5959, 0x5954, 0x5950, 0x594b, 0x5947, 0x5942,
  0x593e, 0x5939, 0x5935, 0x5930, 0x592c, 0x5927, 0x5923, 0x591e,
  0x591a, 0x5915, 0x5911, 0x590c, 0x5908, 0x5903, 0x58fe, 0x58fa,
  0x58f5, 0x58f1, 0x58ec, 0x58e8, 0x58e3, 0x58df, 0x58da, 0x58d6,
  0x58d1, 0x58cd, 0x58c8, 0x58c4, 0x58bf, 0x58bb, 0x58b6, 0x58b2,
  0x58ad, 0x58a9, 0x58a4, 0x589f, 0x589b, 0x5896, 0x5892, 0x588d,
  0x5889, 0x5884, 0x5880, 0x587b, 0x5877, 0x5872, 0x586e, 0x5869,
  0x5864, 0x5860, 0x585b, 0x5857, 0x5852, 0x584e, 0x5849, 0x5845,
  0x5840, 0x583c, 0x5837, 0x5832, 0x582e, 0x5829, 0x5825, 0x5820,
  0x581c, 0x5817, 0x5813, 0x580e, 0x5809, 0x5805, 0x5800, 0x57fc,
  0x57f7, 0x57f3, 0x57ee, 0x57e9, 0x57e5, 0x57e0, 0x57dc, 0x57d7,
  0x57d3, 0x57ce, 0x57c9, 0x57c5, 0x57c0, 0x57bc, 0x57b7, 0x57b3,
  0x57ae, 0x57a9, 0x57a5, 0x57a0, 0x579c, 0x5797, 0x5793, 0x578e,
  0x5789, 0x5785, 0x5780, 0x577c, 0x5777, 0x5772, 0x576e, 0x5769,
  0x5765, 0x5760, 0x575c, 0x5757, 0x5752, 0x574e, 0x5749, 0x5745,
  0x5740, 0x573b, 0x5737, 0x5732, 0x572e, 0x5729, 0x5724, 0x5720,
  0x571b, 0x5717, 0x5712, 0x570d, 0x5709, 0x5704, 0x56ff, 0x56fb,
  0x56f6, 0x56f2, 0x56ed, 0x56e8, 0x56e4, 0x56df, 0x56db, 0x56d6,
  0x56d1, 0x56cd, 0x56c8, 0x56c4, 0x56bf, 0x56ba, 0x56b6, 0x56b1,
  0x56ac, 0x56a8, 0x56a3, 0x569f, 0x569a, 0x5695, 0x5691, 0x568c,
  0x5687, 0x5683, 0x567e, 0x5679, 0x5675, 0x5670, 0x566c, 0x5667,
  0x5662, 0x565e, 0x5659, 0x5654, 0x5650, 0x564b, 0x5646, 0x5642,
  0x563d, 0x5639, 0x5634, 0x562f, 0x562b, 0x5626, 0x5621, 0x561d,
  0x5618, 0x5613, 0x560f, 0x560a, 0x5605, 0x5601, 0x55fc, 0x55f7,
  0x55f3, 0x55ee, 0x55ea, 0x55e5, 0x55e0, 0x55dc, 0x55d7, 0x55d2,
  0x55ce, 0x55c9, 0x55c4, 0x55c0, 0x55bb, 0x55b6, 0x55b2, 0x55ad,
  0x55a8, 0x55a4, 0x559f, 0x559a, 0x5596, 0x5591, 0x558c, 0x5588,
  0x5583, 0x557e, 0x5579, 0x5575, 0x5570, 0x556b, 0x5567, 0x5562,
  0x555d, 0x5559, 0x5554, 0x554f, 0x554b, 0x5546, 0x5541, 0x553d,
  0x5538, 0x5533, 0x552f, 0x552a, 0x5525, 0x5520, 0x551c, 0x5517,
  0x5512, 0x550e, 0x5509, 0x5504, 0x5500, 0x54fb, 0x54f6, 0x54f2,
  0x54ed, 0x54e8, 0x54e3, 0x54df, 0x54da, 0x54d5, 0x54d1, 0x54cc,
  0x54c7, 0x54c2, 0x54be, 0x54b9, 0x54b4, 0x54b0, 0x54ab, 0x54a6,
  0x54a2, 0x549d, 0x5498, 0x5493, 0x548f, 0x548a, 0x5485, 0x5480,
  0x547c, 0x5477, 0x5472, 0x546e, 0x5469, 0x5464, 0x545f, 0x545b,
  0x5456, 0x5451, 0x544d, 0x5448, 0x5443, 0x543e, 0x543a, 0x5435,
  0x5430, 0x542b, 0x5427, 0x5422, 0x541d, 0x5418, 0x5414, 0x540f,
  0x540a, 0x5406, 0x5401, 0x53fc, 0x53f7, 0x53f3, 0x53ee, 0x53e9,
  0x53e4, 0x53e0, 0x53db, 0x53d6, 0x53d1, 0x53cd, 0x53c8, 0x53c3,
  0x53be, 0x53ba, 0x53b5, 0x53b0, 0x53ab, 0x53a7, 0x53a2, 0x539d,
  0x5398, 0x5394, 0x538f, 0x538a, 0x5385, 0x5380, 0x537c, 0x5377,
  0x5372, 0x536d, 0x5369, 0x5364, 0x535f, 0x535a, 0x5356, 0x5351,
  0x534c, 0x5347, 0x5343, 0x533e, 0x5339, 0x5334, 0x532f, 0x532b,
  0x5326, 0x5321, 0x531c, 0x5318, 0x5313, 0x530e, 0x5309, 0x5304,
  0x5300, 0x52fb, 0x52f6, 0x52f1, 0x52ec, 0x52e8, 0x52e3, 0x52de,
  0x52d9, 0x52d5, 0x52d0, 0x52cb, 0x52c6, 0x52c1, 0x52bd, 0x52b8,
  0x52b3, 0x52ae, 0x52a9, 0x52a5, 0x52a0, 0x529b, 0x5296, 0x5291,
  0x528d, 0x5288, 0x5283, 0x527e, 0x5279, 0x5275, 0x5270, 0x526b,
  0x5266, 0x5261, 0x525d, 0x5258, 0x5253, 0x524e, 0x5249, 0x5244,
  0x5240, 0x523b, 0x5236, 0x5231, 0x522c, 0x5228, 0x5223, 0x521e,
  0x5219, 0x5214, 0x520f, 0x520b, 0x5206, 0x5201, 0x51fc, 0x51f7,
  0x51f3, 0x51ee, 0x51e9, 0x51e4, 0x51df, 0x51da, 0x51d6, 0x51d1,
  0x51cc, 0x51c7, 0x51c2, 0x51bd, 0x51b9, 0x51b4, 0x51af, 0x51aa,
  0x51a5, 0x51a0, 0x519c, 0x5197, 0x5192, 0x518d, 0x5188, 0x5183,
  0x517e, 0x517a, 0x5175, 0x5170, 0x516b, 0x5166, 0x5161, 0x515d,
  0x5158, 0x5153, 0x514e, 0x5149, 0x5144, 0x513f, 0x513b, 0x5136,
  0x5131, 0x512c, 0x5127, 0x5122, 0x511d, 0x5119, 0x5114, 0x510f,
  0x510a, 0x5105, 0x5100, 0x50fb, 0x50f7, 0x50f2, 0x50ed, 0x50e8,
  0x50e3, 0x50de, 0x50d9, 0x50d4, 0x50d0, 0x50cb, 0x50c6, 0x50c1,
  0x50bc, 0x50b7, 0x50b2, 0x50ad, 0x50a9, 0x50a4, 0x509f, 0x509a,
  0x5095, 0x5090, 0x508b, 0x5086, 0x5082, 0x507d, 0x5078, 0x5073,
  0x506e, 0x5069, 0x5064, 0x505f, 0x505a, 0x5056, 0x5051, 0x504c,
  0x5047, 0x5042, 0x503d, 0x5038, 0x5033, 0x502e, 0x5029, 0x5025,
  0x5020, 0x501b, 0x5016, 0x5011, 0x500c, 0x5007, 0x5002, 0x4ffd,
  0x4ff8, 0x4ff4, 0x4fef, 0x4fea, 0x4fe5, 0x4fe0, 0x4fdb, 0x4fd6,
  0x4fd1, 0x4fcc, 0x4fc7, 0x4fc2, 0x4fbe, 0x4fb9, 0x4fb4, 0x4faf,
  0x4faa, 0x4fa5, 0x4fa0, 0x4f9b, 0x4f96, 0x4f91, 0x4f8c, 0x4f87,
  0x4f82, 0x4f7e, 0x4f79, 0x4f74, 0x4f6f, 0x4f6a, 0x4f65, 0x4f60,
  0x4f5b, 0x4f56, 0x4f51, 0x4f4c, 0x4f47, 0x4f42, 0x4f3d, 0x4f39,
  0x4f34, 0x4f2f, 0x4f2a, 0x4f25, 0x4f20, 0x4f1b, 0x4f16, 0x4f11,
  0x4f0c, 0x4f07, 0x4f02, 0x4efd, 0x4ef8, 0x4ef3, 0x4eee, 0x4ee9,
  0x4ee5, 0x4ee0, 0x4edb, 0x4ed6, 0x4ed1, 0x4ecc, 0x4ec7, 0x4ec2,
  0x4ebd, 0x4eb8, 0x4eb3, 0x4eae, 0x4ea9, 0x4ea4, 0x4e9f, 0x4e9a,
  0x4e95, 0x4e90, 0x4e8b, 0x4e86, 0x4e81, 0x4e7c, 0x4e78, 0x4e73,
  0x4e6e, 0x4e69, 0x4e64, 0x4e5f, 0x4e5a, 0x4e55, 0x4e50, 0x4e4b,
  0x4e46, 0x4e41, 0x4e3c, 0x4e37, 0x4e32, 0x4e2d, 0x4e28, 0x4e23,
  0x4e1e, 0x4e19, 0x4e14, 0x4e0f, 0x4e0a, 0x4e05, 0x4e00, 0x4dfb,
  0x4df6, 0x4df1, 0x4dec, 0x4de7, 0x4de2, 0x4ddd, 0x4dd8, 0x4dd3,
  0x4dce, 0x4dc9, 0x4dc4, 0x4dbf, 0x4dba, 0x4db5, 0x4db0, 0x4dab,
  0x4da6, 0x4da1, 0x4d9c, 0x4d97, 0x4d92, 0x4d8d, 0x4d88, 0x4d83,
  0x4d7e, 0x4d79, 0x4d74, 0x4d6f, 0x4d6a, 0x4d65, 0x4d60, 0x4d5b,
  0x4d56, 0x4d51, 0x4d4c, 0x4d47, 0x4d42, 0x4d3d, 0x4d38, 0x4d33,
  0x4d2e, 0x4d29, 0x4d24, 0x4d1f, 0x4d1a, 0x4d15, 0x4d10, 0x4d0b,
  0x4d06, 0x4d01, 0x4cfc, 0x4cf7, 0x4cf2, 0x4ced, 0x4ce8, 0x4ce3,
  0x4cde, 0x4cd9, 0x4cd4, 0x4ccf, 0x4cca, 0x4cc5, 0x4cc0, 0x4cbb,
  0x4cb6, 0x4cb1, 0x4cac, 0x4ca7, 0x4ca2, 0x4c9d, 0x4c98, 0x4c93,
  0x4c8e, 0x4c88, 0x4c83, 0x4c7e, 0x4c79, 0x4c74, 0x4c6f, 0x4c6a,
  0x4c65, 0x4c60, 0x4c5b, 0x4c56, 0x4c51, 0x4c4c, 0x4c47, 0x4c42,
  0x4c3d, 0x4c38, 0x4c33, 0x4c2e, 0x4c29, 0x4c24, 0x4c1f, 0x4c1a,
  0x4c14, 0x4c0f, 0x4c0a, 0x4c05, 0x4c00, 0x4bfb, 0x4bf6, 0x4bf1,
  0x4bec, 0x4be7, 0x4be2, 0x4bdd, 0x4bd8, 0x4bd3, 0x4bce, 0x4bc9,
  0x4bc4, 0x4bbe, 0x4bb9, 0x4bb4, 0x4baf, 0x4baa, 0x4ba5, 0x4ba0,
  0x4b9b, 0x4b96, 0x4b91, 0x4b8c, 0x4b87, 0x4b82, 0x4b7d, 0x4b77,
  0x4b72, 0x4b6d, 0x4b68, 0x4b63, 0x4b5e, 0x4b59, 0x4b54, 0x4b4f,
  0x4b4a, 0x4b45, 0x4b40, 0x4b3b, 0x4b35, 0x4b30, 0x4b2b, 0x4b26,
  0x4b21, 0x4b1c, 0x4b17, 0x4b12, 0x4b0d, 0x4b08, 0x4b03, 0x4afd,
  0x4af8, 0x4af3, 0x4aee, 0x4ae9, 0x4ae4, 0x4adf, 0x4ada, 0x4ad5,
  0x4ad0, 0x4acb, 0x4ac5, 0x4ac0, 0x4abb, 0x4ab6, 0x4ab1, 0x4aac,
  0x4aa7, 0x4aa2, 0x4a9d, 0x4a97, 0x4a92, 0x4a8d, 0x4a88, 0x4a83,
  0x4a7e, 0x4a79, 0x4a74, 0x4a6f, 0x4a6a, 0x4a64, 0x4a5f, 0x4a5a,
  0x4a55, 0x4a50, 0x4a4b, 0x4a46, 0x4a41, 0x4a3b, 0x4a36, 0x4a31,
  0x4a2c, 0x4a27, 0x4a22, 0x4a1d, 0x4a18, 0x4a12, 0x4a0d, 0x4a08,
  0x4a03, 0x49fe, 0x49f9, 0x49f4, 0x49ef, 0x49e9, 0x49e4, 0x49df,
  0x49da, 0x49d5, 0x49d0, 0x49cb, 0x49c6, 0x49c0, 0x49bb, 0x49b6,
  0x49b1, 0x49ac, 0x49a7, 0x49a2, 0x499c, 0x4997, 0x4992, 0x498d,
  0x4988, 0x4983, 0x497e, 0x4978, 0x4973, 0x496e, 0x4969, 0x4964,
  0x495f, 0x495a, 0x4954, 0x494f, 0x494a, 0x4945, 0x4940, 0x493b,
  0x4936, 0x4930, 0x492b, 0x4926, 0x4921, 0x491c, 0x4917, 0x4911,
  0x490c, 0x4907, 0x4902, 0x48fd, 0x48f8, 0x48f2, 0x48ed, 0x48e8,
  0x48e3, 0x48de, 0x48d9, 0x48d3, 0x48ce, 0x48c9, 0x48c4, 0x48bf,
  0x48ba, 0x48b4, 0x48af, 0x48aa, 0x48a5, 0x48a0, 0x489b, 0x4895,
  0x4890, 0x488b, 0x4886, 0x4881, 0x487c, 0x4876, 0x4871, 0x486c,
  0x4867, 0x4862, 0x485c, 0x4857, 0x4852, 0x484d, 0x4848, 0x4843,
  0x483d, 0x4838, 0x4833, 0x482e, 0x4829, 0x4823, 0x481e, 0x4819,
  0x4814, 0x480f, 0x4809, 0x4804, 0x47ff, 0x47fa, 0x47f5, 0x47ef,
  0x47ea, 0x47e5, 0x47e0, 0x47db, 0x47d5, 0x47d0, 0x47cb, 0x47c6,
  0x47c1, 0x47bb, 0x47b6, 0x47b1, 0x47ac, 0x47a7, 0x47a1, 0x479c,
  0x4797, 0x4792, 0x478d, 0x4787, 0x4782, 0x477d, 0x4778, 0x4773,
  0x476d, 0x4768, 0x4763, 0x475e, 0x4758, 0x4753, 0x474e, 0x4749,
  0x4744, 0x473e, 0x4739, 0x4734, 0x472f, 0x4729, 0x4724, 0x471f,
  0x471a, 0x4715, 0x470f, 0x470a, 0x4705, 0x4700, 0x46fa, 0x46f5,
  0x46f0, 0x46eb, 0x46e6, 0x46e0, 0x46db, 0x46d6, 0x46d1, 0x46cb,
  0x46c6, 0x46c1, 0x46bc, 0x46b6, 0x46b1, 0x46ac, 0x46a7, 0x46a1,
  0x469c, 0x4697, 0x4692, 0x468d, 0x4687, 0x4682, 0x467d, 0x4678,
  0x4672, 0x466d, 0x4668, 0x4663, 0x465d, 0x4658, 0x4653, 0x464e,
  0x4648, 0x4643, 0x463e, 0x4639, 0x4633, 0x462e, 0x4629, 0x4624,
  0x461e, 0x4619, 0x4614, 0x460e, 0x4609, 0x4604, 0x45ff, 0x45f9,
  0x45f4, 0x45ef, 0x45ea, 0x45e4, 0x45df, 0x45da, 0x45d5, 0x45cf,
  0x45ca, 0x45c5, 0x45c0, 0x45ba, 0x45b5, 0x45b0, 0x45aa, 0x45a5,
  0x45a0, 0x459b, 0x4595, 0x4590, 0x458b, 0x4586, 0x4580, 0x457b,
  0x4576, 0x4570, 0x456b, 0x4566, 0x4561, 0x455b, 0x4556, 0x4551,
  0x454b, 0x4546, 0x4541, 0x453c, 0x4536, 0x4531, 0x452c, 0x4526,
  0x4521, 0x451c, 0x4517, 0x4511, 0x450c, 0x4507, 0x4501, 0x44fc,
  0x44f7, 0x44f2, 0x44ec, 0x44e7, 0x44e2, 0x44dc, 0x44d7, 0x44d2,
  0x44cd, 0x44c7, 0x44c2, 0x44bd, 0x44b7, 0x44b2, 0x44ad, 0x44a7,
  0x44a2, 0x449d, 0x4497, 0x4492, 0x448d, 0x4488, 0x4482, 0x447d,
  0x4478, 0x4472, 0x446d, 0x4468, 0x4462, 0x445d, 0x4458, 0x4452,
  0x444d, 0x4448, 0x4443, 0x443d, 0x4438, 0x4433, 0x442d, 0x4428,
  0x4423, 0x441d, 0x4418, 0x4413, 0x440d, 0x4408, 0x4403, 0x43fd,
  0x43f8, 0x43f3, 0x43ed, 0x43e8, 0x43e3, 0x43dd, 0x43d8, 0x43d3,
  0x43cd, 0x43c8, 0x43c3, 0x43bd, 0x43b8, 0x43b3, 0x43ad, 0x43a8,
  0x43a3, 0x439d, 0x4398, 0x4393, 0x438d, 0x4388, 0x4383, 0x437d,
  0x4378, 0x4373, 0x436d, 0x4368, 0x4363, 0x435d, 0x4358, 0x4353,
  0x434d, 0x4348, 0x4343, 0x433d, 0x4338, 0x4333, 0x432d, 0x4328,
  0x4323, 0x431d, 0x4318, 0x4313, 0x430d, 0x4308, 0x4302, 0x42fd,
  0x42f8, 0x42f2, 0x42ed, 0x42e8, 0x42e2, 0x42dd, 0x42d8, 0x42d2,
  0x42cd, 0x42c8, 0x42c2, 0x42bd, 0x42b7, 0x42b2, 0x42ad, 0x42a7,
  0x42a2, 0x429d, 0x4297, 0x4292, 0x428d, 0x4287, 0x4282, 0x427c,
  0x4277, 0x4272, 0x426c, 0x4267, 0x4262, 0x425c, 0x4257, 0x4251,
  0x424c, 0x4247, 0x4241, 0x423c, 0x4237, 0x4231, 0x422c, 0x4226,
  0x4221, 0x421c, 0x4216, 0x4211, 0x420c, 0x4206, 0x4201, 0x41fb,
  0x41f6, 0x41f1, 0x41eb, 0x41e6, 0x41e0, 0x41db, 0x41d6, 0x41d0,
  0x41cb, 0x41c6, 0x41c0, 0x41bb, 0x41b5, 0x41b0, 0x41ab, 0x41a5,
  0x41a0, 0x419a, 0x4195, 0x4190, 0x418a, 0x4185, 0x417f, 0x417a,
  0x4175, 0x416f, 0x416a, 0x4164, 0x415f, 0x415a, 0x4154, 0x414f,
  0x4149, 0x4144, 0x413f, 0x4139, 0x4134, 0x412e, 0x4129, 0x4124,
  0x411e, 0x4119, 0x4113, 0x410e, 0x4108, 0x4103, 0x40fe, 0x40f8,
  0x40f3, 0x40ed, 0x40e8, 0x40e3, 0x40dd, 0x40d8, 0x40d2, 0x40cd,
  0x40c8, 0x40c2, 0x40bd, 0x40b7, 0x40b2, 0x40ac, 0x40a7, 0x40a2,
  0x409c, 0x4097, 0x4091, 0x408c, 0x4086, 0x4081, 0x407c, 0x4076,
  0x4071, 0x406b, 0x4066, 0x4060, 0x405b, 0x4056, 0x4050, 0x404b,
  0x4045, 0x4040, 0x403a, 0x4035, 0x4030, 0x402a, 0x4025, 0x401f,
  0x401a, 0x4014, 0x400f, 0x4009, 0x4004, 0x3fff, 0x3ff9, 0x3ff4,
  0x3fee, 0x3fe9, 0x3fe3, 0x3fde, 0x3fd8, 0x3fd3, 0x3fce, 0x3fc8,
  0x3fc3, 0x3fbd, 0x3fb8, 0x3fb2, 0x3fad, 0x3fa7, 0x3fa2, 0x3f9d,
  0x3f97, 0x3f92, 0x3f8c, 0x3f87, 0x3f81, 0x3f7c, 0x3f76, 0x3f71,
  0x3f6b, 0x3f66, 0x3f61, 0x3f5b, 0x3f56, 0x3f50, 0x3f4b, 0x3f45,
  0x3f40, 0x3f3a, 0x3f35, 0x3f2f, 0x3f2a, 0x3f24, 0x3f1f, 0x3f1a,
  0x3f14, 0x3f0f, 0x3f09, 0x3f04, 0x3efe, 0x3ef9, 0x3ef3, 0x3eee,
  0x3ee8, 0x3ee3, 0x3edd, 0x3ed8, 0x3ed2, 0x3ecd, 0x3ec7, 0x3ec2,
  0x3ebd, 0x3eb7, 0x3eb2, 0x3eac, 0x3ea7, 0x3ea1, 0x3e9c, 0x3e96,
  0x3e91, 0x3e8b, 0x3e86, 0x3e80, 0x3e7b, 0x3e75, 0x3e70, 0x3e6a,
  0x3e65, 0x3e5f, 0x3e5a, 0x3e54, 0x3e4f, 0x3e49, 0x3e44, 0x3e3e,
  0x3e39, 0x3e33, 0x3e2e, 0x3e28, 0x3e23, 0x3e1d, 0x3e18, 0x3e12,
  0x3e0d, 0x3e07, 0x3e02, 0x3dfc, 0x3df7, 0x3df1, 0x3dec, 0x3de6,
  0x3de1, 0x3ddb, 0x3dd6, 0x3dd0, 0x3dcb, 0x3dc5, 0x3dc0, 0x3dba,
  0x3db5, 0x3daf, 0x3daa, 0x3da4, 0x3d9f, 0x3d99, 0x3d94, 0x3d8e,
  0x3d89, 0x3d83, 0x3d7e, 0x3d78, 0x3d73, 0x3d6d, 0x3d68, 0x3d62,
  0x3d5d, 0x3d57, 0x3d52, 0x3d4c, 0x3d47, 0x3d41, 0x3d3c, 0x3d36,
  0x3d31, 0x3d2b, 0x3d26, 0x3d20, 0x3d1b, 0x3d15, 0x3d10, 0x3d0a,
  0x3d04, 0x3cff, 0x3cf9, 0x3cf4, 0x3cee, 0x3ce9, 0x3ce3, 0x3cde,
  0x3cd8, 0x3cd3, 0x3ccd, 0x3cc8, 0x3cc2, 0x3cbd, 0x3cb7, 0x3cb2,
  0x3cac, 0x3ca7, 0x3ca1, 0x3c9b, 0x3c96, 0x3c90, 0x3c8b, 0x3c85,
  0x3c80, 0x3c7a, 0x3c75, 0x3c6f, 0x3c6a, 0x3c64, 0x3c5f, 0x3c59,
  0x3c53, 0x3c4e, 0x3c48, 0x3c43, 0x3c3d, 0x3c38, 0x3c32, 0x3c2d,
  0x3c27, 0x3c22, 0x3c1c, 0x3c16, 0x3c11, 0x3c0b, 0x3c06, 0x3c00,
  0x3bfb, 0x3bf5, 0x3bf0, 0x3bea, 0x3be5, 0x3bdf, 0x3bd9, 0x3bd4,
  0x3bce, 0x3bc9, 0x3bc3, 0x3bbe, 0x3bb8, 0x3bb3, 0x3bad, 0x3ba7,
  0x3ba2, 0x3b9c, 0x3b97, 0x3b91, 0x3b8c, 0x3b86, 0x3b80, 0x3b7b,
  0x3b75, 0x3b70, 0x3b6a, 0x3b65, 0x3b5f, 0x3b5a, 0x3b54, 0x3b4e,
  0x3b49, 0x3b43, 0x3b3e, 0x3b38, 0x3b33, 0x3b2d, 0x3b27, 0x3b22,
  0x3b1c, 0x3b17, 0x3b11, 0x3b0c, 0x3b06, 0x3b00, 0x3afb, 0x3af5,
  0x3af0, 0x3aea, 0x3ae4, 0x3adf, 0x3ad9, 0x3ad4, 0x3ace, 0x3ac9,
  0x3ac3, 0x3abd, 0x3ab8, 0x3ab2, 0x3aad, 0x3aa7, 0x3aa2, 0x3a9c,
  0x3a96, 0x3a91, 0x3a8b, 0x3a86, 0x3a80, 0x3a7a, 0x3a75, 0x3a6f,
  0x3a6a, 0x3a64, 0x3a5e, 0x3a59, 0x3a53, 0x3a4e, 0x3a48, 0x3a42,
  0x3a3d, 0x3a37, 0x3a32, 0x3a2c, 0x3a26, 0x3a21, 0x3a1b, 0x3a16,
  0x3a10, 0x3a0b, 0x3a05, 0x39ff, 0x39fa, 0x39f4, 0x39ee, 0x39e9,
  0x39e3, 0x39de, 0x39d8, 0x39d2, 0x39cd, 0x39c7, 0x39c2, 0x39bc,
  0x39b6, 0x39b1, 0x39ab, 0x39a6, 0x39a0, 0x399a, 0x3995, 0x398f,
  0x398a, 0x3984, 0x397e, 0x3979, 0x3973, 0x396d, 0x3968, 0x3962,
  0x395d, 0x3957, 0x3951, 0x394c, 0x3946, 0x3941, 0x393b, 0x3935,
  0x3930, 0x392a, 0x3924, 0x391f, 0x3919, 0x3914, 0x390e, 0x3908,
  0x3903, 0x38fd, 0x38f7, 0x38f2, 0x38ec, 0x38e7, 0x38e1, 0x38db,
  0x38d6, 0x38d0, 0x38ca, 0x38c5, 0x38bf, 0x38ba, 0x38b4, 0x38ae,
  0x38a9, 0x38a3, 0x389d, 0x3898, 0x3892, 0x388c, 0x3887, 0x3881,
  0x387c, 0x3876, 0x3870, 0x386b, 0x3865, 0x385f, 0x385a, 0x3854,
  0x384e, 0x3849, 0x3843, 0x383d, 0x3838, 0x3832, 0x382d, 0x3827,
  0x3821, 0x381c, 0x3816, 0x3810, 0x380b, 0x3805, 0x37ff, 0x37fa,
  0x37f4, 0x37ee, 0x37e9, 0x37e3, 0x37dd, 0x37d8, 0x37d2, 0x37cc,
  0x37c7, 0x37c1, 0x37bc, 0x37b6, 0x37b0, 0x37ab, 0x37a5, 0x379f,
  0x379a, 0x3794, 0x378e, 0x3789, 0x3783, 0x377d, 0x3778, 0x3772,
  0x376c, 0x3767, 0x3761, 0x375b, 0x3756, 0x3750, 0x374a, 0x3745,
  0x373f, 0x3739, 0x3734, 0x372e, 0x3728, 0x3723, 0x371d, 0x3717,
  0x3712, 0x370c, 0x3706, 0x3701, 0x36fb, 0x36f5, 0x36f0, 0x36ea,
  0x36e4, 0x36df, 0x36d9, 0x36d3, 0x36ce, 0x36c8, 0x36c2, 0x36bc,
  0x36b7, 0x36b1, 0x36ab, 0x36a6, 0x36a0, 0x369a, 0x3695, 0x368f,
  0x3689, 0x3684, 0x367e, 0x3678, 0x3673, 0x366d, 0x3667, 0x3662,
  0x365c, 0x3656, 0x3650, 0x364b, 0x3645, 0x363f, 0x363a, 0x3634,
  0x362e, 0x3629, 0x3623, 0x361d, 0x3618, 0x3612, 0x360c, 0x3606,
  0x3601, 0x35fb, 0x35f5, 0x35f0, 0x35ea, 0x35e4, 0x35df, 0x35d9,
  0x35d3, 0x35cd, 0x35c8, 0x35c2, 0x35bc, 0x35b7, 0x35b1, 0x35ab,
  0x35a6, 0x35a0, 0x359a, 0x3594, 0x358f, 0x3589, 0x3583, 0x357e,
  0x3578, 0x3572, 0x356c, 0x3567, 0x3561, 0x355b, 0x3556, 0x3550,
  0x354a, 0x3544, 0x353f, 0x3539, 0x3533, 0x352e, 0x3528, 0x3522,
  0x351c, 0x3517, 0x3511, 0x350b, 0x3506, 0x3500, 0x34fa, 0x34f4,
  0x34ef, 0x34e9, 0x34e3, 0x34de, 0x34d8, 0x34d2, 0x34cc, 0x34c7,
  0x34c1, 0x34bb, 0x34b6, 0x34b0, 0x34aa, 0x34a4, 0x349f, 0x3499,
  0x3493, 0x348d, 0x3488, 0x3482, 0x347c, 0x3476, 0x3471, 0x346b,
  0x3465, 0x3460, 0x345a, 0x3454, 0x344e, 0x3449, 0x3443, 0x343d,
  0x3437, 0x3432, 0x342c, 0x3426, 0x3420, 0x341b, 0x3415, 0x340f,
  0x340a, 0x3404, 0x33fe, 0x33f8, 0x33f3, 0x33ed, 0x33e7, 0x33e1,
  0x33dc, 0x33d6, 0x33d0, 0x33ca, 0x33c5, 0x33bf, 0x33b9, 0x33b3,
  0x33ae, 0x33a8, 0x33a2, 0x339c, 0x3397, 0x3391, 0x338b, 0x3385,
  0x3380, 0x337a, 0x3374, 0x336e, 0x3369, 0x3363, 0x335d, 0x3357,
  0x3352, 0x334c, 0x3346, 0x3340, 0x333b, 0x3335, 0x332f, 0x3329,
  0x3324, 0x331e, 0x3318, 0x3312, 0x330c, 0x3307, 0x3301, 0x32fb,
  0x32f5, 0x32f0, 0x32ea, 0x32e4, 0x32de, 0x32d9, 0x32d3, 0x32cd,
  0x32c7, 0x32c2, 0x32bc, 0x32b6, 0x32b0, 0x32aa, 0x32a5, 0x329f,
  0x3299, 0x3293, 0x328e, 0x3288, 0x3282, 0x327c, 0x3276, 0x3271,
  0x326b, 0x3265, 0x325f, 0x325a, 0x3254, 0x324e, 0x3248, 0x3243,
  0x323d, 0x3237, 0x3231, 0x322b, 0x3226, 0x3220, 0x321a, 0x3214,
  0x320e, 0x3209, 0x3203, 0x31fd, 0x31f7, 0x31f2, 0x31ec, 0x31e6,
  0x31e0, 0x31da, 0x31d5, 0x31cf, 0x31c9, 0x31c3, 0x31bd, 0x31b8,
  0x31b2, 0x31ac, 0x31a6, 0x31a1, 0x319b, 0x3195, 0x318f, 0x3189,
  0x3184, 0x317e, 0x3178, 0x3172, 0x316c, 0x3167, 0x3161, 0x315b,
  0x3155, 0x314f, 0x314a, 0x3144, 0x313e, 0x3138, 0x3132, 0x312d,
  0x3127, 0x3121, 0x311b, 0x3115, 0x3110, 0x310a, 0x3104, 0x30fe,
  0x30f8, 0x30f3, 0x30ed, 0x30e7, 0x30e1, 0x30db, 0x30d6, 0x30d0,
  0x30ca, 0x30c4, 0x30be, 0x30b8, 0x30b3, 0x30ad, 0x30a7, 0x30a1,
  0x309b, 0x3096, 0x3090, 0x308a, 0x3084, 0x307e, 0x3079, 0x3073,
  0x306d, 0x3067, 0x3061, 0x305b, 0x3056, 0x3050, 0x304a, 0x3044,
  0x303e, 0x3039, 0x3033, 0x302d, 0x3027, 0x3021, 0x301b, 0x3016,
  0x3010, 0x300a, 0x3004, 0x2ffe, 0x2ff8, 0x2ff3, 0x2fed, 0x2fe7,
  0x2fe1, 0x2fdb, 0x2fd6, 0x2fd0, 0x2fca, 0x2fc4, 0x2fbe, 0x2fb8,
  0x2fb3, 0x2fad, 0x2fa7, 0x2fa1, 0x2f9b, 0x2f95, 0x2f90, 0x2f8a,
  0x2f84, 0x2f7e, 0x2f78, 0x2f72, 0x2f6d, 0x2f67, 0x2f61, 0x2f5b,
  0x2f55, 0x2f4f, 0x2f4a, 0x2f44, 0x2f3e, 0x2f38, 0x2f32, 0x2f2c,
  0x2f27, 0x2f21, 0x2f1b, 0x2f15, 0x2f0f, 0x2f09, 0x2f03, 0x2efe,
  0x2ef8, 0x2ef2, 0x2eec, 0x2ee6, 0x2ee0, 0x2edb, 0x2ed5, 0x2ecf,
  0x2ec9, 0x2ec3, 0x2ebd, 0x2eb7, 0x2eb2, 0x2eac, 0x2ea6, 0x2ea0,
  0x2e9a, 0x2e94, 0x2e8e, 0x2e89, 0x2e83, 0x2e7d, 0x2e77, 0x2e71,
  0x2e6b, 0x2e65, 0x2e60, 0x2e5a, 0x2e54, 0x2e4e, 0x2e48, 0x2e42,
  0x2e3c, 0x2e37, 0x2e31, 0x2e2b, 0x2e25, 0x2e1f, 0x2e19, 0x2e13,
  0x2e0e, 0x2e08, 0x2e02, 0x2dfc, 0x2df6, 0x2df0, 0x2dea, 0x2de5,
  0x2ddf, 0x2dd9, 0x2dd3, 0x2dcd, 0x2dc7, 0x2dc1, 0x2dbb, 0x2db6,
  0x2db0, 0x2daa, 0x2da4, 0x2d9e, 0x2d98, 0x2d92, 0x2d8d, 0x2d87,
  0x2d81, 0x2d7b, 0x2d75, 0x2d6f, 0x2d69, 0x2d63, 0x2d5e, 0x2d58,
  0x2d52, 0x2d4c, 0x2d46, 0x2d40, 0x2d3a, 0x2d34, 0x2d2f, 0x2d29,
  0x2d23, 0x2d1d, 0x2d17, 0x2d11, 0x2d0b, 0x2d05, 0x2cff, 0x2cfa,
  0x2cf4, 0x2cee, 0x2ce8, 0x2ce2, 0x2cdc, 0x2cd6, 0x2cd0, 0x2ccb,
  0x2cc5, 0x2cbf, 0x2cb9, 0x2cb3, 0x2cad, 0x2ca7, 0x2ca1, 0x2c9b,
  0x2c96, 0x2c90, 0x2c8a, 0x2c84, 0x2c7e, 0x2c78, 0x2c72, 0x2c6c,
  0x2c66, 0x2c61, 0x2c5b, 0x2c55, 0x2c4f, 0x2c49, 0x2c43, 0x2c3d,
  0x2c37, 0x2c31, 0x2c2b, 0x2c26, 0x2c20, 0x2c1a, 0x2c14, 0x2c0e,
  0x2c08, 0x2c02, 0x2bfc, 0x2bf6, 0x2bf0, 0x2beb, 0x2be5, 0x2bdf,
  0x2bd9, 0x2bd3, 0x2bcd, 0x2bc7, 0x2bc1, 0x2bbb, 0x2bb5, 0x2bb0,
  0x2baa, 0x2ba4, 0x2b9e, 0x2b98, 0x2b92, 0x2b8c, 0x2b86, 0x2b80,
  0x2b7a, 0x2b74, 0x2b6f, 0x2b69, 0x2b63, 0x2b5d, 0x2b57, 0x2b51,
  0x2b4b, 0x2b45, 0x2b3f, 0x2b39, 0x2b33, 0x2b2d, 0x2b28, 0x2b22,
  0x2b1c, 0x2b16, 0x2b10, 0x2b0a, 0x2b04, 0x2afe, 0x2af8, 0x2af2,
  0x2aec, 0x2ae6, 0x2ae1, 0x2adb, 0x2ad5, 0x2acf, 0x2ac9, 0x2ac3,
  0x2abd, 0x2ab7, 0x2ab1, 0x2aab, 0x2aa5, 0x2a9f, 0x2a99, 0x2a94,
  0x2a8e, 0x2a88, 0x2a82, 0x2a7c, 0x2a76, 0x2a70, 0x2a6a, 0x2a64,
  0x2a5e, 0x2a58, 0x2a52, 0x2a4c, 0x2a47, 0x2a41, 0x2a3b, 0x2a35,
  0x2a2f, 0x2a29, 0x2a23, 0x2a1d, 0x2a17, 0x2a11, 0x2a0b, 0x2a05,
  0x29ff, 0x29f9, 0x29f3, 0x29ee, 0x29e8, 0x29e2, 0x29dc, 0x29d6,
  0x29d0, 0x29ca, 0x29c4, 0x29be, 0x29b8, 0x29b2, 0x29ac, 0x29a6,
  0x29a0, 0x299a, 0x2994, 0x298e, 0x2989, 0x2983, 0x297d, 0x2977,
  0x2971, 0x296b, 0x2965, 0x295f, 0x2959, 0x2953, 0x294d, 0x2947,
  0x2941, 0x293b, 0x2935, 0x292f, 0x2929, 0x2923, 0x291d, 0x2918,
  0x2912, 0x290c, 0x2906, 0x2900, 0x28fa, 0x28f4, 0x28ee, 0x28e8,
  0x28e2, 0x28dc, 0x28d6, 0x28d0, 0x28ca, 0x28c4, 0x28be, 0x28b8,
  0x28b2, 0x28ac, 0x28a6, 0x28a0, 0x289a, 0x2895, 0x288f, 0x2889,
  0x2883, 0x287d, 0x2877, 0x2871, 0x286b, 0x2865, 0x285f, 0x2859,
  0x2853, 0x284d, 0x2847, 0x2841, 0x283b, 0x2835, 0x282f, 0x2829,
  0x2823, 0x281d, 0x2817, 0x2811, 0x280b, 0x2805, 0x27ff, 0x27f9,
  0x27f3, 0x27ee, 0x27e8, 0x27e2, 0x27dc, 0x27d6, 0x27d0, 0x27ca,
  0x27c4, 0x27be, 0x27b8, 0x27b2, 0x27ac, 0x27a6, 0x27a0, 0x279a,
  0x2794, 0x278e, 0x2788, 0x2782, 0x277c, 0x2776, 0x2770, 0x276a,
  0x2764, 0x275e, 0x2758, 0x2752, 0x274c, 0x2746, 0x2740, 0x273a,
  0x2734, 0x272e, 0x2728, 0x2722, 0x271c, 0x2716, 0x2710, 0x270a,
  0x2704, 0x26fe, 0x26f8, 0x26f2, 0x26ec, 0x26e7, 0x26e1, 0x26db,
  0x26d5, 0x26cf, 0x26c9, 0x26c3, 0x26bd, 0x26b7, 0x26b1, 0x26ab,
  0x26a5, 0x269f, 0x2699, 0x2693, 0x268d, 0x2687, 0x2681, 0x267b,
  0x2675, 0x266f, 0x2669, 0x2663, 0x265d, 0x2657, 0x2651, 0x264b,
  0x2645, 0x263f, 0x2639, 0x2633, 0x262d, 0x2627, 0x2621, 0x261b,
  0x2615, 0x260f, 0x2609, 0x2603, 0x25fd, 0x25f7, 0x25f1, 0x25eb,
  0x25e5, 0x25df, 0x25d9, 0x25d3, 0x25cd, 0x25c7, 0x25c1, 0x25bb,
  0x25b5, 0x25af, 0x25a9, 0x25a3, 0x259d, 0x2597, 0x2591, 0x258b,
  0x2585, 0x257f, 0x2579, 0x2573, 0x256d, 0x2567, 0x2561, 0x255b,
  0x2555, 0x254f, 0x2549, 0x2543, 0x253d, 0x2537, 0x2531, 0x252b,
  0x2525, 0x251f, 0x2519, 0x2513, 0x250c, 0x2506, 0x2500, 0x24fa,
  0x24f4, 0x24ee, 0x24e8, 0x24e2, 0x24dc, 0x24d6, 0x24d0, 0x24ca,
  0x24c4, 0x24be, 0x24b8, 0x24b2, 0x24ac, 0x24a6, 0x24a0, 0x249a,
  0x2494, 0x248e, 0x2488, 0x2482, 0x247c, 0x2476, 0x2470, 0x246a,
  0x2464, 0x245e, 0x2458, 0x2452, 0x244c, 0x2446, 0x2440, 0x243a,
  0x2434, 0x242e, 0x2428, 0x2422, 0x241c, 0x2416, 0x2410, 0x240a,
  0x2404, 0x23fd, 0x23f7, 0x23f1, 0x23eb, 0x23e5, 0x23df, 0x23d9,
  0x23d3, 0x23cd, 0x23c7, 0x23c1, 0x23bb, 0x23b5, 0x23af, 0x23a9,
  0x23a3, 0x239d, 0x2397, 0x2391, 0x238b, 0x2385, 0x237f, 0x2379,
  0x2373, 0x236d, 0x2367, 0x2361, 0x235b, 0x2355, 0x234e, 0x2348,
  0x2342, 0x233c, 0x2336, 0x2330, 0x232a, 0x2324, 0x231e, 0x2318,
  0x2312, 0x230c, 0x2306, 0x2300, 0x22fa, 0x22f4, 0x22ee, 0x22e8,
  0x22e2, 0x22dc, 0x22d6, 0x22d0, 0x22ca, 0x22c4, 0x22bd, 0x22b7,
  0x22b1, 0x22ab, 0x22a5, 0x229f, 0x2299, 0x2293, 0x228d, 0x2287,
  0x2281, 0x227b, 0x2275, 0x226f, 0x2269, 0x2263, 0x225d, 0x2257,
  0x2251, 0x224a, 0x2244, 0x223e, 0x2238, 0x2232, 0x222c, 0x2226,
  0x2220, 0x221a, 0x2214, 0x220e, 0x2208, 0x2202, 0x21fc, 0x21f6,
  0x21f0, 0x21ea, 0x21e4, 0x21dd, 0x21d7, 0x21d1, 0x21cb, 0x21c5,
  0x21bf, 0x21b9, 0x21b3, 0x21ad, 0x21a7, 0x21a1, 0x219b, 0x2195,
  0x218f, 0x2189, 0x2183, 0x217c, 0x2176, 0x2170, 0x216a, 0x2164,
  0x215e, 0x2158, 0x2152, 0x214c, 0x2146, 0x2140, 0x213a, 0x2134,
  0x212e, 0x2128, 0x2121, 0x211b, 0x2115, 0x210f, 0x2109, 0x2103,
  0x20fd, 0x20f7, 0x20f1, 0x20eb, 0x20e5, 0x20df, 0x20d9, 0x20d3,
  0x20cc, 0x20c6, 0x20c0, 0x20ba, 0x20b4, 0x20ae, 0x20a8, 0x20a2,
  0x209c, 0x2096, 0x2090, 0x208a, 0x2084, 0x207e, 0x2077, 0x2071,
  0x206b, 0x2065, 0x205f, 0x2059, 0x2053, 0x204d, 0x2047, 0x2041,
  0x203b, 0x2035, 0x202e, 0x2028, 0x2022, 0x201c, 0x2016, 0x2010,
  0x200a, 0x2004, 0x1ffe, 0x1ff8, 0x1ff2, 0x1fec, 0x1fe5, 0x1fdf,
  0x1fd9, 0x1fd3, 0x1fcd, 0x1fc7, 0x1fc1, 0x1fbb, 0x1fb5, 0x1faf,
  0x1fa9, 0x1fa3, 0x1f9c, 0x1f96, 0x1f90, 0x1f8a, 0x1f84, 0x1f7e,
  0x1f78, 0x1f72, 0x1f6c, 0x1f66, 0x1f60, 0x1f59, 0x1f53, 0x1f4d,
  0x1f47, 0x1f41, 0x1f3b, 0x1f35, 0x1f2f, 0x1f29, 0x1f23, 0x1f1d,
  0x1f16, 0x1f10, 0x1f0a, 0x1f04, 0x1efe, 0x1ef8, 0x1ef2, 0x1eec,
  0x1ee6, 0x1ee0, 0x1ed9, 0x1ed3, 0x1ecd, 0x1ec7, 0x1ec1, 0x1ebb,
  0x1eb5, 0x1eaf, 0x1ea9, 0x1ea3, 0x1e9c, 0x1e96, 0x1e90, 0x1e8a,
  0x1e84, 0x1e7e, 0x1e78, 0x1e72, 0x1e6c, 0x1e66, 0x1e5f, 0x1e59,
  0x1e53, 0x1e4d, 0x1e47, 0x1e41, 0x1e3b, 0x1e35, 0x1e2f, 0x1e29,
  0x1e22, 0x1e1c, 0x1e16, 0x1e10, 0x1e0a, 0x1e04, 0x1dfe, 0x1df8,
  0x1df2, 0x1deb, 0x1de5, 0x1ddf, 0x1dd9, 0x1dd3, 0x1dcd, 0x1dc7,
  0x1dc1, 0x1dbb, 0x1db4, 0x1dae, 0x1da8, 0x1da2, 0x1d9c, 0x1d96,
  0x1d90, 0x1d8a, 0x1d84, 0x1d7d, 0x1d77, 0x1d71, 0x1d6b, 0x1d65,
  0x1d5f, 0x1d59, 0x1d53, 0x1d4c, 0x1d46, 0x1d40, 0x1d3a, 0x1d34,
  0x1d2e, 0x1d28, 0x1d22, 0x1d1c, 0x1d15, 0x1d0f, 0x1d09, 0x1d03,
  0x1cfd, 0x1cf7, 0x1cf1, 0x1ceb, 0x1ce4, 0x1cde, 0x1cd8, 0x1cd2,
  0x1ccc, 0x1cc6, 0x1cc0, 0x1cba, 0x1cb3, 0x1cad, 0x1ca7, 0x1ca1,
  0x1c9b, 0x1c95, 0x1c8f, 0x1c89, 0x1c83, 0x1c7c, 0x1c76, 0x1c70,
  0x1c6a, 0x1c64, 0x1c5e, 0x1c58, 0x1c51, 0x1c4b, 0x1c45, 0x1c3f,
  0x1c39, 0x1c33, 0x1c2d, 0x1c27, 0x1c20, 0x1c1a, 0x1c14, 0x1c0e,
  0x1c08, 0x1c02, 0x1bfc, 0x1bf6, 0x1bef, 0x1be9, 0x1be3, 0x1bdd,
  0x1bd7, 0x1bd1, 0x1bcb, 0x1bc4, 0x1bbe, 0x1bb8, 0x1bb2, 0x1bac,
  0x1ba6, 0x1ba0, 0x1b9a, 0x1b93, 0x1b8d, 0x1b87, 0x1b81, 0x1b7b,
  0x1b75, 0x1b6f, 0x1b68, 0x1b62, 0x1b5c, 0x1b56, 0x1b50, 0x1b4a,
  0x1b44, 0x1b3d, 0x1b37, 0x1b31, 0x1b2b, 0x1b25, 0x1b1f, 0x1b19,
  0x1b13, 0x1b0c, 0x1b06, 0x1b00, 0x1afa, 0x1af4, 0x1aee, 0x1ae8,
  0x1ae1, 0x1adb, 0x1ad5, 0x1acf, 0x1ac9, 0x1ac3, 0x1abd, 0x1ab6,
  0x1ab0, 0x1aaa, 0x1aa4, 0x1a9e, 0x1a98, 0x1a91, 0x1a8b, 0x1a85,
  0x1a7f, 0x1a79, 0x1a73, 0x1a6d, 0x1a66, 0x1a60, 0x1a5a, 0x1a54,
  0x1a4e, 0x1a48, 0x1a42, 0x1a3b, 0x1a35, 0x1a2f, 0x1a29, 0x1a23,
  0x1a1d, 0x1a17, 0x1a10, 0x1a0a, 0x1a04, 0x19fe, 0x19f8, 0x19f2,
  0x19eb, 0x19e5, 0x19df, 0x19d9, 0x19d3, 0x19cd, 0x19c7, 0x19c0,
  0x19ba, 0x19b4, 0x19ae, 0x19a8, 0x19a2, 0x199b, 0x1995, 0x198f,
  0x1989, 0x1983, 0x197d, 0x1977, 0x1970, 0x196a, 0x1964, 0x195e,
  0x1958, 0x1952, 0x194b, 0x1945, 0x193f, 0x1939, 0x1933, 0x192d,
  0x1926, 0x1920, 0x191a, 0x1914, 0x190e, 0x1908, 0x1901, 0x18fb,
  0x18f5, 0x18ef, 0x18e9, 0x18e3, 0x18dc, 0x18d6, 0x18d0, 0x18ca,
  0x18c4, 0x18be, 0x18b8, 0x18b1, 0x18ab, 0x18a5, 0x189f, 0x1899,
  0x1893, 0x188c, 0x1886, 0x1880, 0x187a, 0x1874, 0x186e, 0x1867,
  0x1861, 0x185b, 0x1855, 0x184f, 0x1848, 0x1842, 0x183c, 0x1836,
  0x1830, 0x182a, 0x1823, 0x181d, 0x1817, 0x1811, 0x180b, 0x1805,
  0x17fe, 0x17f8, 0x17f2, 0x17ec, 0x17e6, 0x17e0, 0x17d9, 0x17d3,
  0x17cd, 0x17c7, 0x17c1, 0x17bb, 0x17b4, 0x17ae, 0x17a8, 0x17a2,
  0x179c, 0x1795, 0x178f, 0x1789, 0x1783, 0x177d, 0x1777, 0x1770,
  0x176a, 0x1764, 0x175e, 0x1758, 0x1752, 0x174b, 0x1745, 0x173f,
  0x1739, 0x1733, 0x172c, 0x1726, 0x1720, 0x171a, 0x1714, 0x170e,
  0x1707, 0x1701, 0x16fb, 0x16f5, 0x16ef, 0x16e8, 0x16e2, 0x16dc,
  0x16d6, 0x16d0, 0x16ca, 0x16c3, 0x16bd, 0x16b7, 0x16b1, 0x16ab,
  0x16a4, 0x169e, 0x1698, 0x1692, 0x168c, 0x1686, 0x167f, 0x1679,
  0x1673, 0x166d, 0x1667, 0x1660, 0x165a, 0x1654, 0x164e, 0x1648,
  0x1642, 0x163b, 0x1635, 0x162f, 0x1629, 0x1623, 0x161c, 0x1616,
  0x1610, 0x160a, 0x1604, 0x15fd, 0x15f7, 0x15f1, 0x15eb, 0x15e5,
  0x15de, 0x15d8, 0x15d2, 0x15cc, 0x15c6, 0x15c0, 0x15b9, 0x15b3,
  0x15ad, 0x15a7, 0x15a1, 0x159a, 0x1594, 0x158e, 0x1588, 0x1582,
  0x157b, 0x1575, 0x156f, 0x1569, 0x1563, 0x155c, 0x1556, 0x1550,
  0x154a, 0x1544, 0x153d, 0x1537, 0x1531, 0x152b, 0x1525, 0x151e,
  0x1518, 0x1512, 0x150c, 0x1506, 0x14ff, 0x14f9, 0x14f3, 0x14ed,
  0x14e7, 0x14e0, 0x14da, 0x14d4, 0x14ce, 0x14c8, 0x14c1, 0x14bb,
  0x14b5, 0x14af, 0x14a9, 0x14a2, 0x149c, 0x1496, 0x1490, 0x148a,
  0x1483, 0x147d, 0x1477, 0x1471, 0x146b, 0x1464, 0x145e, 0x1458,
  0x1452, 0x144c, 0x1445, 0x143f, 0x1439, 0x1433, 0x142d, 0x1426,
  0x1420, 0x141a, 0x1414, 0x140e, 0x1407, 0x1401, 0x13fb, 0x13f5,
  0x13ef, 0x13e8, 0x13e2, 0x13dc, 0x13d6, 0x13d0, 0x13c9, 0x13c3,
  0x13bd, 0x13b7, 0x13b1, 0x13aa, 0x13a4, 0x139e, 0x1398, 0x1391,
  0x138b, 0x1385, 0x137f, 0x1379, 0x1372, 0x136c, 0x1366, 0x1360,
  0x135a, 0x1353, 0x134d, 0x1347, 0x1341, 0x133b, 0x1334, 0x132e,
  0x1328, 0x1322, 0x131b, 0x1315, 0x130f, 0x1309, 0x1303, 0x12fc,
  0x12f6, 0x12f0, 0x12ea, 0x12e4, 0x12dd, 0x12d7, 0x12d1, 0x12cb,
  0x12c4, 0x12be, 0x12b8, 0x12b2, 0x12ac, 0x12a5, 0x129f, 0x1299,
  0x1293, 0x128d, 0x1286, 0x1280, 0x127a, 0x1274, 0x126d, 0x1267,
  0x1261, 0x125b, 0x1255, 0x124e, 0x1248, 0x1242, 0x123c, 0x1235,
  0x122f, 0x1229, 0x1223, 0x121d, 0x1216, 0x1210, 0x120a, 0x1204,
  0x11fd, 0x11f7, 0x11f1, 0x11eb, 0x11e5, 0x11de, 0x11d8, 0x11d2,
  0x11cc, 0x11c5, 0x11bf, 0x11b9, 0x11b3, 0x11ad, 0x11a6, 0x11a0,
  0x119a, 0x1194, 0x118d, 0x1187, 0x1181, 0x117b, 0x1175, 0x116e,
  0x1168, 0x1162, 0x115c, 0x1155, 0x114f, 0x1149, 0x1143, 0x113d,
  0x1136, 0x1130, 0x112a, 0x1124, 0x111d, 0x1117, 0x1111, 0x110b,
  0x1105, 0x10fe, 0x10f8, 0x10f2, 0x10ec, 0x10e5, 0x10df, 0x10d9,
  0x10d3, 0x10cc, 0x10c6, 0x10c0, 0x10ba, 0x10b4, 0x10ad, 0x10a7,
  0x10a1, 0x109b, 0x1094, 0x108e, 0x1088, 0x1082, 0x107b, 0x1075,
  0x106f, 0x1069, 0x1063, 0x105c, 0x1056, 0x1050, 0x104a, 0x1043,
  0x103d, 0x1037, 0x1031, 0x102a, 0x1024, 0x101e, 0x1018, 0x1012,
  0x100b, 0x1005, 0xfff, 0xff9, 0xff2, 0xfec, 0xfe6, 0xfe0,
  0xfd9, 0xfd3, 0xfcd, 0xfc7, 0xfc0, 0xfba, 0xfb4, 0xfae,
  0xfa8, 0xfa1, 0xf9b, 0xf95, 0xf8f, 0xf88, 0xf82, 0xf7c,
  0xf76, 0xf6f, 0xf69, 0xf63, 0xf5d, 0xf56, 0xf50, 0xf4a,
  0xf44, 0xf3e, 0xf37, 0xf31, 0xf2b, 0xf25, 0xf1e, 0xf18,
  0xf12, 0xf0c, 0xf05, 0xeff, 0xef9, 0xef3, 0xeec, 0xee6,
  0xee0, 0xeda, 0xed3, 0xecd, 0xec7, 0xec1, 0xeba, 0xeb4,
  0xeae, 0xea8, 0xea1, 0xe9b, 0xe95, 0xe8f, 0xe89, 0xe82,
  0xe7c, 0xe76, 0xe70, 0xe69, 0xe63, 0xe5d, 0xe57, 0xe50,
  0xe4a, 0xe44, 0xe3e, 0xe37, 0xe31, 0xe2b, 0xe25, 0xe1e,
  0xe18, 0xe12, 0xe0c, 0xe05, 0xdff, 0xdf9, 0xdf3, 0xdec,
  0xde6, 0xde0, 0xdda, 0xdd3, 0xdcd, 0xdc7, 0xdc1, 0xdba,
  0xdb4, 0xdae, 0xda8, 0xda1, 0xd9b, 0xd95, 0xd8f, 0xd88,
  0xd82, 0xd7c, 0xd76, 0xd6f, 0xd69, 0xd63, 0xd5d, 0xd56,
  0xd50, 0xd4a, 0xd44, 0xd3d, 0xd37, 0xd31, 0xd2b, 0xd24,
  0xd1e, 0xd18, 0xd12, 0xd0b, 0xd05, 0xcff, 0xcf9, 0xcf2,
  0xcec, 0xce6, 0xce0, 0xcd9, 0xcd3, 0xccd, 0xcc7, 0xcc0,
  0xcba, 0xcb4, 0xcae, 0xca7, 0xca1, 0xc9b, 0xc95, 0xc8e,
  0xc88, 0xc82, 0xc7c, 0xc75, 0xc6f, 0xc69, 0xc63, 0xc5c,
  0xc56, 0xc50, 0xc4a, 0xc43, 0xc3d, 0xc37, 0xc31, 0xc2a,
  0xc24, 0xc1e, 0xc18, 0xc11, 0xc0b, 0xc05, 0xbff, 0xbf8,
  0xbf2, 0xbec, 0xbe6, 0xbdf, 0xbd9, 0xbd3, 0xbcd, 0xbc6,
  0xbc0, 0xbba, 0xbb4, 0xbad, 0xba7, 0xba1, 0xb9b, 0xb94,
  0xb8e, 0xb88, 0xb81, 0xb7b, 0xb75, 0xb6f, 0xb68, 0xb62,
  0xb5c, 0xb56, 0xb4f, 0xb49, 0xb43, 0xb3d, 0xb36, 0xb30,
  0xb2a, 0xb24, 0xb1d, 0xb17, 0xb11, 0xb0b, 0xb04, 0xafe,
  0xaf8, 0xaf2, 0xaeb, 0xae5, 0xadf, 0xad8, 0xad2, 0xacc,
  0xac6, 0xabf, 0xab9, 0xab3, 0xaad, 0xaa6, 0xaa0, 0xa9a,
  0xa94, 0xa8d, 0xa87, 0xa81, 0xa7b, 0xa74, 0xa6e, 0xa68,
  0xa62, 0xa5b, 0xa55, 0xa4f, 0xa48, 0xa42, 0xa3c, 0xa36,
  0xa2f, 0xa29, 0xa23, 0xa1d, 0xa16, 0xa10, 0xa0a, 0xa04,
  0x9fd, 0x9f7, 0x9f1, 0x9eb, 0x9e4, 0x9de, 0x9d8, 0x9d1,
  0x9cb, 0x9c5, 0x9bf, 0x9b8, 0x9b2, 0x9ac, 0x9a6, 0x99f,
  0x999, 0x993, 0x98d, 0x986, 0x980, 0x97a, 0x973, 0x96d,
  0x967, 0x961, 0x95a, 0x954, 0x94e, 0x948, 0x941, 0x93b,
  0x935, 0x92f, 0x928, 0x922, 0x91c, 0x915, 0x90f, 0x909,
  0x903, 0x8fc, 0x8f6, 0x8f0, 0x8ea, 0x8e3, 0x8dd, 0x8d7,
  0x8d1, 0x8ca, 0x8c4, 0x8be, 0x8b7, 0x8b1, 0x8ab, 0x8a5,
  0x89e, 0x898, 0x892, 0x88c, 0x885, 0x87f, 0x879, 0x872,
  0x86c, 0x866, 0x860, 0x859, 0x853, 0x84d, 0x847, 0x840,
  0x83a, 0x834, 0x82e, 0x827, 0x821, 0x81b, 0x814, 0x80e,
  0x808, 0x802, 0x7fb, 0x7f5, 0x7ef, 0x7e9, 0x7e2, 0x7dc,
  0x7d6, 0x7cf, 0x7c9, 0x7c3, 0x7bd, 0x7b6, 0x7b0, 0x7aa,
  0x7a4, 0x79d, 0x797, 0x791, 0x78a, 0x784, 0x77e, 0x778,
  0x771, 0x76b, 0x765, 0x75f, 0x758, 0x752, 0x74c, 0x745,
  0x73f, 0x739, 0x733, 0x72c, 0x726, 0x720, 0x71a, 0x713,
  0x70d, 0x707, 0x700, 0x6fa, 0x6f4, 0x6ee, 0x6e7, 0x6e1,
  0x6db, 0x6d5, 0x6ce, 0x6c8, 0x6c2, 0x6bb, 0x6b5, 0x6af,
  0x6a9, 0x6a2, 0x69c, 0x696, 0x690, 0x689, 0x683, 0x67d,
  0x676, 0x670, 0x66a, 0x664, 0x65d, 0x657, 0x651, 0x64a,
  0x644, 0x63e, 0x638, 0x631, 0x62b, 0x625, 0x61f, 0x618,
  0x612, 0x60c, 0x605, 0x5ff, 0x5f9, 0x5f3, 0x5ec, 0x5e6,
  0x5e0, 0x5da, 0x5d3, 0x5cd, 0x5c7, 0x5c0, 0x5ba, 0x5b4,
  0x5ae, 0x5a7, 0x5a1, 0x59b, 0x594, 0x58e, 0x588, 0x582,
  0x57b, 0x575, 0x56f, 0x569, 0x562, 0x55c, 0x556, 0x54f,
  0x549, 0x543, 0x53d, 0x536, 0x530, 0x52a, 0x523, 0x51d,
  0x517, 0x511, 0x50a, 0x504, 0x4fe, 0x4f8, 0x4f1, 0x4eb,
  0x4e5, 0x4de, 0x4d8, 0x4d2, 0x4cc, 0x4c5, 0x4bf, 0x4b9,
  0x4b2, 0x4ac, 0x4a6, 0x4a0, 0x499, 0x493, 0x48d, 0x487,
  0x480, 0x47a, 0x474, 0x46d, 0x467, 0x461, 0x45b, 0x454,
  0x44e, 0x448, 0x441, 0x43b, 0x435, 0x42f, 0x428, 0x422,
  0x41c, 0x415, 0x40f, 0x409, 0x403, 0x3fc, 0x3f6, 0x3f0,
  0x3ea, 0x3e3, 0x3dd, 0x3d7, 0x3d0, 0x3ca, 0x3c4, 0x3be,
  0x3b7, 0x3b1, 0x3ab, 0x3a4, 0x39e, 0x398, 0x392, 0x38b,
  0x385, 0x37f, 0x378, 0x372, 0x36c, 0x366, 0x35f, 0x359,
  0x353, 0x34c, 0x346, 0x340, 0x33a, 0x333, 0x32d, 0x327,
  0x321, 0x31a, 0x314, 0x30e, 0x307, 0x301, 0x2fb, 0x2f5,
  0x2ee, 0x2e8, 0x2e2, 0x2db, 0x2d5, 0x2cf, 0x2c9, 0x2c2,
  0x2bc, 0x2b6, 0x2af, 0x2a9, 0x2a3, 0x29d, 0x296, 0x290,
  0x28a, 0x283, 0x27d, 0x277, 0x271, 0x26a, 0x264, 0x25e,
  0x258, 0x251, 0x24b, 0x245, 0x23e, 0x238, 0x232, 0x22c,
  0x225, 0x21f, 0x219, 0x212, 0x20c, 0x206, 0x200, 0x1f9,
  0x1f3, 0x1ed, 0x1e6, 0x1e0, 0x1da, 0x1d4, 0x1cd, 0x1c7,
  0x1c1, 0x1ba, 0x1b4, 0x1ae, 0x1a8, 0x1a1, 0x19b, 0x195,
  0x18e, 0x188, 0x182, 0x17c, 0x175, 0x16f, 0x169, 0x162,
  0x15c, 0x156, 0x150, 0x149, 0x143, 0x13d, 0x137, 0x130,
  0x12a, 0x124, 0x11d, 0x117, 0x111, 0x10b, 0x104, 0xfe,
  0xf8, 0xf1, 0xeb, 0xe5, 0xdf, 0xd8, 0xd2, 0xcc,
  0xc5, 0xbf, 0xb9, 0xb3, 0xac, 0xa6, 0xa0, 0x99,
  0x93, 0x8d, 0x87, 0x80, 0x7a, 0x74, 0x6d, 0x67,
  0x61, 0x5b, 0x54, 0x4e, 0x48, 0x41, 0x3b, 0x35,
  0x2f, 0x28, 0x22, 0x1c, 0x15, 0xf, 0x9, 0x3,
};

/**    
 * @brief  Initialization function for the Q15 DCT4/IDCT4.   
 * @param[in,out] *S         points to an instance of Q15 DCT4/IDCT4 structure.   
 * @param[in]     *S_RFFT    points to an instance of Q15 RFFT/RIFFT structure.   
 * @param[in]     *S_CFFT    points to an instance of Q15 CFFT/CIFFT structure.   
 * @param[in]     N          length of the DCT4.   
 * @param[in]     Nby2       half of the length of the DCT4.   
 * @param[in]     normalize  normalizing factor.   
 * @return  	  arm_status function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>N</code> is not a supported transform length.   
 * \par Normalizing factor:    
 * The normalizing factor is <code>sqrt(2/N)</code>, which depends on the size of transform <code>N</code>.    
 * Normalizing factors in 1.15 format are mentioned in the table below for different DCT sizes:    
 * \image html dct4NormalizingQ15Table.gif    
 */

arm_status arm_dct4_init_q15(
  arm_dct4_instance_q15 * S,
  arm_rfft_instance_q15 * S_RFFT,
  arm_cfft_radix4_instance_q15 * S_CFFT,
  uint16_t N,
  uint16_t Nby2,
  q15_t normalize)
{
  /*  Initialise the default arm status */
  arm_status status = ARM_MATH_SUCCESS;

  /* Initializing the pointer array with the weight table base addresses of different lengths */
  q15_t *twiddlePtr[4] = { (q15_t *) WeightsQ15_128, (q15_t *) WeightsQ15_512,
    (q15_t *) WeightsQ15_2048, (q15_t *) WeightsQ15_8192
  };

  /* Initializing the pointer array with the cos factor table base addresses of different lengths */
  q15_t *pCosFactor[4] =
    { (q15_t *) cos_factorsQ15_128, (q15_t *) cos_factorsQ15_512,
    (q15_t *) cos_factorsQ15_2048, (q15_t *) cos_factorsQ15_8192
  };

  /* Initialize the DCT4 length */
  S->N = N;

  /* Initialize the half of DCT4 length */
  S->Nby2 = Nby2;

  /* Initialize the DCT4 Normalizing factor */
  S->normalize = normalize;

  /* Initialize Real FFT Instance */
  S->pRfft = S_RFFT;

  /* Initialize Complex FFT Instance */
  S->pCfft = S_CFFT;

  switch (N)
  {
    /* Initialize the table modifier values */
  case 8192u:
    S->pTwiddle = twiddlePtr[3];
    S->pCosFactor = pCosFactor[3];
    break;
  case 2048u:
    S->pTwiddle = twiddlePtr[2];
    S->pCosFactor = pCosFactor[2];
    break;
  case 512u:
    S->pTwiddle = twiddlePtr[1];
    S->pCosFactor = pCosFactor[1];
    break;
  case 128u:
    S->pTwiddle = twiddlePtr[0];
    S->pCosFactor = pCosFactor[0];
    break;
  default:
    status = ARM_MATH_ARGUMENT_ERROR;
  }

  /* Initialize the RFFT/RIFFT */
  arm_rfft_init_q15(S->pRfft, S->N, 0u, 1u);

  /* return the status of DCT4 Init function */
  return (status);
}

/**    
   * @} end of DCT4_IDCT4 group    
   */
