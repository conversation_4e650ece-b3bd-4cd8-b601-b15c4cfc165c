#ifndef __APP_PROTOCOL_H__
#define __APP_PROTOCOL_H__

#include "stm32l4xx_hal.h"
#include <string.h>

/* 协议相关定义 */
#define PROTOCOL_MAX_DATA_LEN   64

/* 功能类型定义 */
typedef enum {
    FUNC_HEARTBEAT = 0x01,      // 心跳包
    FUNC_GET_VERSION = 0x02,    // 版本查询
    FUNC_SYSTEM_STATUS = 0x03,  // 系统状态
    FUNC_LED_CONTROL = 0x10,    // LED控制
    FUNC_KEY_STATUS = 0x11,     // 按键状态
    FUNC_IO_CONTROL = 0x12,     // IO控制
    FUNC_RELAY_CONTROL = 0x13,  // 继电器控制
    FUNC_TEMP_MEASURE = 0x20,   // 温度测量
    FUNC_ADC_MEASURE = 0x21,    // ADC测量
    FUNC_CURRENT_MEASURE = 0x22, // 电流测量
    FUNC_LOAD_TEST = 0x30,      // 动态负载测试
    FUNC_DISPLAY_CONTROL = 0x40 // 显示控制
} Protocol_FuncType_TypeDef;

/* LED端口定义 */
typedef enum {
    LED_PORT_SYS = 0,
    LED_PORT_DEBUG
} LED_Port_TypeDef;

/* 温度传感器端口定义 */
typedef enum {
    TEMP_PORT_MAX31856_CH1 = 0,
    TEMP_PORT_MAX31856_CH2,
    TEMP_PORT_DS18B20_CH1,
    TEMP_PORT_DS18B20_CH2
} Temp_Port_TypeDef;

/* ADC端口定义 */
typedef enum {
    ADC_PORT_ADS1115_CH0 = 0,
    ADC_PORT_ADS1115_CH1,
    ADC_PORT_ADS1115_CH2,
    ADC_PORT_ADS1115_CH3,
    ADC_PORT_INTERNAL_CH1,
    ADC_PORT_INTERNAL_CH2
} ADC_Port_TypeDef;

/* 电流测量端口定义 */
typedef enum {
    CURRENT_PORT_INA226 = 0
} Current_Port_TypeDef;

/* ADS1115通道定义 */
typedef enum {
    ADS1115_CHANNEL0 = 0,
    ADS1115_CHANNEL1,
    ADS1115_CHANNEL2,
    ADS1115_CHANNEL3
} ADS1115_Channel_TypeDef;

/* 错误码定义 */
typedef enum {
    ERROR_SUCCESS = 0x00,       // 成功
    ERROR_INVALID_FUNCTION = 0x01,  // 无效功能码
    ERROR_INVALID_PARAM = 0x02,     // 无效参数
    ERROR_INVALID_PORT = 0x03,      // 无效端口
    ERROR_DEVICE_FAULT = 0x04,      // 设备故障
    ERR_DATA_LEN = 0x05,           // 数据长度错误
    ERR_DATA_VALUE = 0x06,         // 数据值错误
    ERR_FUNC_CODE = 0x07           // 功能码错误
} Protocol_ErrorCode_TypeDef;

/* 协议帧结构体 */
typedef struct {
    uint8_t func_code;
    uint8_t data_len;
    uint8_t data[PROTOCOL_MAX_DATA_LEN];
} Protocol_Frame_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef APP_PROTOCOL_Init(UART_HandleTypeDef *huart);
void APP_PROTOCOL_Process(void);
void APP_PROTOCOL_RxCallback(uint8_t data);
void APP_PROTOCOL_SendResponse(uint8_t func_code, uint8_t *data, uint8_t len);
void APP_PROTOCOL_SendError(uint8_t func_code, uint8_t error_code);

#endif /* __APP_PROTOCOL_H__ */






