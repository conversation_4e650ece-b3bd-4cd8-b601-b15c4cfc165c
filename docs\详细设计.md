# 多功能测试板详细设计

## 1. 概述
本文档旨在对多功能测试板项目进行详细设计，根据项目计划中的模块划分，对各个模块的功能、接口、数据结构和实现细节进行详细阐述，为后续的编码工作提供指导。

## 2. 硬件抽象层（HAL）设计

### 2.1 GPIO配置
- **PA1(KEY1)**: 输入，下降沿中断
- **PA0(KEY2)**: 输入，下降沿中断
- **PC6(KEY3)**: 输入，下降沿中断
- **PC7(KEY4)**: 输入，下降沿中断
- **PB4(IL_DI1_IN_H)**: 输入，上升沿/下降沿中断（根据需求配置）
- **PB3(IL_DI1_IN_H)**: 输入，上升沿/下降沿中断（根据需求配置）
- **PB5(OL_REALY1_NC)**: 输出，推挽
- **PB8(OL_REALY1_NC)**: 输出，推挽
- **PA6(SPI_MISO)**: SPI复用功能
- **PA7(SPI_MOSI)**: SPI复用功能
- **PA5(SPI_CLK)**: SPI复用功能
- **PB0(SPI_CS1)**: 输出，推挽（软件控制）
- **PB2(SPI_CS2)**: 输出，推挽（软件控制）
- **PC4(MAX_FAULT1)**: 输入，下降沿中断
- **PA3(MAX_FAULT2)**: 输入，下降沿中断
- **PC5(MAX_DRDY1)**: 输入，下降沿中断
- **PB1(MAX_DRDY2)**: 输入，下降沿中断
- **PC8(18B20_DATA1)**: GPIO开漏模式（输入/输出切换）
- **PC9(18B20_DATA2)**: GPIO开漏模式（输入/输出切换）
- **PB6(I2C_SCL)**: I2C复用功能
- **PB7(I2C_SDA)**: I2C复用功能
- **PD2(ADC_RDY)**: 输入，下降沿中断
- **PA12(O_PWM_LOAD_TEST)**: 定时器PWM输出复用功能
- **PC3(ADC_IN1)**: 模拟输入
- **PC2(ADC_IN2)**: 模拟输入
- **PB10(I2C2_SCL)**: I2C复用功能
- **PB11(I2C2_SDA)**: I2C复用功能
- **PC1(UART_TX)**: UART复用功能
- **PC0(UART_RX)**: UART复用功能
- **PC13(SYS_LED)**: 输出，推挽
- **PB9(DEBUG_LED)**: 输出，推挽

### 2.2 定时器配置
- **TIM1**: 用于生成动态负载测试的PWM信号。
- **TIM2/3/4/5**: 用于按键消抖、继电器定时切换、DS18B20单总线时序、各种传感器周期性采集以及系统运行指示灯闪烁等。
- 配置SysTick定时器作为系统时基，实现毫秒级延时和任务调度。

### 2.3 串口配置
- **USART1**: 波特率115200，8N1，中断接收模式，并使用DMA发送。

### 2.4 SPI配置
- **SPI1**: 用于MAX31856MUD热电偶芯片通信，主模式，8位数据帧，MSB优先，软件片选控制。

### 2.5 I2C配置
- **I2C1**: 用于ADS1115和INA226AIDGSR通信，主模式。
- **I2C2**: 用于OLED显示屏通信，主模式。

### 2.6 ADC配置
- **ADC1**: 用于内部ADC采集，配置2个通道（PC3, PC2），使用DMA多通道采集，可配置采样周期和分辨率。

## 3. 板级支持包（BSP）设计

### 3.1 BSP_LED模块
- **文件**: `bsp_led.h`, `bsp_led.c`
- **功能**: 控制系统指示灯（SYS_LED）和调试指示灯（DEBUG_LED）的亮灭和闪烁。
- **接口**: 
  - `BSP_LED_Init()`: 初始化LED GPIO。
  - `BSP_LED_On(LED_ID)`: 点亮指定LED。
  - `BSP_LED_Off(LED_ID)`: 关闭指定LED。
  - `BSP_LED_Toggle(LED_ID)`: 翻转指定LED状态。
  - `BSP_LED_SetBlink(LED_ID, period_ms)`: 设置LED闪烁周期。

### 3.2 BSP_KEY模块
- **文件**: `bsp_key.h`, `bsp_key.c`
- **功能**: 管理4路按键的检测、消抖和状态上报。
- **数据结构**: `typedef struct { GPIO_TypeDef *GPIOx; uint16_t GPIO_Pin; uint32_t last_press_time; uint8_t state; uint32_t debounce_threshold; } Key_TypeDef;`
- **接口**: 
  - `BSP_KEY_Init()`: 初始化按键GPIO和中断。
  - `BSP_KEY_Scan()`: 在定时器中断中周期性扫描按键，实现消抖。
  - `BSP_KEY_GetState(KEY_ID)`: 获取按键当前状态。
  - `BSP_KEY_SetDebounceThreshold(KEY_ID, threshold_ms)`: 设置按键消抖时间。
  - `HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)`: 外部中断回调函数中触发按键处理。

### 3.3 BSP_IO模块
- **文件**: `bsp_io.h`, `bsp_io.c`
- **功能**: 管理2路外部输入IO的检测和状态上报。
- **接口**: 
  - `BSP_IO_Init()`: 初始化IO GPIO和中断。
  - `BSP_IO_GetState(IO_ID)`: 获取IO当前状态。

### 3.4 BSP_RELAY模块
- **文件**: `bsp_relay.h`, `bsp_relay.c`
- **功能**: 控制2路继电器的开关和定时切换。
- **数据结构**: `typedef struct { GPIO_TypeDef *GPIOx; uint16_t GPIO_Pin; uint32_t on_time_s; uint32_t off_time_s; uint8_t state; uint32_t last_toggle_time; } Relay_TypeDef;`
- **接口**: 
  - `BSP_RELAY_Init()`: 初始化继电器GPIO。
  - `BSP_RELAY_SetState(RELAY_ID, state)`: 设置继电器状态（开/关）。
  - `BSP_RELAY_SetToggleTime(RELAY_ID, on_time_s, off_time_s)`: 设置继电器定时切换时间。
  - `BSP_RELAY_Process()`: 在主循环或定时器中处理继电器定时切换逻辑。

## 4. 设备驱动层（Drivers）设计

### 4.1 DRV_MAX31856模块
- **文件**: `drv_max31856.h`, `drv_max31856.c`
- **功能**: 热电偶MAX31856MUD芯片驱动，通过SPI获取温度数据和故障信息。
- **接口**: 
  - `DRV_MAX31856_Init(SPI_HandleTypeDef *hspi, GPIO_TypeDef *CS_GPIOx, uint16_t CS_GPIO_Pin)`: 初始化MAX31856，绑定SPI和CS引脚。
  - `DRV_MAX31856_ReadTemp(channel)`: 读取指定通道的温度。
  - `DRV_MAX31856_ReadFault(channel)`: 读取指定通道的故障状态。
  - `HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)`: DRDY和FAULT引脚中断处理。

### 4.2 DRV_DS18B20模块
- **文件**: `drv_ds18b20.h`, `drv_ds18b20.c`
- **功能**: DS18B20数字温度传感器驱动，通过单总线获取温度数据。
- **接口**: 
  - `DRV_DS18B20_Init(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)`: 初始化DS18B20引脚。
  - `DRV_DS18B20_ReadTemp(channel)`: 读取指定通道的温度。
  - `DRV_DS18B20_StartConversion(channel)`: 启动温度转换。

### 4.3 DRV_ADS1115模块
- **文件**: `drv_ads1115.h`, `drv_ads1115.c`
- **功能**: 外部ADC ADS1115芯片驱动，通过I2C获取4通道电压数据。
- **接口**: 
  - `DRV_ADS1115_Init(I2C_HandleTypeDef *hi2c, uint8_t address)`: 初始化ADS1115，绑定I2C和地址。
  - `DRV_ADS1115_ReadVoltage(channel)`: 读取指定通道的电压值。
  - `DRV_ADS1115_StartConversion(channel)`: 启动指定通道的转换。
  - `HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)`: DRDY引脚中断处理。

### 4.4 DRV_INA226模块
- **文件**: `drv_ina226.h`, `drv_ina226.c`
- **功能**: INA226AIDGSR电流监测芯片驱动，通过I2C获取电流、电压和功率数据。
- **接口**: 
  - `DRV_INA226_Init(I2C_HandleTypeDef *hi2c, uint8_t address)`: 初始化INA226，绑定I2C和地址。
  - `DRV_INA226_ReadCurrent()`: 读取电流值。
  - `DRV_INA226_ReadVoltage()`: 读取电压值。
  - `DRV_INA226_ReadPower()`: 读取功率值。
  - `DRV_INA226_SetConfig(config_reg_value)`: 配置采样率、平均次数等。

### 4.5 DRV_OLED模块
- **文件**: `drv_oled.h`, `drv_oled.c`
- **功能**: OLED显示屏驱动，通过I2C进行显示控制。
- **接口**: 
  - `DRV_OLED_Init(I2C_HandleTypeDef *hi2c)`: 初始化OLED。
  - `DRV_OLED_Clear()`: 清屏。
  - `DRV_OLED_ShowChar(x, y, char_data)`: 显示字符。
  - `DRV_OLED_ShowString(x, y, string_data)`: 显示字符串。
  - `DRV_OLED_Refresh()`: 刷新屏幕。

## 5. 应用层（App）设计

### 5.1 APP_PROTOCOL模块
- **文件**: `app_protocol.h`, `app_protocol.c`
- **功能**: 实现串口通信协议的解析、命令执行和数据上报。
- **数据结构**: 定义协议帧结构体，配置信息结构体。
- **接口**: 
  - `APP_PROTOCOL_Init(UART_HandleTypeDef *huart)`: 初始化协议模块。
  - `APP_PROTOCOL_ReceiveHandler(uint8_t data)`: 串口接收中断回调函数中调用，进行协议解析。
  - `APP_PROTOCOL_Process()`: 在主循环中处理已接收的命令。
  - `APP_PROTOCOL_SendResponse(function_type, port_num, data, len)`: 发送数据上报帧。
  - `APP_PROTOCOL_SendError(error_code)`: 发送错误帧。

### 5.2 APP_LOAD_TEST模块
- **文件**: `app_load_test.h`, `app_load_test.c`
- **功能**: 动态负载测试模块，通过PWM信号控制负载。
- **接口**: 
  - `APP_LOAD_TEST_Init(TIM_HandleTypeDef *htim, uint32_t Channel)`: 初始化PWM。
  - `APP_LOAD_TEST_SetDutyCycle(duty_cycle)`: 设置PWM占空比。
  - `APP_LOAD_TEST_SetFrequency(frequency)`: 设置PWM频率。

### 5.3 APP_ADC模块
- **文件**: `app_adc.h`, `app_adc.c`
- **功能**: 内部ADC数据处理和上报。
- **接口**: 
  - `APP_ADC_Init()`: 初始化内部ADC。
  - `APP_ADC_StartConversion()`: 启动ADC转换。
  - `APP_ADC_GetData(channel)`: 获取指定通道的ADC值。
  - `APP_ADC_Process()`: 在主循环或定时器中周期性处理ADC数据并上报。
  - `APP_ADC_SetConfig(channel, sample_rate, interval)`: 配置采样速率和采样间隔。

### 5.4 APP_DISPLAY模块
- **文件**: `app_display.h`, `app_display.c`
- **功能**: OLED显示应用，显示系统状态、传感器数据等。
- **接口**: 
  - `APP_DISPLAY_Init()`: 初始化显示模块。
  - `APP_DISPLAY_Update()`: 周期性更新显示内容。
  - `APP_DISPLAY_ShowRunningStatus()`: 显示"运行中"状态。

## 6. 系统服务层（Service）设计

### 6.1 SRV_TIMER模块
- **文件**: `srv_timer.h`, `srv_timer.c`
- **功能**: 提供软件定时器管理，用于周期性任务调度。
- **接口**: 
  - `SRV_TIMER_Init()`: 初始化软件定时器。
  - `SRV_TIMER_Create(period_ms, callback_func, mode)`: 创建软件定时器。
  - `SRV_TIMER_Start(timer_id)`: 启动定时器。
  - `SRV_TIMER_Stop(timer_id)`: 停止定时器。
  - `SRV_TIMER_Process()`: 在系统主循环中调用，更新定时器状态。

### 6.2 SRV_CONFIG模块
- **文件**: `srv_config.h`, `srv_config.c`
- **功能**: 系统参数的存储、加载和管理，例如按键消抖时间、继电器定时时间等。
- **接口**: 
  - `SRV_CONFIG_Init()`: 初始化配置模块，从Flash/EEPROM加载配置。
  - `SRV_CONFIG_Save()`: 保存当前配置到Flash/EEPROM。
  - `SRV_CONFIG_GetParam(param_id)`: 获取指定参数的值。
  - `SRV_CONFIG_SetParam(param_id, value)`: 设置指定参数的值。

## 7. 异常处理与错误管理
- **看门狗**: 配置硬件看门狗（IWDG/WWDG），防止程序死机。
- **错误码**: 定义统一的错误码，用于协议上报和内部错误处理。
- **日志记录**: 通过串口或调试LED输出关键错误信息。

## 8. 系统启动流程
1. 系统时钟配置。
2. HAL库初始化。
3. 外设GPIO初始化。
4. 各模块（BSP、Drivers、Service）初始化。
5. 从配置模块加载系统参数。
6. 开启全局中断。
7. OLED显示"运行中"。
8. 进入主循环，执行任务调度和数据处理。

## 9. 任务调度与主循环
在主循环中，通过定时器和状态机调度各个模块的任务，例如：
```c
int main(void)
{
  // ... HAL库初始化和外设配置

  // 模块初始化
  BSP_LED_Init();
  BSP_KEY_Init();
  APP_PROTOCOL_Init(&huart1);
  // ... 其他模块初始化

  BSP_LED_SetBlink(SYS_LED, 1000); // 系统运行指示灯1Hz闪烁
  APP_DISPLAY_ShowRunningStatus(); // OLED显示"运行中"

  while (1)
  {
    // 协议处理
    APP_PROTOCOL_Process();

    // 任务调度
    SRV_TIMER_Process();
    BSP_KEY_Scan(); // 按键扫描
    BSP_RELAY_Process(); // 继电器控制
    APP_ADC_Process(); // ADC数据处理
    // ... 其他模块的周期性任务

    // 喂狗
    HAL_IWDG_Refresh(&hiwdg);
  }
}

// 定时器中断回调函数示例
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if (htim->Instance == TIMx)
  {
    // SRV_TIMER软件定时器更新
    SRV_TIMER_Tick();
  }
}

// 串口接收回调函数示例
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == USART1)
  {
    APP_PROTOCOL_ReceiveHandler(received_data); // 处理接收到的字节
    // 重新开启接收中断
    HAL_UART_Receive_IT(&huart1, (uint8_t *)uart_rx_buf, 1);
  }
}
``` 