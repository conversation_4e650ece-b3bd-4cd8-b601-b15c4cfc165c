#ifndef __BSP_LED_H__
#define __BSP_LED_H__

#include "stm32l4xx_hal.h"

/* LED类型定义 */
typedef enum {
    BSP_SYS_LED = 0,    // 系统运行指示灯
    BSP_DEBUG_LED       // 调试指示灯
} LED_TypeDef;

/* LED状态定义 */
typedef enum {
    LED_OFF = 0,
    LED_ON,
    LED_BLINK
} LED_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_LED_Init(void);
void BSP_LED_On(LED_TypeDef led);
void BSP_LED_Off(LED_TypeDef led);
void BSP_LED_Toggle(LED_TypeDef led);
void BSP_LED_SetBlink(LED_TypeDef led, uint32_t period_ms);
void BSP_LED_Process(void);

#endif /* __BSP_LED_H__ */




