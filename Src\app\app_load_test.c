/**
 * @file    app_load_test.c
 * @brief   动态负载测试应用层实现
 * <AUTHOR>
 * @date    2025-07-15
 */

#include "app/app_load_test.h"
#include <math.h>

/* 定时器句柄 */
static TIM_HandleTypeDef *load_htim;

/* 负载测试参数 */
static Load_Param_TypeDef load_params;

/* 测试状态 */
static uint8_t is_running = 0;

/* 测试计数器 */
static uint32_t test_counter = 0;
static uint32_t test_start_time = 0;

/* PWM通道 */
#define LOAD_PWM_CHANNEL TIM_CHANNEL_1

/* PWM周期 */
#define PWM_PERIOD 1000

/**
 * @brief  设置PWM占空比
 * @param  duty: 占空比(0-1000)
 * @retval HAL状态
 */
static HAL_StatusTypeDef SetPWMDuty(uint16_t duty)
{
    if(duty > PWM_PERIOD) {
        duty = PWM_PERIOD;
    }
    
    /* 设置PWM比较值 */
    __HAL_TIM_SET_COMPARE(load_htim, LOAD_PWM_CHANNEL, duty);
    
    return HAL_OK;
}

/**
 * @brief  动态负载测试初始化
 * @param  htim: 定时器句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_LOAD_TEST_Init(TIM_HandleTypeDef *htim)
{
    load_htim = htim;
    
    /* 初始化参数 */
    load_params.mode = LOAD_MODE_OFF;
    load_params.duty = 0;
    load_params.frequency = 1;
    load_params.amplitude = 500;
    load_params.offset = 500;
    load_params.duration = 0;
    
    /* 启动PWM输出 */
    if(HAL_TIM_PWM_Start(load_htim, LOAD_PWM_CHANNEL) != HAL_OK) {
        return HAL_ERROR;
    }
    
    /* 初始占空比为0 */
    SetPWMDuty(0);
    
    return HAL_OK;
}

/**
 * @brief  设置负载测试模式
 * @param  mode: 测试模式
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_LOAD_TEST_SetMode(Load_Mode_TypeDef mode)
{
    if(is_running) {
        return HAL_ERROR;  // 测试运行中不能修改模式
    }
    
    load_params.mode = mode;
    
    return HAL_OK;
}

/**
 * @brief  设置负载测试参数
 * @param  params: 参数结构体指针
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_LOAD_TEST_SetParams(Load_Param_TypeDef *params)
{
    if(is_running || params == NULL) {
        return HAL_ERROR;
    }
    
    /* 参数范围检查 */
    if(params->duty > PWM_PERIOD) {
        params->duty = PWM_PERIOD;
    }
    
    if(params->amplitude > PWM_PERIOD) {
        params->amplitude = PWM_PERIOD;
    }
    
    if(params->offset > PWM_PERIOD) {
        params->offset = PWM_PERIOD;
    }
    
    if(params->frequency == 0) {
        params->frequency = 1;
    }
    
    /* 复制参数 */
    load_params = *params;
    
    return HAL_OK;
}

/**
 * @brief  启动负载测试
 * @param  config: 负载测试配置结构体指针
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_LOAD_TEST_Start(LoadTest_Config_TypeDef *config)
{
    if(config == NULL) {
        return HAL_ERROR;
    }
    
    load_status.is_running = 1;
    load_status.mode = config->mode;
    load_status.elapsed_time = 0;
    
    // 设置PWM占空比
    SetPWMDuty(config->duty);
    
    return HAL_OK;
}

/**
 * @brief  停止负载测试
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_LOAD_TEST_Stop(void)
{
    is_running = 0;
    SetPWMDuty(0);
    
    return HAL_OK;
}

/**
 * @brief  获取负载测试状态
 * @param  None
 * @retval LoadTest_Status_TypeDef
 */
LoadTest_Status_TypeDef APP_LOAD_TEST_GetStatus(void)
{
    return load_status;
}

/**
 * @brief  负载测试处理任务
 * @param  None
 * @retval None
 */
void APP_LOAD_TEST_Process(void)
{
    if(!is_running) {
        return;
    }
    
    /* 检查测试持续时间 */
    if(load_params.duration > 0) {
        uint32_t elapsed = HAL_GetTick() - test_start_time;
        if(elapsed >= load_params.duration) {
            APP_LOAD_TEST_Stop();
            return;
        }
    }
    
    /* 根据不同模式生成PWM波形 */
    uint16_t duty = 0;
    
    switch(load_params.mode) {
        case LOAD_MODE_CONSTANT:
            duty = load_params.duty;
            break;
            
        case LOAD_MODE_PULSE:
            {
                /* 脉冲模式：周期性开关 */
                uint32_t period_ms = 1000 / load_params.frequency;
                uint32_t elapsed = HAL_GetTick() - test_start_time;
                uint32_t phase = elapsed % period_ms;
                
                if(phase < period_ms / 2) {
                    duty = load_params.amplitude;
                } else {
                    duty = 0;
                }
            }
            break;
            
        case LOAD_MODE_RAMP:
            {
                /* 斜坡模式：线性增加然后重置 */
                uint32_t period_ms = 1000 / load_params.frequency;
                uint32_t elapsed = HAL_GetTick() - test_start_time;
                uint32_t phase = elapsed % period_ms;
                
                duty = (uint16_t)((float)phase / period_ms * load_params.amplitude) + load_params.offset;
                if(duty > PWM_PERIOD) duty = PWM_PERIOD;
            }
            break;
            
        case LOAD_MODE_SINE:
            {
                /* 正弦模式：正弦波形 */
                uint32_t period_ms = 1000 / load_params.frequency;
                uint32_t elapsed = HAL_GetTick() - test_start_time;
                uint32_t phase = elapsed % period_ms;
                
                float angle = (float)phase / period_ms * 2.0f * 3.14159f;
                float sine_val = sinf(angle);
                
                duty = (uint16_t)(sine_val * load_params.amplitude / 2.0f + load_params.offset);
                if(duty > PWM_PERIOD) duty = PWM_PERIOD;
            }
            break;
            
        default:
            duty = 0;
            break;
    }
    
    /* 设置PWM占空比 */
    SetPWMDuty(duty);
    
    /* 更新计数器 */
    test_counter++;
}

