/**
 * @file    drv_ads1115.h
 * @brief   ADS1115外部ADC芯片驱动头文件
 * <AUTHOR>
 * @date    2025-01-28
 */

#ifndef __DRV_ADS1115_H
#define __DRV_ADS1115_H

#include "main.h"

/* ADS1115 I2C地址 */
#define ADS1115_I2C_ADDR        0x48  // ADDR引脚接地

/* ADS1115寄存器地址 */
#define ADS1115_REG_CONVERSION  0x00  // 转换结果寄存器
#define ADS1115_REG_CONFIG      0x01  // 配置寄存器
#define ADS1115_REG_LO_THRESH   0x02  // 低阈值寄存器
#define ADS1115_REG_HI_THRESH   0x03  // 高阈值寄存器

/* ADS1115通道定义 */
typedef enum {
    ADS1115_CHANNEL_0 = 0,
    ADS1115_CHANNEL_1 = 1,
    ADS1115_CHANNEL_2 = 2,
    ADS1115_CHANNEL_3 = 3
} ADS1115_Channel_TypeDef;

/* ADS1115增益定义 */
typedef enum {
    ADS1115_GAIN_6_144V = 0x00,  // ±6.144V
    ADS1115_GAIN_4_096V = 0x01,  // ±4.096V
    ADS1115_GAIN_2_048V = 0x02,  // ±2.048V (默认)
    ADS1115_GAIN_1_024V = 0x03,  // ±1.024V
    ADS1115_GAIN_0_512V = 0x04,  // ±0.512V
    ADS1115_GAIN_0_256V = 0x05   // ±0.256V
} ADS1115_Gain_TypeDef;

/* ADS1115数据速率定义 */
typedef enum {
    ADS1115_DR_8SPS = 0x00,     // 8 SPS
    ADS1115_DR_16SPS = 0x01,    // 16 SPS
    ADS1115_DR_32SPS = 0x02,    // 32 SPS
    ADS1115_DR_64SPS = 0x03,    // 64 SPS
    ADS1115_DR_128SPS = 0x04,   // 128 SPS (默认)
    ADS1115_DR_250SPS = 0x05,   // 250 SPS
    ADS1115_DR_475SPS = 0x06,   // 475 SPS
    ADS1115_DR_860SPS = 0x07    // 860 SPS
} ADS1115_DataRate_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef DRV_ADS1115_Init(I2C_HandleTypeDef *hi2c);
HAL_StatusTypeDef DRV_ADS1115_SetGain(ADS1115_Gain_TypeDef gain);
HAL_StatusTypeDef DRV_ADS1115_SetDataRate(ADS1115_DataRate_TypeDef data_rate);
HAL_StatusTypeDef DRV_ADS1115_ReadChannel(ADS1115_Channel_TypeDef channel, int16_t *raw_value);
HAL_StatusTypeDef DRV_ADS1115_ReadVoltage(ADS1115_Channel_TypeDef channel, float *voltage);
uint8_t DRV_ADS1115_IsReady(void);

#endif /* __DRV_ADS1115_H */
