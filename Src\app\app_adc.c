/**
 * @file    app_adc.c
 * @brief   内部ADC应用层实现
 * <AUTHOR>
 * @date    2025-01-29
 */

#include "app/app_adc.h"

/* ADC句柄 */
static ADC_HandleTypeDef *app_hadc;

/* ADC通道配置 */
static uint32_t adc_channels[INTERNAL_ADC_CH_MAX] = {
    ADC_CHANNEL_1,  // 内部ADC通道1
    ADC_CHANNEL_2   // 内部ADC通道2
};

/**
 * @brief  内部ADC初始化
 * @param  hadc: ADC句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_ADC_Init(ADC_HandleTypeDef *hadc)
{
    app_hadc = hadc;
    
    /* 校准ADC */
    if(HAL_ADCEx_Calibration_Start(app_hadc, ADC_SINGLE_ENDED) != HAL_OK) {
        return HAL_ERROR;
    }
    
    return HAL_OK;
}

/**
 * @brief  读取内部ADC电压值
 * @param  channel: ADC通道
 * @param  voltage: 电压值指针（单位：V）
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_ADC_ReadVoltage(InternalADC_Channel_TypeDef channel, float *voltage)
{
    ADC_ChannelConfTypeDef sConfig = {0};
    uint32_t adc_value;
    HAL_StatusTypeDef status;
    
    if(channel >= INTERNAL_ADC_CH_MAX) {
        return HAL_ERROR;
    }
    
    /* 配置ADC通道 */
    sConfig.Channel = adc_channels[channel];
    sConfig.Rank = ADC_REGULAR_RANK_1;
    sConfig.SamplingTime = ADC_SAMPLETIME_247CYCLES_5;
    sConfig.SingleDiff = ADC_SINGLE_ENDED;
    sConfig.OffsetNumber = ADC_OFFSET_NONE;
    sConfig.Offset = 0;
    
    status = HAL_ADC_ConfigChannel(app_hadc, &sConfig);
    if(status != HAL_OK) return status;
    
    /* 启动ADC转换 */
    status = HAL_ADC_Start(app_hadc);
    if(status != HAL_OK) return status;
    
    /* 等待转换完成 */
    status = HAL_ADC_PollForConversion(app_hadc, 100);
    if(status != HAL_OK) {
        HAL_ADC_Stop(app_hadc);
        return status;
    }
    
    /* 获取ADC值 */
    adc_value = HAL_ADC_GetValue(app_hadc);
    
    /* 停止ADC */
    HAL_ADC_Stop(app_hadc);
    
    /* 转换为电压值（假设参考电压为3.3V，12位ADC） */
    *voltage = ((float)adc_value / 4095.0f) * 3.3f;
    
    return HAL_OK;
}

/**
 * @brief  ADC处理任务
 * @param  None
 * @retval None
 */
void APP_ADC_Process(void)
{
    /* 这里可以添加周期性ADC采集任务 */
    /* 目前主要通过协议命令触发采集 */
}