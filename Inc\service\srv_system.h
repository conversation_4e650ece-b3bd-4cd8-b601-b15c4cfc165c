#ifndef __SRV_SYSTEM_H__
#define __SRV_SYSTEM_H__

#include "stm32l4xx_hal.h"
#include "bsp/bsp_led.h"

/* 系统错误类型定义 */
typedef enum {
    SYS_ERROR_NONE = 0,
    SYS_ERROR_SENSOR_FAULT,
    SYS_ERROR_COMMUNICATION,
    SYS_ERROR_MEMORY,
    SYS_ERROR_HARDWARE
} System_Error_TypeDef;

/* 系统状态结构体 */
typedef struct {
    uint8_t cpu_usage;          // CPU使用率 (0-100%)
    uint8_t memory_usage;       // 内存使用率 (0-100%)
    float temperature;          // 系统温度 (°C)
    float voltage;              // 系统电压 (V)
    uint32_t error_count;       // 错误计数
    uint32_t uptime;            // 运行时间 (秒)
    uint32_t uptime_seconds;    // 运行时间秒数
    uint8_t system_health;      // 系统健康度 (0-100)
} System_Status_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef SRV_SYSTEM_Init(void);
void SRV_SYSTEM_Process(void);
System_Status_TypeDef* SRV_SYSTEM_GetStatus(void);
void SRV_SYSTEM_ReportError(System_Error_TypeDef error);
uint8_t SRV_SYSTEM_SelfCheck(void);
void SRV_SYSTEM_FeedWatchdog(void);

#endif /* __SRV_SYSTEM_H__ */







