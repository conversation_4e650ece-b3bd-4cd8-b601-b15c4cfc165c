#include "service/srv_timer.h"

/* 定时器结构体 */
typedef struct {
    uint32_t period_ms;
    uint32_t last_tick;
    TimerCallback_TypeDef callback;
    uint8_t active;
} Timer_TypeDef;

/* 定时器数组 */
static Timer_TypeDef timers[MAX_TIMERS];
static uint8_t timer_count = 0;

HAL_StatusTypeDef SRV_TIMER_Init(void)
{
    for(int i = 0; i < MAX_TIMERS; i++) {
        timers[i].active = 0;
        timers[i].callback = NULL;
    }
    timer_count = 0;
    return HAL_OK;
}

int8_t SRV_TIMER_Create(uint32_t period_ms, TimerCallback_TypeDef callback)
{
    if(timer_count >= MAX_TIMERS || callback == NULL) {
        return -1;
    }
    
    timers[timer_count].period_ms = period_ms;
    timers[timer_count].callback = callback;
    timers[timer_count].active = 0;
    timers[timer_count].last_tick = HAL_GetTick();
    
    return timer_count++;
}

HAL_StatusTypeDef SRV_TIMER_Start(int8_t timer_id)
{
    if(timer_id < 0 || timer_id >= timer_count) {
        return HAL_ERROR;
    }
    
    timers[timer_id].active = 1;
    timers[timer_id].last_tick = HAL_GetTick();
    return HAL_OK;
}

HAL_StatusTypeDef SRV_TIMER_Stop(int8_t timer_id)
{
    if(timer_id < 0 || timer_id >= timer_count) {
        return HAL_ERROR;
    }
    
    timers[timer_id].active = 0;
    return HAL_OK;
}

void SRV_TIMER_Process(void)
{
    uint32_t current_tick = HAL_GetTick();
    
    for(int i = 0; i < timer_count; i++) {
        if(timers[i].active && timers[i].callback != NULL) {
            if(current_tick - timers[i].last_tick >= timers[i].period_ms) {
                timers[i].last_tick = current_tick;
                timers[i].callback();
            }
        }
    }
}


