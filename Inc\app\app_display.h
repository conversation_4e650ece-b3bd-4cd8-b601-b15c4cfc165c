/**
 * @file    app_display.h
 * @brief   显示应用层头文件
 * <AUTHOR>
 * @date    2025-01-29
 */

#ifndef __APP_DISPLAY_H
#define __APP_DISPLAY_H

#include "main.h"
#include "drivers/drv_oled.h"

/* 显示页面定义 */
typedef enum {
    DISPLAY_PAGE_MAIN = 0,      // 主页面
    DISPLAY_PAGE_SENSOR,        // 传感器数据页面
    DISPLAY_PAGE_STATUS,        // 状态页面
    DISPLAY_PAGE_MAX
} Display_Page_TypeDef;

/* 显示内容定义 */
typedef struct {
    char title[32];             // 标题
    float temp_max31856_ch1;    // MAX31856通道1温度
    float temp_max31856_ch2;    // MAX31856通道2温度
    float temp_ds18b20_ch1;     // DS18B20通道1温度
    float temp_ds18b20_ch2;     // DS18B20通道2温度
    float voltage_ads1115[4];   // ADS1115电压值
    float voltage_internal[2];  // 内部ADC电压值
    float ina226_voltage;       // INA226电压
    float ina226_current;       // INA226电流
    float ina226_power;         // INA226功率
    uint8_t key_status;         // 按键状态
    uint8_t io_status;          // IO状态
    uint8_t relay_status;       // 继电器状态
    uint8_t led_status;         // LED状态
} Display_Data_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef APP_DISPLAY_Init(I2C_HandleTypeDef *hi2c);
HAL_StatusTypeDef APP_DISPLAY_SetPage(Display_Page_TypeDef page);
HAL_StatusTypeDef APP_DISPLAY_UpdateData(Display_Data_TypeDef *data);
HAL_StatusTypeDef APP_DISPLAY_ShowMessage(const char *message);
HAL_StatusTypeDef APP_DISPLAY_ShowRunningStatus(void);
void APP_DISPLAY_Process(void);

#endif /* __APP_DISPLAY_H */
