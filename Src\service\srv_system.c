/**
 * @file    srv_system.c
 * @brief   系统服务实现
 * <AUTHOR>
 * @date    2025-07-15
 */

#include "service/srv_system.h"

/* 系统状态变量 */
static System_Status_TypeDef system_status;
static uint32_t system_uptime = 0;
static uint32_t last_uptime_update = 0;

/* 注释掉看门狗相关代码 */
// extern IWDG_HandleTypeDef hiwdg;

/**
 * @brief  系统服务初始化
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef SRV_SYSTEM_Init(void)
{
    system_status.cpu_usage = 0;
    system_status.memory_usage = 0;
    system_status.temperature = 25.0f;
    system_status.voltage = 3.3f;
    system_status.error_count = 0;
    system_status.uptime = 0;
    
    system_uptime = 0;
    last_uptime_update = HAL_GetTick();
    
    return HAL_OK;
}

/**
 * @brief  系统服务处理任务
 * @param  None
 * @retval None
 */
void SRV_SYSTEM_Process(void)
{
    uint32_t current_tick = HAL_GetTick();
    
    /* 更新运行时间 */
    if(current_tick - last_uptime_update >= 1000) {
        system_uptime++;
        system_status.uptime = system_uptime;
        last_uptime_update = current_tick;
    }
    
    /* 更新系统状态 */
    // TODO: 实际的CPU使用率计算
    system_status.cpu_usage = 50;  // 临时值
    
    // TODO: 实际的内存使用率计算
    system_status.memory_usage = 30;  // 临时值
    
    // TODO: 实际的温度读取
    system_status.temperature = 25.0f;  // 临时值
    
    // TODO: 实际的电压读取
    system_status.voltage = 3.3f;  // 临时值
}

/**
 * @brief  获取系统状态
 * @param  status: 状态结构体指针
 * @retval HAL状态
 */
System_Status_TypeDef* SRV_SYSTEM_GetStatus(void)
{
    return &system_status;
}

/**
 * @brief  报告系统错误
 * @param  error: 错误类型
 * @retval None
 */
void SRV_SYSTEM_ReportError(System_Error_TypeDef error)
{
    system_status.error_count++;
    
    /* 根据错误类型降低系统健康度 */
    switch(error) {
        case SYS_ERROR_SENSOR_FAULT:
            if(system_status.system_health > 10) {
                system_status.system_health -= 10;
            }
            break;
            
        case SYS_ERROR_COMMUNICATION:
            if(system_status.system_health > 5) {
                system_status.system_health -= 5;
            }
            break;
            
        case SYS_ERROR_MEMORY:
            if(system_status.system_health > 20) {
                system_status.system_health -= 20;
            }
            break;
            
        case SYS_ERROR_HARDWARE:
            if(system_status.system_health > 30) {
                system_status.system_health -= 30;
            }
            break;
            
        default:
            break;
    }
    
    /* 系统健康度过低时闪烁错误指示灯 */
    if(system_status.system_health < 50) {
        BSP_LED_SetBlink(BSP_DEBUG_LED, 200);  // 快速闪烁表示系统异常
    }
}

/**
 * @brief  系统自检
 * @param  None
 * @retval 系统健康度(0-100)
 */
uint8_t SRV_SYSTEM_SelfCheck(void)
{
    uint8_t health_score = 100;
    
    /* 检查内存使用情况 */
    // 这里可以添加栈使用率检查等
    
    /* 检查外设状态 */
    // 可以检查各个传感器的通信状态
    
    /* 检查错误率 */
    if(system_status.error_count > 0) {
        uint32_t error_rate = system_status.error_count * 100 / (system_status.uptime_seconds + 1);
        if(error_rate > 10) {  // 错误率超过10%
            health_score -= 20;
        } else if(error_rate > 5) {  // 错误率超过5%
            health_score -= 10;
        }
    }
    
    return health_score;
}

/**
 * @brief  喂看门狗
 * @param  None
 * @retval None
 */
void SRV_SYSTEM_FeedWatchdog(void)
{
    /* 暂时注释掉看门狗喂狗 */
    // HAL_IWDG_Refresh(&hiwdg);
}


