/**
 * @file    drv_ads1115.c
 * @brief   ADS1115外部ADC芯片驱动实现
 * <AUTHOR>
 * @date    2025-01-29
 */

#include "drivers/drv_ads1115.h"

/* I2C句柄 */
static I2C_HandleTypeDef *ads1115_hi2c;

/* 当前配置 */
static ADS1115_Gain_TypeDef current_gain = ADS1115_GAIN_2_048V;
static ADS1115_DataRate_TypeDef current_data_rate = ADS1115_DR_128SPS;

/* 增益对应的电压范围（mV） */
static const float gain_voltage_range[] = {
    6144.0f,  // ±6.144V
    4096.0f,  // ±4.096V
    2048.0f,  // ±2.048V
    1024.0f,  // ±1.024V
    512.0f,   // ±0.512V
    256.0f    // ±0.256V
};

/**
 * @brief  写寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 写入数据（16位）
 * @retval HAL状态
 */
static HAL_StatusTypeDef ADS1115_WriteRegister(uint8_t reg_addr, uint16_t data)
{
    uint8_t tx_data[3];
    
    tx_data[0] = reg_addr;
    tx_data[1] = (data >> 8) & 0xFF;  // 高字节
    tx_data[2] = data & 0xFF;         // 低字节
    
    return HAL_I2C_Master_Transmit(ads1115_hi2c, ADS1115_I2C_ADDR << 1, tx_data, 3, 100);
}

/**
 * @brief  读寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 读取数据指针（16位）
 * @retval HAL状态
 */
static HAL_StatusTypeDef ADS1115_ReadRegister(uint8_t reg_addr, uint16_t *data)
{
    uint8_t rx_data[2];
    HAL_StatusTypeDef status;
    
    /* 发送寄存器地址 */
    status = HAL_I2C_Master_Transmit(ads1115_hi2c, ADS1115_I2C_ADDR << 1, &reg_addr, 1, 100);
    if(status != HAL_OK) return status;
    
    /* 读取数据 */
    status = HAL_I2C_Master_Receive(ads1115_hi2c, ADS1115_I2C_ADDR << 1, rx_data, 2, 100);
    if(status != HAL_OK) return status;
    
    *data = (rx_data[0] << 8) | rx_data[1];
    
    return HAL_OK;
}

/**
 * @brief  ADS1115初始化
 * @param  hi2c: I2C句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_ADS1115_Init(I2C_HandleTypeDef *hi2c)
{
    uint16_t config_reg;
    HAL_StatusTypeDef status;
    
    ads1115_hi2c = hi2c;
    
    /* 读取配置寄存器验证通信 */
    status = ADS1115_ReadRegister(ADS1115_REG_CONFIG, &config_reg);
    if(status != HAL_OK) {
        return status;  // 通信失败
    }
    
    /* 配置默认参数 */
    status = DRV_ADS1115_SetGain(ADS1115_GAIN_2_048V);
    if(status != HAL_OK) return status;
    
    status = DRV_ADS1115_SetDataRate(ADS1115_DR_128SPS);
    if(status != HAL_OK) return status;
    
    return HAL_OK;
}

/**
 * @brief  设置增益
 * @param  gain: 增益设置
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_ADS1115_SetGain(ADS1115_Gain_TypeDef gain)
{
    current_gain = gain;
    return HAL_OK;
}

/**
 * @brief  设置数据速率
 * @param  data_rate: 数据速率
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_ADS1115_SetDataRate(ADS1115_DataRate_TypeDef data_rate)
{
    current_data_rate = data_rate;
    return HAL_OK;
}

/**
 * @brief  读取指定通道的原始值
 * @param  channel: ADC通道
 * @param  raw_value: 原始值指针
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_ADS1115_ReadChannel(ADS1115_Channel_TypeDef channel, int16_t *raw_value)
{
    uint16_t config_reg;
    uint16_t conversion_reg;
    HAL_StatusTypeDef status;
    uint32_t timeout = 1000;  // 超时计数
    
    /* 配置寄存器设置 */
    config_reg = 0x8000 |                    // OS: 开始单次转换
                 ((4 + channel) << 12) |     // MUX: 选择通道（单端输入）
                 (current_gain << 9) |       // PGA: 增益设置
                 (1 << 8) |                  // MODE: 单次转换模式
                 (current_data_rate << 5) |  // DR: 数据速率
                 (0 << 4) |                  // COMP_MODE: 传统比较器
                 (0 << 3) |                  // COMP_POL: 低电平有效
                 (0 << 2) |                  // COMP_LAT: 非锁存
                 (3 << 0);                   // COMP_QUE: 禁用比较器
    
    /* 写入配置寄存器启动转换 */
    status = ADS1115_WriteRegister(ADS1115_REG_CONFIG, config_reg);
    if(status != HAL_OK) return status;
    
    /* 等待转换完成 */
    do {
        HAL_Delay(1);
        status = ADS1115_ReadRegister(ADS1115_REG_CONFIG, &config_reg);
        if(status != HAL_OK) return status;
        
        timeout--;
        if(timeout == 0) return HAL_TIMEOUT;
        
    } while((config_reg & 0x8000) == 0);  // 等待OS位置1
    
    /* 读取转换结果 */
    status = ADS1115_ReadRegister(ADS1115_REG_CONVERSION, &conversion_reg);
    if(status != HAL_OK) return status;
    
    *raw_value = (int16_t)conversion_reg;
    
    return HAL_OK;
}

/**
 * @brief  读取指定通道的电压值
 * @param  channel: ADC通道
 * @param  voltage: 电压值指针（单位：V）
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_ADS1115_ReadVoltage(ADS1115_Channel_TypeDef channel, float *voltage)
{
    int16_t raw_value;
    HAL_StatusTypeDef status;
    
    status = DRV_ADS1115_ReadChannel(channel, &raw_value);
    if(status != HAL_OK) return status;
    
    /* 转换为电压值 */
    *voltage = ((float)raw_value / 32768.0f) * (gain_voltage_range[current_gain] / 1000.0f);
    
    return HAL_OK;
}

/**
 * @brief  检查转换是否完成
 * @param  None
 * @retval 1-完成，0-未完成
 */
uint8_t DRV_ADS1115_IsReady(void)
{
    uint16_t config_reg;
    
    if(ADS1115_ReadRegister(ADS1115_REG_CONFIG, &config_reg) == HAL_OK) {
        return (config_reg & 0x8000) ? 1 : 0;
    }
    
    return 0;
}