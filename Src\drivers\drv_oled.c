/**
 * @file    drv_oled.c
 * @brief   OLED显示驱动实现
 * <AUTHOR>
 * @date    2025-01-29
 */

#include "drivers/drv_oled.h"
#include <string.h>
#include <stdio.h>

/* I2C句柄 */
static I2C_HandleTypeDef *oled_hi2c;

/* 显示缓冲区 */
static uint8_t oled_buffer[OLED_WIDTH * OLED_HEIGHT / 8];

/* 6x8字体数据（简化版，仅包含数字和基本字符） */
static const uint8_t font_6x8[][6] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // 空格
    {0x3E, 0x51, 0x49, 0x45, 0x3E, 0x00}, // 0
    {0x00, 0x42, 0x7F, 0x40, 0x00, 0x00}, // 1
    {0x42, 0x61, 0x51, 0x49, 0x46, 0x00}, // 2
    {0x21, 0x41, 0x45, 0x4B, 0x31, 0x00}, // 3
    {0x18, 0x14, 0x12, 0x7F, 0x10, 0x00}, // 4
    {0x27, 0x45, 0x45, 0x45, 0x39, 0x00}, // 5
    {0x3C, 0x4A, 0x49, 0x49, 0x30, 0x00}, // 6
    {0x01, 0x71, 0x09, 0x05, 0x03, 0x00}, // 7
    {0x36, 0x49, 0x49, 0x49, 0x36, 0x00}, // 8
    {0x06, 0x49, 0x49, 0x29, 0x1E, 0x00}, // 9
    {0x7C, 0x12, 0x11, 0x12, 0x7C, 0x00}, // A
    {0x7F, 0x49, 0x49, 0x49, 0x36, 0x00}, // B
    {0x3E, 0x41, 0x41, 0x41, 0x22, 0x00}, // C
    {0x7F, 0x41, 0x41, 0x22, 0x1C, 0x00}, // D
    {0x7F, 0x49, 0x49, 0x49, 0x41, 0x00}, // E
    {0x7F, 0x09, 0x09, 0x09, 0x01, 0x00}, // F
    {0x00, 0x00, 0x60, 0x60, 0x00, 0x00}, // .
    {0x08, 0x08, 0x08, 0x08, 0x08, 0x00}, // -
    {0x00, 0x36, 0x36, 0x00, 0x00, 0x00}, // :
};

/**
 * @brief  发送命令到OLED
 * @param  cmd: 命令字节
 * @retval HAL状态
 */
static HAL_StatusTypeDef OLED_WriteCommand(uint8_t cmd)
{
    uint8_t data[2] = {0x00, cmd};  // 0x00表示命令
    return HAL_I2C_Master_Transmit(oled_hi2c, OLED_I2C_ADDR, data, 2, 100);
}

/**
 * @brief  发送数据到OLED
 * @param  data: 数据缓冲区
 * @param  len: 数据长度
 * @retval HAL状态
 */
static HAL_StatusTypeDef OLED_WriteData(uint8_t *data, uint16_t len)
{
    uint8_t cmd_data[len + 1];
    cmd_data[0] = 0x40;  // 0x40表示数据
    memcpy(&cmd_data[1], data, len);
    return HAL_I2C_Master_Transmit(oled_hi2c, OLED_I2C_ADDR, cmd_data, len + 1, 100);
}

/**
 * @brief  OLED初始化
 * @param  hi2c: I2C句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_Init(I2C_HandleTypeDef *hi2c)
{
    oled_hi2c = hi2c;
    
    /* 初始化序列 */
    if(OLED_WriteCommand(OLED_CMD_DISPLAY_OFF) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_CLOCK_DIV) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x80) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_MUX_RATIO) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x3F) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_DISPLAY_OFFSET) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x00) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_START_LINE | 0x00) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_CHARGE_PUMP) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x14) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_MEMORY_MODE) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x00) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_SEGMENT_REMAP | 0x01) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_COM_SCAN_DIR) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_COM_PINS) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x12) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_CONTRAST) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0xCF) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_PRECHARGE) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0xF1) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_VCOM_DETECT) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0x40) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_ENTIRE_DISPLAY_ON) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_NORMAL_DISPLAY) != HAL_OK) return HAL_ERROR;
    
    /* 清屏并开启显示 */
    DRV_OLED_Clear();
    DRV_OLED_Update();
    DRV_OLED_DisplayOn();
    
    return HAL_OK;
}

/**
 * @brief  清屏
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_Clear(void)
{
    memset(oled_buffer, 0, sizeof(oled_buffer));
    return HAL_OK;
}

/**
 * @brief  开启显示
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_DisplayOn(void)
{
    return OLED_WriteCommand(OLED_CMD_DISPLAY_ON);
}

/**
 * @brief  关闭显示
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_DisplayOff(void)
{
    return OLED_WriteCommand(OLED_CMD_DISPLAY_OFF);
}

/**
 * @brief  设置对比度
 * @param  contrast: 对比度值(0-255)
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_SetContrast(uint8_t contrast)
{
    if(OLED_WriteCommand(OLED_CMD_SET_CONTRAST) != HAL_OK) return HAL_ERROR;
    return OLED_WriteCommand(contrast);
}

/**
 * @brief  画点
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  color: 颜色(0=黑, 1=白)
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_DrawPixel(uint8_t x, uint8_t y, uint8_t color)
{
    if(x >= OLED_WIDTH || y >= OLED_HEIGHT) {
        return HAL_ERROR;
    }
    
    uint16_t pos = x + (y / 8) * OLED_WIDTH;
    uint8_t bit = y % 8;
    
    if(color) {
        oled_buffer[pos] |= (1 << bit);
    } else {
        oled_buffer[pos] &= ~(1 << bit);
    }
    
    return HAL_OK;
}

/**
 * @brief  显示字符串
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  str: 字符串
 * @param  font: 字体大小
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_DrawString(uint8_t x, uint8_t y, const char *str, OLED_FontSize_TypeDef font)
{
    uint8_t char_width = (font == OLED_FONT_6x8) ? 6 : 8;
    uint8_t pos_x = x;
    
    while(*str && pos_x < OLED_WIDTH) {
        uint8_t char_index = 0;
        
        /* 字符映射 */
        if(*str >= '0' && *str <= '9') {
            char_index = *str - '0' + 1;  // 数字0-9对应索引1-10
        } else if(*str >= 'A' && *str <= 'F') {
            char_index = *str - 'A' + 11; // 字母A-F对应索引11-16
        } else if(*str == '.') {
            char_index = 17;
        } else if(*str == '-') {
            char_index = 18;
        } else if(*str == ':') {
            char_index = 19;
        } else {
            char_index = 0;  // 空格
        }
        
        /* 绘制字符 */
        for(uint8_t i = 0; i < char_width && pos_x < OLED_WIDTH; i++) {
            uint8_t font_data = font_6x8[char_index][i];
            for(uint8_t j = 0; j < 8; j++) {
                if(font_data & (1 << j)) {
                    DRV_OLED_DrawPixel(pos_x, y + j, 1);
                }
            }
            pos_x++;
        }
        str++;
    }
    
    return HAL_OK;
}

/**
 * @brief  显示数字
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  num: 数字
 * @param  font: 字体大小
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_DrawNumber(uint8_t x, uint8_t y, int32_t num, OLED_FontSize_TypeDef font)
{
    char str[12];
    sprintf(str, "%d", (int)num);
    return DRV_OLED_DrawString(x, y, str, font);
}

/**
 * @brief  显示浮点数
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  num: 浮点数
 * @param  decimal: 小数位数
 * @param  font: 字体大小
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_DrawFloat(uint8_t x, uint8_t y, float num, uint8_t decimal, OLED_FontSize_TypeDef font)
{
    char str[16];
    sprintf(str, "%.*f", decimal, num);
    return DRV_OLED_DrawString(x, y, str, font);
}

/**
 * @brief  更新显示
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_OLED_Update(void)
{
    /* 设置显示区域 */
    if(OLED_WriteCommand(OLED_CMD_SET_COLUMN_ADDR) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_WIDTH - 1) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(OLED_CMD_SET_PAGE_ADDR) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand(0) != HAL_OK) return HAL_ERROR;
    if(OLED_WriteCommand((OLED_HEIGHT / 8) - 1) != HAL_OK) return HAL_ERROR;
    
    /* 发送显示数据 */
    return OLED_WriteData(oled_buffer, sizeof(oled_buffer));
}