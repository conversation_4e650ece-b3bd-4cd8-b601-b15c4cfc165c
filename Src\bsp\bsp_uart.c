#include "bsp/bsp_uart.h"

static UART_HandleTypeDef *g_huart = NULL;

HAL_StatusTypeDef BSP_UART_Init(UART_HandleTypeDef *huart)
{
    g_huart = huart;
    return HAL_OK;
}

HAL_StatusTypeDef BSP_UART_SendData(uint8_t *data, uint16_t size)
{
    if(g_huart == NULL) return HAL_ERROR;
    return HAL_UART_Transmit(g_huart, data, size, 1000);
}

HAL_StatusTypeDef BSP_UART_ReceiveData(uint8_t *data, uint16_t size, uint32_t timeout)
{
    if(g_huart == NULL) return HAL_ERROR;
    return HAL_UART_Receive(g_huart, data, size, timeout);
}




