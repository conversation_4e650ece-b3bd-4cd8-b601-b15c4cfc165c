/**
 * @file    drv_oled.h
 * @brief   OLED显示驱动头文件
 * <AUTHOR>
 * @date    2025-01-29
 */

#ifndef __DRV_OLED_H
#define __DRV_OLED_H

#include "main.h"

/* OLED显示参数 */
#define OLED_WIDTH      128
#define OLED_HEIGHT     64
#define OLED_PAGE_SIZE  8

/* OLED I2C地址 */
#define OLED_I2C_ADDR   0x78

/* OLED命令定义 */
#define OLED_CMD_DISPLAY_OFF        0xAE
#define OLED_CMD_DISPLAY_ON         0xAF
#define OLED_CMD_SET_CONTRAST       0x81
#define OLED_CMD_ENTIRE_DISPLAY_ON  0xA5
#define OLED_CMD_NORMAL_DISPLAY     0xA6
#define OLED_CMD_INVERSE_DISPLAY    0xA7
#define OLED_CMD_SET_MUX_RATIO      0xA8
#define OLED_CMD_SET_DISPLAY_OFFSET 0xD3
#define OLED_CMD_SET_START_LINE     0x40
#define OLED_CMD_SET_SEGMENT_REMAP  0xA1
#define OLED_CMD_SET_COM_SCAN_DIR   0xC8
#define OLED_CMD_SET_COM_PINS       0xDA
#define OLED_CMD_SET_CLOCK_DIV      0xD5
#define OLED_CMD_SET_PRECHARGE      0xD9
#define OLED_CMD_SET_VCOM_DETECT    0xDB
#define OLED_CMD_SET_CHARGE_PUMP    0x8D
#define OLED_CMD_SET_MEMORY_MODE    0x20
#define OLED_CMD_SET_COLUMN_ADDR    0x21
#define OLED_CMD_SET_PAGE_ADDR      0x22

/* 字体大小定义 */
typedef enum {
    OLED_FONT_6x8 = 0,
    OLED_FONT_8x16
} OLED_FontSize_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef DRV_OLED_Init(I2C_HandleTypeDef *hi2c);
HAL_StatusTypeDef DRV_OLED_Clear(void);
HAL_StatusTypeDef DRV_OLED_DisplayOn(void);
HAL_StatusTypeDef DRV_OLED_DisplayOff(void);
HAL_StatusTypeDef DRV_OLED_SetContrast(uint8_t contrast);
HAL_StatusTypeDef DRV_OLED_DrawPixel(uint8_t x, uint8_t y, uint8_t color);
HAL_StatusTypeDef DRV_OLED_DrawString(uint8_t x, uint8_t y, const char *str, OLED_FontSize_TypeDef font);
HAL_StatusTypeDef DRV_OLED_DrawNumber(uint8_t x, uint8_t y, int32_t num, OLED_FontSize_TypeDef font);
HAL_StatusTypeDef DRV_OLED_DrawFloat(uint8_t x, uint8_t y, float num, uint8_t decimal, OLED_FontSize_TypeDef font);
HAL_StatusTypeDef DRV_OLED_Update(void);

#endif /* __DRV_OLED_H */
