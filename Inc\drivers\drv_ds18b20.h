/**
 * @file    drv_ds18b20.h
 * @brief   DS18B20数字温度传感器驱动头文件
 * <AUTHOR>
 * @date    2025-01-28
 */

#ifndef __DRV_DS18B20_H
#define __DRV_DS18B20_H

#include "main.h"

/* DS18B20通道定义 */
typedef enum {
    DS18B20_CHANNEL1 = 0,    // PA2
    DS18B20_CHANNEL2 = 1     // PA4
} DS18B20_Channel_TypeDef;

/* DS18B20命令定义 */
#define DS18B20_CMD_SEARCH_ROM      0xF0
#define DS18B20_CMD_READ_ROM        0x33
#define DS18B20_CMD_MATCH_ROM       0x55
#define DS18B20_CMD_SKIP_ROM        0xCC
#define DS18B20_CMD_ALARM_SEARCH    0xEC
#define DS18B20_CMD_CONVERT_T       0x44
#define DS18B20_CMD_WRITE_SCRATCHPAD 0x4E
#define DS18B20_CMD_READ_SCRATCHPAD  0xBE
#define DS18B20_CMD_COPY_SCRATCHPAD  0x48
#define DS18B20_CMD_RECALL_E2       0xB8
#define DS18B20_CMD_READ_POWER      0xB4

/* DS18B20分辨率定义 */
typedef enum {
    DS18B20_RESOLUTION_9BIT = 0x1F,   // 9位，93.75ms转换时间
    DS18B20_RESOLUTION_10BIT = 0x3F,  // 10位，187.5ms转换时间
    DS18B20_RESOLUTION_11BIT = 0x5F,  // 11位，375ms转换时间
    DS18B20_RESOLUTION_12BIT = 0x7F   // 12位，750ms转换时间
} DS18B20_Resolution_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef DRV_DS18B20_Init(void);
HAL_StatusTypeDef DRV_DS18B20_StartConversion(DS18B20_Channel_TypeDef channel);
HAL_StatusTypeDef DRV_DS18B20_ReadTemp(DS18B20_Channel_TypeDef channel, float *temperature);
HAL_StatusTypeDef DRV_DS18B20_SetResolution(DS18B20_Channel_TypeDef channel, DS18B20_Resolution_TypeDef resolution);
uint8_t DRV_DS18B20_IsPresent(DS18B20_Channel_TypeDef channel);

#endif /* __DRV_DS18B20_H */
