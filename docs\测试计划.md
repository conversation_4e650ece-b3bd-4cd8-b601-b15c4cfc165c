# 多功能测试板测试计划

## 1. 概述
本文档旨在明确多功能测试板项目的测试目标、范围、策略、阶段、资源和验收标准，以确保最终产品符合项目需求和质量标准。

## 2. 测试目标
- 验证所有功能模块是否按照 [项目需求.md](mdc:docs/项目需求.md) 和 [详细设计.md](mdc:docs/详细设计.md) 实现。
- 验证串口通信协议是否符合 [通信协议.md](mdc:docs/通信协议.md) 的规定，包括命令解析和数据上报的正确性。
- 评估系统性能，包括响应时间、数据采集精度、采样频率等是否满足要求。
- 识别并记录所有缺陷，并跟踪其修复情况。
- 确保系统在各种操作条件下的稳定性和可靠性。

## 3. 测试范围
- **模块功能测试**：对每个独立的功能模块进行测试，如按键、LED、继电器、传感器驱动等。
- **通信协议测试**：验证串口通信协议的解析和发送功能。
- **功能集成测试**：测试多个模块协同工作时的功能和接口。
- **性能测试**：测试系统在不同负载和配置下的性能表现。
- **稳定性测试**：长时间运行测试，检查系统是否出现异常。
- **异常处理测试**：测试系统在输入非法数据、硬件故障等异常情况下的响应。

## 4. 测试策略
- **自底向上测试**：从底层驱动模块开始，逐步向上集成并测试。
- **黑盒测试**：主要基于项目需求和协议文档进行功能性测试，不关注内部实现细节。
- **白盒测试**：对关键模块和复杂算法进行代码级测试，确保逻辑正确性。
- **自动化测试**：未来可考虑开发上位机自动化测试工具，提高测试效率。
- **回归测试**：在代码修改或缺陷修复后，对受影响的功能进行回归测试，确保没有引入新的问题。

## 5. 测试阶段

### 5.1 单元测试
- **目的**：验证单个模块或函数的正确性。
- **执行者**：模块开发者。
- **内容**：针对每个C文件中的函数进行测试，模拟输入，检查输出和内部状态。
- **工具**：调试器，或通过主程序调用进行简单测试。

### 5.2 集成测试
- **目的**：验证多个模块协同工作时的功能和接口。
- **执行者**：模块开发者或测试人员。
- **内容**：测试按键与LED、串口通信与传感器数据上报等组合功能。
- **工具**：串口调试助手、示波器、万用表。

### 5.3 系统测试
- **目的**：全面验证系统是否满足 [项目需求.md](mdc:docs/项目需求.md) 中所有功能和性能指标。
- **执行者**：测试人员。
- **内容**：按照测试用例，从用户角度模拟实际使用场景，进行端到端测试。
- **工具**：串口调试助手、上位机（如果开发）、外部激励源、测量仪器。

### 5.4 验收测试
- **目的**：由项目负责人或最终用户确认产品是否符合验收标准。
- **执行者**：项目负责人/用户与开发团队。
- **内容**：根据 [项目需求.md](mdc:docs/项目需求.md) 和 [通信协议.md](mdc:docs/通信协议.md) 的关键功能进行验证。

## 6. 测试环境
- **硬件环境**：
  - 多功能测试板硬件V1.0
  - 外部电源、线缆
  - 调试器（ST-Link/J-Link）
  - 示波器、万用表等测量仪器
  - 热电偶、DS18B20、ADC输入信号源、负载等测试附件

- **软件环境**：
  - 开发工具链 (Keil/IAR/STM32CubeIDE)
  - 串口调试助手 (如：SSCOM, XCOM)
  - 上位机软件 (如果开发)
  - 版本控制工具 (Git)

## 7. 测试用例（示例）

### 7.1 按键功能测试
| 用例ID | 测试项       | 操作步骤                                  | 预期结果                                   |
|-------|--------------|------------------------------------------|---------------------------------------------|
| TC_KEY_001 | KEY1 短按 | 按下KEY1，持续20ms后释放                  | 串口上报KEY1状态，OLED显示按键按下事件    |
| TC_KEY_002 | KEY1 长按 | 按下KEY1，持续5秒后释放                   | 串口上报KEY1长按事件，OLED显示长按事件    |
| TC_KEY_003 | KEY消抖配置 | 通过串口协议配置KEY2消抖时间为100ms     | KEY2在按下100ms后才被识别为有效按下     |

### 7.2 继电器控制测试
| 用例ID | 测试项       | 操作步骤                                  | 预期结果                                     |
|-------|--------------|------------------------------------------|-----------------------------------------------|
| TC_RELAY_001 | PB5开关 | 通过串口协议控制PB5继电器打开             | 继电器吸合，万用表测量输出端通路             |
| TC_RELAY_002 | PB8定时 | 通过串口协议设置PB8继电器定时切换（5s开1s关） | PB8继电器每5秒吸合，持续1秒后释放，重复执行 |

### 7.3 串口通信测试
| 用例ID | 测试项       | 操作步骤                                  | 预期结果                                     |
|-------|--------------|------------------------------------------|-----------------------------------------------|
| TC_UART_001 | 命令解析 | 发送合法的按键配置协议帧                 | MCU正确解析命令，并更新按键消抖时间参数       |
| TC_UART_002 | 错误帧处理 | 发送非法协议头的数据帧                   | MCU上报协议格式错误帧                        |
| TC_UART_003 | 数据上报 | 触发热电偶数据采集                       | 串口正确上报热电偶温度数据，符合协议格式     |

## 8. 缺陷管理
- **缺陷报告**：发现缺陷后，应详细记录缺陷信息，包括重现步骤、实际结果、预期结果、影响范围和优先级。
- **缺陷跟踪**：使用合适的工具（如：Jira, Excel）跟踪缺陷从发现到修复关闭的整个生命周期。
- **缺陷修复验证**：缺陷修复后，进行验证性测试，确保缺陷已解决且未引入新的问题。

## 9. 验收标准
- 所有 [项目需求.md](mdc:docs/项目需求.md) 中定义的功能模块均通过测试，无严重缺陷。
- 关键性能指标（如响应时间、精度）满足 [项目需求.md](mdc:docs/项目需求.md) 中的要求。
- 串口通信协议完全符合 [通信协议.md](mdc:docs/通信协议.md) 的规定，且稳定可靠。
- 系统在长时间运行和异常条件下的表现稳定。
- 所有测试用例执行完毕，关键缺陷均已修复或有明确的处理方案。

## 10. 风险与应对
- **风险**：测试环境搭建困难，测试设备不足。
  **应对**：提前规划测试环境，采购或借用必要设备，或在虚拟环境中进行部分测试。
- **风险**：测试过程中发现大量缺陷，导致项目延期。
  **应对**：加强开发阶段的单元测试和代码审查，尽早发现并解决问题。
- **风险**：需求变更导致测试用例失效。
  **应对**：建立需求变更管理流程，及时更新测试计划和测试用例。 