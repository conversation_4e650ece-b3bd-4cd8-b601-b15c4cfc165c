# 多功能测试板项目计划

## 1. 项目概述
本项目旨在开发一款基于STM32L431RCT6的多功能测试板，用于研发团队内部测试嵌入式产品。项目将按照模块化方式进行开发，确保代码的可维护性和可扩展性。

## 2. 项目阶段划分

### 2.1 阶段一：基础框架搭建（预计1周）
- 项目初始化与配置
- 基础外设驱动开发
- 系统架构设计

### 2.2 阶段二：核心功能开发（预计2周）
- 按键和IO检测模块
- 继电器控制模块
- 串口通信协议实现

### 2.3 阶段三：传感器驱动开发（预计2周）
- 热电偶接口驱动
- DS18B20驱动
- ADC采集模块
- INA226电流监测模块

### 2.4 阶段四：高级功能与集成（预计1周）
- OLED显示模块
- 动态负载测试模块
- 系统集成与优化

### 2.5 阶段五：测试与完善（预计1周）
- 单元测试
- 集成测试
- 问题修复与优化

## 3. 模块划分

### 3.1 硬件抽象层（HAL）
- 基于STM32 HAL库，封装底层硬件操作
- 包括GPIO、UART、SPI、I2C、ADC、TIM等外设驱动

### 3.2 板级支持包（BSP）
- **BSP_LED**：LED控制模块
  - 系统运行指示灯控制
  - 调试指示灯控制
  
- **BSP_KEY**：按键处理模块
  - 按键扫描与消抖
  - 按键状态检测与上报
  
- **BSP_IO**：IO输入检测模块
  - 外部输入信号检测
  - 信号状态上报
  
- **BSP_RELAY**：继电器控制模块
  - 继电器开关控制
  - 定时切换功能

### 3.3 设备驱动层（Drivers）
- **DRV_MAX31856**：热电偶芯片驱动
  - SPI通信
  - 温度数据读取与处理
  - 故障检测
  
- **DRV_DS18B20**：温度传感器驱动
  - 单总线通信协议实现
  - 温度数据读取与处理
  
- **DRV_ADS1115**：外部ADC驱动
  - I2C通信
  - 多通道数据采集
  - 数据转换与处理
  
- **DRV_INA226**：电流监测芯片驱动
  - I2C通信
  - 电流、电压、功率数据采集
  
- **DRV_OLED**：OLED显示驱动
  - I2C通信
  - 显示控制与刷新

### 3.4 应用层（App）
- **APP_PROTOCOL**：通信协议处理
  - 串口数据接收与解析
  - 命令执行
  - 数据上报
  
- **APP_LOAD_TEST**：动态负载测试
  - PWM信号生成
  - 负载控制逻辑
  
- **APP_ADC**：ADC数据处理
  - 内部ADC采样控制
  - 数据处理与转换
  
- **APP_DISPLAY**：显示应用
  - 界面设计与显示
  - 数据更新

### 3.5 系统服务层（Service）
- **SRV_TIMER**：定时器服务
  - 提供软件定时器功能
  - 周期性任务调度
  
- **SRV_CONFIG**：配置管理
  - 系统参数存储与管理
  - 配置加载与保存

## 4. 开发优先级与依赖关系

### 4.1 优先级一（基础功能）
1. 系统初始化与时钟配置
2. LED控制模块
3. 串口通信基础功能
4. 按键检测模块

### 4.2 优先级二（核心功能）
1. 通信协议框架
2. 继电器控制模块
3. IO输入检测模块
4. 内部ADC采集模块

### 4.3 优先级三（传感器驱动）
1. MAX31856热电偶驱动
2. DS18B20温度传感器驱动
3. ADS1115外部ADC驱动
4. INA226电流监测驱动

### 4.4 优先级四（高级功能）
1. OLED显示模块
2. 动态负载测试模块
3. 系统集成与优化

## 5. 文件结构规划

```
testboard/
├── Inc/
│   ├── main.h                  - 主程序头文件
│   ├── bsp/                    - 板级支持包
│   │   ├── bsp_led.h
│   │   ├── bsp_key.h
│   │   ├── bsp_io.h
│   │   └── bsp_relay.h
│   ├── drivers/                - 设备驱动
│   │   ├── drv_max31856.h
│   │   ├── drv_ds18b20.h
│   │   ├── drv_ads1115.h
│   │   ├── drv_ina226.h
│   │   └── drv_oled.h
│   ├── app/                    - 应用层
│   │   ├── app_protocol.h
│   │   ├── app_load_test.h
│   │   ├── app_adc.h
│   │   └── app_display.h
│   └── service/                - 系统服务
│       ├── srv_timer.h
│       └── srv_config.h
├── Src/
│   ├── main.c                  - 主程序
│   ├── bsp/                    - 板级支持包实现
│   │   ├── bsp_led.c
│   │   ├── bsp_key.c
│   │   ├── bsp_io.c
│   │   └── bsp_relay.c
│   ├── drivers/                - 设备驱动实现
│   │   ├── drv_max31856.c
│   │   ├── drv_ds18b20.c
│   │   ├── drv_ads1115.c
│   │   ├── drv_ina226.c
│   │   └── drv_oled.c
│   ├── app/                    - 应用层实现
│   │   ├── app_protocol.c
│   │   ├── app_load_test.c
│   │   ├── app_adc.c
│   │   └── app_display.c
│   └── service/                - 系统服务实现
│       ├── srv_timer.c
│       └── srv_config.c
└── docs/                       - 项目文档
```

## 6. 里程碑计划

### 里程碑1：基础框架完成
- 完成系统初始化
- 完成LED控制模块
- 完成串口基础功能
- 实现按键检测基础功能

### 里程碑2：核心功能完成
- 完成通信协议框架
- 完成继电器控制模块
- 完成IO输入检测模块
- 完成内部ADC采集模块

### 里程碑3：传感器驱动完成
- 完成MAX31856热电偶驱动
- 完成DS18B20温度传感器驱动
- 完成ADS1115外部ADC驱动
- 完成INA226电流监测驱动

### 里程碑4：高级功能完成
- 完成OLED显示模块
- 完成动态负载测试模块
- 完成系统集成与优化

### 里程碑5：项目完成
- 完成所有功能测试
- 修复所有已知问题
- 完成文档编写
- 项目交付

## 7. 风险评估与应对措施

### 7.1 技术风险
- **风险**：部分芯片驱动开发难度较大
  **应对**：提前研究芯片手册，必要时寻求技术支持

- **风险**：多功能模块集成可能导致系统不稳定
  **应对**：采用模块化设计，确保各模块独立性，减少耦合

### 7.2 进度风险
- **风险**：某些模块开发可能超出预期时间
  **应对**：合理安排开发优先级，确保核心功能优先实现

- **风险**：测试阶段可能发现较多问题
  **应对**：在开发过程中进行单元测试，减少集成测试阶段的问题

### 7.3 资源风险
- **风险**：可能缺少某些硬件测试设备
  **应对**：提前准备必要的测试设备，或设计替代测试方案

## 8. 团队分工
待定 