Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32l431xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(RESET) refers to startup_stm32l431xx.o(STACK) for __initial_sp
    startup_stm32l431xx.o(RESET) refers to startup_stm32l431xx.o(.text) for Reset_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.NMI_Handler) for NMI_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.SVC_Handler) for SVC_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32l431xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(.text) refers to system_stm32l4xx_1.o(i.SystemInit) for SystemInit
    startup_stm32l431xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32l431xx.o(.text) refers to startup_stm32l431xx.o(HEAP) for Heap_Mem
    startup_stm32l431xx.o(.text) refers to startup_stm32l431xx.o(STACK) for Stack_Mem
    main_1.o(i.IOScanTask) refers to bsp_io.o(i.BSP_IO_Scan) for BSP_IO_Scan
    main_1.o(i.IOStateChangeCallback) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main_1.o(i.IOStateChangeCallback) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main_1.o(i.IOStateChangeCallback) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main_1.o(i.IOStateChangeCallback) refers to _printf_dec.o(.text) for _printf_int_dec
    main_1.o(i.IOStateChangeCallback) refers to _printf_str.o(.text) for _printf_str
    main_1.o(i.IOStateChangeCallback) refers to noretval__2sprintf.o(.text) for __2sprintf
    main_1.o(i.IOStateChangeCallback) refers to strlen.o(.text) for strlen
    main_1.o(i.IOStateChangeCallback) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    main_1.o(i.KeyScanTask) refers to bsp_key.o(i.BSP_KEY_Scan) for BSP_KEY_Scan
    main_1.o(i.MX_ADC1_Init) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    main_1.o(i.MX_ADC1_Init) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    main_1.o(i.MX_ADC1_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main_1.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main_1.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    main_1.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    main_1.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    main_1.o(i.MX_I2C1_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    main_1.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    main_1.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    main_1.o(i.MX_I2C2_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_LPUART1_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    main_1.o(i.MX_LPUART1_UART_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_SPI1_Init) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    main_1.o(i.MX_SPI1_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    main_1.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main_1.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main_1.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main_1.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Init) for HAL_Init
    main_1.o(i.main) refers to main_1.o(i.SystemClock_Config) for SystemClock_Config
    main_1.o(i.main) refers to main_1.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main_1.o(i.main) refers to main_1.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main_1.o(i.main) refers to main_1.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main_1.o(i.main) refers to main_1.o(i.MX_LPUART1_UART_Init) for MX_LPUART1_UART_Init
    main_1.o(i.main) refers to main_1.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main_1.o(i.main) refers to main_1.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Init) for SRV_TIMER_Init
    main_1.o(i.main) refers to srv_system.o(i.SRV_SYSTEM_Init) for SRV_SYSTEM_Init
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_Init) for BSP_LED_Init
    main_1.o(i.main) refers to bsp_uart.o(i.BSP_UART_Init) for BSP_UART_Init
    main_1.o(i.main) refers to bsp_key.o(i.BSP_KEY_Init) for BSP_KEY_Init
    main_1.o(i.main) refers to bsp_io.o(i.BSP_IO_Init) for BSP_IO_Init
    main_1.o(i.main) refers to bsp_relay.o(i.BSP_RELAY_Init) for BSP_RELAY_Init
    main_1.o(i.main) refers to app_protocol.o(i.APP_PROTOCOL_Init) for APP_PROTOCOL_Init
    main_1.o(i.main) refers to bsp_io.o(i.BSP_IO_RegisterCallback) for BSP_IO_RegisterCallback
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Create) for SRV_TIMER_Create
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Start) for SRV_TIMER_Start
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_SetBlink) for BSP_LED_SetBlink
    main_1.o(i.main) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main_1.o(i.main) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    main_1.o(i.main) refers to drv_max31856.o(i.DRV_MAX31856_Init) for DRV_MAX31856_Init
    main_1.o(i.main) refers to drv_ds18b20.o(i.DRV_DS18B20_Init) for DRV_DS18B20_Init
    main_1.o(i.main) refers to drv_ads1115.o(i.DRV_ADS1115_Init) for DRV_ADS1115_Init
    main_1.o(i.main) refers to drv_ina226.o(i.DRV_INA226_Init) for DRV_INA226_Init
    main_1.o(i.main) refers to app_adc.o(i.APP_ADC_Init) for APP_ADC_Init
    main_1.o(i.main) refers to app_display.o(i.APP_DISPLAY_Init) for APP_DISPLAY_Init
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Process) for SRV_TIMER_Process
    main_1.o(i.main) refers to srv_system.o(i.SRV_SYSTEM_Process) for SRV_SYSTEM_Process
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_Process) for BSP_LED_Process
    main_1.o(i.main) refers to bsp_relay.o(i.BSP_RELAY_Process) for BSP_RELAY_Process
    main_1.o(i.main) refers to app_protocol.o(i.APP_PROTOCOL_Process) for APP_PROTOCOL_Process
    main_1.o(i.main) refers to app_adc.o(i.APP_ADC_Process) for APP_ADC_Process
    main_1.o(i.main) refers to app_display.o(i.APP_DISPLAY_Process) for APP_DISPLAY_Process
    main_1.o(i.main) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main_1.o(i.main) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main_1.o(i.main) refers to strcpy.o(.text) for strcpy
    main_1.o(i.main) refers to bsp_key.o(i.BSP_KEY_GetState) for BSP_KEY_GetState
    main_1.o(i.main) refers to bsp_io.o(i.BSP_IO_GetState) for BSP_IO_GetState
    main_1.o(i.main) refers to bsp_relay.o(i.BSP_RELAY_GetState) for BSP_RELAY_GetState
    main_1.o(i.main) refers to app_display.o(i.APP_DISPLAY_UpdateData) for APP_DISPLAY_UpdateData
    main_1.o(i.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    main_1.o(i.main) refers to strlen.o(.text) for strlen
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_Toggle) for BSP_LED_Toggle
    main_1.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main_1.o(i.main) refers to main_1.o(.bss) for .bss
    main_1.o(i.main) refers to main_1.o(i.IOStateChangeCallback) for IOStateChangeCallback
    main_1.o(i.main) refers to main_1.o(i.KeyScanTask) for KeyScanTask
    main_1.o(i.main) refers to main_1.o(i.IOScanTask) for IOScanTask
    main_1.o(i.main) refers to main_1.o(.data) for .data
    stm32l4xx_it_1.o(i.EXTI3_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it_1.o(i.EXTI4_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it_1.o(i.SysTick_Handler) refers to stm32l4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32l4xx_it_1.o(i.SysTick_Handler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) for HAL_SYSTICK_IRQHandler
    stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp_1.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal_msp_1.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp_1.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAError) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32l4xx_hal_adc.o(i.ADC_Disable) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_Enable) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_Enable) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_ConfigAnalogWDThresholds) for LL_ADC_ConfigAnalogWDThresholds
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal.o(i.HAL_DeInit) refers to stm32l4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_GetTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_IncTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_MspInit) for HAL_MspInit
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal.o(i.HAL_InitTick) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) for I2C_ITMasterSequentialCplt
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) for I2C_ITSlaveSequentialCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx_1.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx_1.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx_1.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l4xx_1.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) for RCC_SetFlashLatencyFromMSIRange
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx_1.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange) for HAL_PWREx_GetVoltageRange
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to system_stm32l4xx_1.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) for RCCEx_PLLSAI1_Config
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32l4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization) refers to stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT) refers to stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAError) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32l4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback) for HAL_TIMEx_CommutationCallback
    system_stm32l4xx_1.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx_1.o(.constdata) for .constdata
    system_stm32l4xx_1.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx_1.o(.data) for .data
    bsp_io.o(i.BSP_IO_GetState) refers to bsp_io.o(.data) for .data
    bsp_io.o(i.BSP_IO_RegisterCallback) refers to bsp_io.o(.data) for .data
    bsp_io.o(i.BSP_IO_Scan) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bsp_io.o(i.BSP_IO_Scan) refers to bsp_io.o(.data) for .data
    bsp_key.o(i.BSP_KEY_GetState) refers to bsp_key.o(.data) for .data
    bsp_key.o(i.BSP_KEY_Scan) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bsp_key.o(i.BSP_KEY_Scan) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_key.o(i.BSP_KEY_Scan) refers to bsp_key.o(.data) for .data
    bsp_led.o(i.BSP_LED_Off) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_led.o(i.BSP_LED_Off) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_On) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_led.o(i.BSP_LED_On) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_led.o(i.BSP_LED_Process) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    bsp_led.o(i.BSP_LED_Process) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_SetBlink) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_led.o(i.BSP_LED_SetBlink) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_Toggle) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    bsp_led.o(i.BSP_LED_Toggle) refers to bsp_led.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_GetState) refers to bsp_relay.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_Off) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_relay.o(i.BSP_RELAY_Off) refers to bsp_relay.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_On) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_relay.o(i.BSP_RELAY_On) refers to bsp_relay.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_SetState) refers to bsp_relay.o(i.BSP_RELAY_Off) for BSP_RELAY_Off
    bsp_relay.o(i.BSP_RELAY_SetState) refers to bsp_relay.o(i.BSP_RELAY_On) for BSP_RELAY_On
    bsp_relay.o(i.BSP_RELAY_Toggle) refers to bsp_relay.o(i.BSP_RELAY_On) for BSP_RELAY_On
    bsp_relay.o(i.BSP_RELAY_Toggle) refers to bsp_relay.o(i.BSP_RELAY_Off) for BSP_RELAY_Off
    bsp_relay.o(i.BSP_RELAY_Toggle) refers to bsp_relay.o(.data) for .data
    bsp_uart.o(i.BSP_UART_Init) refers to bsp_uart.o(.data) for .data
    bsp_uart.o(i.BSP_UART_ReceiveData) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    bsp_uart.o(i.BSP_UART_ReceiveData) refers to bsp_uart.o(.data) for .data
    bsp_uart.o(i.BSP_UART_SendData) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    bsp_uart.o(i.BSP_UART_SendData) refers to bsp_uart.o(.data) for .data
    app_adc.o(i.APP_ADC_Init) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    app_adc.o(i.APP_ADC_Init) refers to app_adc.o(.data) for .data
    app_adc.o(i.APP_ADC_ReadVoltage) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    app_adc.o(i.APP_ADC_ReadVoltage) refers to app_adc.o(.data) for .data
    app_display.o(i.APP_DISPLAY_Init) refers to drv_oled.o(i.DRV_OLED_Init) for DRV_OLED_Init
    app_display.o(i.APP_DISPLAY_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_display.o(i.APP_DISPLAY_Init) refers to strcpy.o(.text) for strcpy
    app_display.o(i.APP_DISPLAY_Init) refers to app_display.o(i.APP_DISPLAY_ShowRunningStatus) for APP_DISPLAY_ShowRunningStatus
    app_display.o(i.APP_DISPLAY_Init) refers to app_display.o(.bss) for .bss
    app_display.o(i.APP_DISPLAY_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(i.DisplayMainPage) for DisplayMainPage
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(i.DisplaySensorPage) for DisplaySensorPage
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(i.DisplayStatusPage) for DisplayStatusPage
    app_display.o(i.APP_DISPLAY_Process) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(.data) for .data
    app_display.o(i.APP_DISPLAY_SetPage) refers to app_display.o(.data) for .data
    app_display.o(i.APP_DISPLAY_ShowMessage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.APP_DISPLAY_ShowMessage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.APP_DISPLAY_ShowMessage) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    app_display.o(i.APP_DISPLAY_ShowRunningStatus) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.APP_DISPLAY_ShowRunningStatus) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.APP_DISPLAY_ShowRunningStatus) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    app_display.o(i.APP_DISPLAY_UpdateData) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_display.o(i.APP_DISPLAY_UpdateData) refers to app_display.o(.bss) for .bss
    app_display.o(i.APP_DISPLAY_UpdateData) refers to app_display.o(.data) for .data
    app_display.o(i.DisplayMainPage) refers to _printf_pad.o(.text) for _printf_pre_padding
    app_display.o(i.DisplayMainPage) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    app_display.o(i.DisplayMainPage) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    app_display.o(i.DisplayMainPage) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    app_display.o(i.DisplayMainPage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.DisplayMainPage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.DisplayMainPage) refers to noretval__2sprintf.o(.text) for __2sprintf
    app_display.o(i.DisplayMainPage) refers to drv_oled.o(i.DRV_OLED_DrawFloat) for DRV_OLED_DrawFloat
    app_display.o(i.DisplayMainPage) refers to app_display.o(.bss) for .bss
    app_display.o(i.DisplaySensorPage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.DisplaySensorPage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.DisplaySensorPage) refers to drv_oled.o(i.DRV_OLED_DrawFloat) for DRV_OLED_DrawFloat
    app_display.o(i.DisplaySensorPage) refers to app_display.o(.bss) for .bss
    app_display.o(i.DisplayStatusPage) refers to _printf_pad.o(.text) for _printf_pre_padding
    app_display.o(i.DisplayStatusPage) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    app_display.o(i.DisplayStatusPage) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    app_display.o(i.DisplayStatusPage) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    app_display.o(i.DisplayStatusPage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.DisplayStatusPage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.DisplayStatusPage) refers to noretval__2sprintf.o(.text) for __2sprintf
    app_display.o(i.DisplayStatusPage) refers to drv_oled.o(i.DRV_OLED_DrawFloat) for DRV_OLED_DrawFloat
    app_display.o(i.DisplayStatusPage) refers to app_display.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_GetStatus) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(i.APP_LOAD_TEST_Stop) for APP_LOAD_TEST_Stop
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_SetMode) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_SetMode) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_SetParams) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_SetParams) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_Start) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Start) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Stop) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Stop) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.SetPWMDuty) refers to app_load_test.o(.data) for .data
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.APP_PROTOCOL_SendResponse) for APP_PROTOCOL_SendResponse
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_Off) for BSP_LED_Off
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_On) for BSP_LED_On
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_Toggle) for BSP_LED_Toggle
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_SetBlink) for BSP_LED_SetBlink
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.APP_PROTOCOL_SendError) for APP_PROTOCOL_SendError
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_key.o(i.BSP_KEY_GetState) for BSP_KEY_GetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_io.o(i.BSP_IO_GetState) for BSP_IO_GetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_io.o(i.BSP_IO_SetDebounceThreshold) for BSP_IO_SetDebounceThreshold
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_SetState) for BSP_RELAY_SetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_Toggle) for BSP_RELAY_Toggle
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_SetToggleMode) for BSP_RELAY_SetToggleMode
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_SetPulseMode) for BSP_RELAY_SetPulseMode
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_GetState) for BSP_RELAY_GetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessTempMeasure) for ProcessTempMeasure
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessADCMeasure) for ProcessADCMeasure
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessCurrentMeasure) for ProcessCurrentMeasure
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessDisplayControl) for ProcessDisplayControl
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessLoadTest) for ProcessLoadTest
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessSystemStatus) for ProcessSystemStatus
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(.constdata) for .constdata
    app_protocol.o(i.APP_PROTOCOL_Init) refers to app_protocol.o(.data) for .data
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(i.APP_PROTOCOL_HandleFrame) for APP_PROTOCOL_HandleFrame
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(i.APP_PROTOCOL_SendError) for APP_PROTOCOL_SendError
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(.data) for .data
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(.bss) for .bss
    app_protocol.o(i.APP_PROTOCOL_SendError) refers to app_protocol.o(i.APP_PROTOCOL_SendResponse) for APP_PROTOCOL_SendResponse
    app_protocol.o(i.APP_PROTOCOL_SendResponse) refers to app_protocol.o(i.APP_PROTOCOL_CalculateChecksum) for APP_PROTOCOL_CalculateChecksum
    app_protocol.o(i.APP_PROTOCOL_SendResponse) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    app_protocol.o(i.APP_PROTOCOL_SendResponse) refers to app_protocol.o(.data) for .data
    app_protocol.o(i.ProcessADCMeasure) refers to drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) for DRV_ADS1115_ReadVoltage
    app_protocol.o(i.ProcessADCMeasure) refers to app_adc.o(i.APP_ADC_ReadVoltage) for APP_ADC_ReadVoltage
    app_protocol.o(i.ProcessADCMeasure) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessCurrentMeasure) refers to drv_ina226.o(i.DRV_INA226_ReadVoltage) for DRV_INA226_ReadVoltage
    app_protocol.o(i.ProcessCurrentMeasure) refers to drv_ina226.o(i.DRV_INA226_ReadCurrent) for DRV_INA226_ReadCurrent
    app_protocol.o(i.ProcessCurrentMeasure) refers to drv_ina226.o(i.DRV_INA226_ReadPower) for DRV_INA226_ReadPower
    app_protocol.o(i.ProcessCurrentMeasure) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessDisplayControl) refers to app_display.o(i.APP_DISPLAY_SetPage) for APP_DISPLAY_SetPage
    app_protocol.o(i.ProcessDisplayControl) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    app_protocol.o(i.ProcessDisplayControl) refers to app_display.o(i.APP_DISPLAY_ShowMessage) for APP_DISPLAY_ShowMessage
    app_protocol.o(i.ProcessDisplayControl) refers to drv_oled.o(i.DRV_OLED_DisplayOn) for DRV_OLED_DisplayOn
    app_protocol.o(i.ProcessDisplayControl) refers to drv_oled.o(i.DRV_OLED_DisplayOff) for DRV_OLED_DisplayOff
    app_protocol.o(i.ProcessDisplayControl) refers to drv_oled.o(i.DRV_OLED_SetContrast) for DRV_OLED_SetContrast
    app_protocol.o(i.ProcessDisplayControl) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_SetMode) for APP_LOAD_TEST_SetMode
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_SetParams) for APP_LOAD_TEST_SetParams
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_Start) for APP_LOAD_TEST_Start
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_Stop) for APP_LOAD_TEST_Stop
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_GetStatus) for APP_LOAD_TEST_GetStatus
    app_protocol.o(i.ProcessLoadTest) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessSystemStatus) refers to srv_system.o(i.SRV_SYSTEM_GetStatus) for SRV_SYSTEM_GetStatus
    app_protocol.o(i.ProcessSystemStatus) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessTempMeasure) refers to drv_max31856.o(i.DRV_MAX31856_ReadTemp) for DRV_MAX31856_ReadTemp
    app_protocol.o(i.ProcessTempMeasure) refers to drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) for DRV_DS18B20_ReadTemp
    app_protocol.o(i.ProcessTempMeasure) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.SendResponse) refers to app_protocol.o(i.APP_PROTOCOL_CalculateChecksum) for APP_PROTOCOL_CalculateChecksum
    app_protocol.o(i.SendResponse) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    app_protocol.o(i.SendResponse) refers to app_protocol.o(.data) for .data
    drv_ads1115.o(i.ADS1115_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ads1115.o(i.ADS1115_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    drv_ads1115.o(i.ADS1115_ReadRegister) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_Init) refers to drv_ads1115.o(i.ADS1115_ReadRegister) for ADS1115_ReadRegister
    drv_ads1115.o(i.DRV_ADS1115_Init) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_IsReady) refers to drv_ads1115.o(i.ADS1115_ReadRegister) for ADS1115_ReadRegister
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to drv_ads1115.o(i.ADS1115_ReadRegister) for ADS1115_ReadRegister
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) refers to drv_ads1115.o(i.DRV_ADS1115_ReadChannel) for DRV_ADS1115_ReadChannel
    drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) refers to drv_ads1115.o(.constdata) for .constdata
    drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_SetDataRate) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_SetGain) refers to drv_ads1115.o(.data) for .data
    drv_ds18b20.o(i.DRV_DS18B20_Init) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DRV_DS18B20_Init) refers to drv_ds18b20.o(i.DRV_DS18B20_IsPresent) for DRV_DS18B20_IsPresent
    drv_ds18b20.o(i.DRV_DS18B20_Init) refers to drv_ds18b20.o(i.DRV_DS18B20_SetResolution) for DRV_DS18B20_SetResolution
    drv_ds18b20.o(i.DRV_DS18B20_IsPresent) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_WriteByte) for DS18B20_WriteByte
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_SetPinOutput) for DS18B20_SetPinOutput
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_WritePin) for DS18B20_WritePin
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_DelayUs) for DS18B20_DelayUs
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_ReadPin) for DS18B20_ReadPin
    drv_ds18b20.o(i.DRV_DS18B20_SetResolution) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_SetResolution) refers to drv_ds18b20.o(i.DS18B20_WriteByte) for DS18B20_WriteByte
    drv_ds18b20.o(i.DRV_DS18B20_StartConversion) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_StartConversion) refers to drv_ds18b20.o(i.DS18B20_WriteByte) for DS18B20_WriteByte
    drv_ds18b20.o(i.DS18B20_DelayUs) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    drv_ds18b20.o(i.DS18B20_ReadPin) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    drv_ds18b20.o(i.DS18B20_ReadPin) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_SetPinOutput) for DS18B20_SetPinOutput
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_WritePin) for DS18B20_WritePin
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_DelayUs) for DS18B20_DelayUs
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_ReadPin) for DS18B20_ReadPin
    drv_ds18b20.o(i.DS18B20_SetPinInput) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_ds18b20.o(i.DS18B20_SetPinInput) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    drv_ds18b20.o(i.DS18B20_SetPinInput) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ds18b20.o(i.DS18B20_SetPinOutput) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_ds18b20.o(i.DS18B20_SetPinOutput) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    drv_ds18b20.o(i.DS18B20_SetPinOutput) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_SetPinOutput) for DS18B20_SetPinOutput
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_WritePin) for DS18B20_WritePin
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_DelayUs) for DS18B20_DelayUs
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DS18B20_WritePin) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    drv_ds18b20.o(i.DS18B20_WritePin) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(i.INA226_WriteRegister) for INA226_WriteRegister
    drv_ina226.o(i.DRV_INA226_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(i.DRV_INA226_SetCalibration) for DRV_INA226_SetCalibration
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.DRV_INA226_IsReady) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadCurrent) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadCurrent) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.DRV_INA226_ReadPower) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadPower) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.DRV_INA226_ReadShuntVoltage) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadVoltage) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_SetCalibration) refers to drv_ina226.o(i.INA226_WriteRegister) for INA226_WriteRegister
    drv_ina226.o(i.DRV_INA226_SetCalibration) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.INA226_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ina226.o(i.INA226_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    drv_ina226.o(i.INA226_ReadRegister) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.INA226_WriteRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ina226.o(i.INA226_WriteRegister) refers to drv_ina226.o(.data) for .data
    drv_max31856.o(i.DRV_MAX31856_ClearFault) refers to drv_max31856.o(i.MAX31856_ReadRegister) for MAX31856_ReadRegister
    drv_max31856.o(i.DRV_MAX31856_ClearFault) refers to drv_max31856.o(i.MAX31856_WriteRegister) for MAX31856_WriteRegister
    drv_max31856.o(i.DRV_MAX31856_Config) refers to drv_max31856.o(i.MAX31856_WriteRegister) for MAX31856_WriteRegister
    drv_max31856.o(i.DRV_MAX31856_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_max31856.o(i.DRV_MAX31856_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    drv_max31856.o(i.DRV_MAX31856_Init) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.DRV_MAX31856_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    drv_max31856.o(i.DRV_MAX31856_Init) refers to drv_max31856.o(i.DRV_MAX31856_Config) for DRV_MAX31856_Config
    drv_max31856.o(i.DRV_MAX31856_Init) refers to drv_max31856.o(.data) for .data
    drv_max31856.o(i.DRV_MAX31856_ReadColdJunctionTemp) refers to drv_max31856.o(i.MAX31856_ReadMultipleRegisters) for MAX31856_ReadMultipleRegisters
    drv_max31856.o(i.DRV_MAX31856_ReadFault) refers to drv_max31856.o(i.MAX31856_ReadRegister) for MAX31856_ReadRegister
    drv_max31856.o(i.DRV_MAX31856_ReadTemp) refers to drv_max31856.o(i.MAX31856_ReadMultipleRegisters) for MAX31856_ReadMultipleRegisters
    drv_max31856.o(i.MAX31856_CS_Control) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    drv_max31856.o(i.MAX31856_CS_Control) refers to drv_max31856.o(.constdata) for .constdata
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to drv_max31856.o(.data) for .data
    drv_max31856.o(i.MAX31856_ReadRegister) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.MAX31856_ReadRegister) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    drv_max31856.o(i.MAX31856_ReadRegister) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    drv_max31856.o(i.MAX31856_ReadRegister) refers to drv_max31856.o(.data) for .data
    drv_max31856.o(i.MAX31856_WriteRegister) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.MAX31856_WriteRegister) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    drv_max31856.o(i.MAX31856_WriteRegister) refers to drv_max31856.o(.data) for .data
    drv_oled.o(i.DRV_OLED_Clear) refers to rt_memclr.o(.text) for __aeabi_memclr
    drv_oled.o(i.DRV_OLED_Clear) refers to drv_oled.o(.bss) for .bss
    drv_oled.o(i.DRV_OLED_DisplayOff) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_DisplayOn) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to noretval__2sprintf.o(.text) for __2sprintf
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to _printf_dec.o(.text) for _printf_int_dec
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to noretval__2sprintf.o(.text) for __2sprintf
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    drv_oled.o(i.DRV_OLED_DrawPixel) refers to drv_oled.o(.bss) for .bss
    drv_oled.o(i.DRV_OLED_DrawString) refers to drv_oled.o(i.DRV_OLED_DrawPixel) for DRV_OLED_DrawPixel
    drv_oled.o(i.DRV_OLED_DrawString) refers to drv_oled.o(.constdata) for .constdata
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(.data) for .data
    drv_oled.o(i.DRV_OLED_SetContrast) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_Update) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_Update) refers to h1_alloc.o(.text) for malloc
    drv_oled.o(i.DRV_OLED_Update) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    drv_oled.o(i.DRV_OLED_Update) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_oled.o(i.DRV_OLED_Update) refers to h1_free.o(.text) for free
    drv_oled.o(i.DRV_OLED_Update) refers to drv_oled.o(.bss) for .bss
    drv_oled.o(i.DRV_OLED_Update) refers to drv_oled.o(.data) for .data
    drv_oled.o(i.OLED_WriteCommand) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_oled.o(i.OLED_WriteCommand) refers to drv_oled.o(.data) for .data
    srv_system.o(i.SRV_SYSTEM_GetStatus) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_Init) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_system.o(i.SRV_SYSTEM_Init) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_Init) refers to srv_system.o(.data) for .data
    srv_system.o(i.SRV_SYSTEM_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_system.o(i.SRV_SYSTEM_Process) refers to srv_system.o(.data) for .data
    srv_system.o(i.SRV_SYSTEM_Process) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_ReportError) refers to bsp_led.o(i.BSP_LED_SetBlink) for BSP_LED_SetBlink
    srv_system.o(i.SRV_SYSTEM_ReportError) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_SelfCheck) refers to srv_system.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Create) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_timer.o(i.SRV_TIMER_Create) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Create) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Init) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Init) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_timer.o(i.SRV_TIMER_Process) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Process) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Start) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_timer.o(i.SRV_TIMER_Start) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Start) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Stop) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Stop) refers to srv_timer.o(.bss) for .bss
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main_1.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32l431xx.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing main_1.o(.rev16_text), (4 bytes).
    Removing main_1.o(.revsh_text), (4 bytes).
    Removing main_1.o(.rrx_text), (6 bytes).
    Removing main_1.o(i._Error_Handler), (2 bytes).
    Removing stm32l4xx_it_1.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_it_1.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_it_1.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp_1.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_msp_1.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_msp_1.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspDeInit), (40 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_UART_MspDeInit), (40 bytes).
    Removing stm32l4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_ConversionStop), (192 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt), (104 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_Enable), (152 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (550 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit), (364 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (486 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (164 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (190 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start), (136 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (196 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT), (232 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (116 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32l4xx_hal_adc.o(i.LL_ADC_ConfigAnalogWDThresholds), (40 bytes).
    Removing stm32l4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (56 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (28 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (108 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (26 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (34 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (34 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (34 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1212 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (46 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (160 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (142 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (200 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (80 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (90 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (92 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (138 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (102 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (12 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (8 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime), (70 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState), (16 bytes).
    Removing stm32l4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DeInit), (44 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_SRAM2Erase), (28 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32l4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (98 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (294 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (244 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT), (128 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT), (124 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (248 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (374 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (270 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (210 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (366 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (266 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (206 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (318 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (92 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (320 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (156 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (92 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAAbort), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAError), (18 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (66 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (66 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ), (88 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITError), (224 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt), (92 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt), (180 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt), (76 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt), (240 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt), (84 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (196 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT), (288 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (112 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (112 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (106 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (272 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit), (212 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (228 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (132 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (56 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (204 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (916 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_StandbyMSIRangeConfig), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32l4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_Fast), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode), (204 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (136 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (284 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program), (170 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (116 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32l4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase), (28 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (104 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (248 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase), (48 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (226 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (148 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (140 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (278 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (246 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32l4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Abort), (54 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit), (140 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (170 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Init), (216 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (202 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (68 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (212 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (88 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (84 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (64 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (128 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (128 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP2Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (60 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32l4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (32 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (32 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (84 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32l4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (58 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (58 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (44 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (44 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort), (124 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive), (86 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (124 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (58 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (88 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT), (196 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAPause), (98 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAResume), (92 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop), (84 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DeInit), (64 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler), (332 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive), (238 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (148 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT), (172 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (136 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (104 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError), (20 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (38 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt), (48 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback), (52 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (22 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_EndRxTransfer), (32 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_EndTxTransfer), (18 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT), (92 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT), (90 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT), (66 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT), (60 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (138 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort), (352 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (348 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop), (40 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (216 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive), (358 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (320 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (192 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (560 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (424 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (200 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (252 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (148 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (54 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (70 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR), (128 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR), (124 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (82 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR), (64 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR), (76 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError), (18 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (86 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (128 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (124 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction), (116 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT), (36 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT), (30 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi_ex.o(i.HAL_SPIEx_FlushRxFifo), (36 bytes).
    Removing stm32l4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Init), (54 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start), (26 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (92 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (24 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (44 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (48 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (42 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (248 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (258 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (260 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (96 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (260 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (96 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (156 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (50 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (272 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (92 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (68 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (116 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (116 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (284 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Init), (54 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start), (26 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (200 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (76 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (44 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (100 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (94 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (358 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (114 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Init), (54 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start), (68 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (248 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (116 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (96 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (152 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (148 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (224 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (74 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (68 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (88 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (108 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (128 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (296 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (54 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (68 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (248 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (116 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (104 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (152 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (148 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (68 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization), (66 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchronization_IT), (66 bytes).
    Removing stm32l4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_Base_SetConfig), (108 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_CCxChannelCmd), (22 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMACaptureCplt), (62 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (62 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAError), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_DMATriggerCplt), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC1_SetConfig), (120 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC2_SetConfig), (120 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC3_SetConfig), (116 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC4_SetConfig), (96 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC5_SetConfig), (92 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_OC6_SetConfig), (92 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (128 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI1_SetConfig), (80 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32l4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutationCallback), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (136 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (156 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent), (92 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_DMA), (124 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutationEvent_IT), (102 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (192 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (28 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (104 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (38 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (46 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (56 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (76 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (36 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (220 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (96 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (68 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (124 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (134 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (26 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (46 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (68 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (88 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (36 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (220 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (96 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (68 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (124 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (134 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (76 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32l4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (22 bytes).
    Removing system_stm32l4xx_1.o(.rev16_text), (4 bytes).
    Removing system_stm32l4xx_1.o(.revsh_text), (4 bytes).
    Removing system_stm32l4xx_1.o(.rrx_text), (6 bytes).
    Removing system_stm32l4xx_1.o(i.SystemCoreClockUpdate), (168 bytes).
    Removing bsp_io.o(.rev16_text), (4 bytes).
    Removing bsp_io.o(.revsh_text), (4 bytes).
    Removing bsp_io.o(.rrx_text), (6 bytes).
    Removing bsp_io.o(i.BSP_IO_SetDebounceThreshold), (2 bytes).
    Removing bsp_key.o(.rev16_text), (4 bytes).
    Removing bsp_key.o(.revsh_text), (4 bytes).
    Removing bsp_key.o(.rrx_text), (6 bytes).
    Removing bsp_led.o(.rev16_text), (4 bytes).
    Removing bsp_led.o(.revsh_text), (4 bytes).
    Removing bsp_led.o(.rrx_text), (6 bytes).
    Removing bsp_led.o(i.BSP_LED_Off), (32 bytes).
    Removing bsp_led.o(i.BSP_LED_On), (32 bytes).
    Removing bsp_relay.o(.rev16_text), (4 bytes).
    Removing bsp_relay.o(.revsh_text), (4 bytes).
    Removing bsp_relay.o(.rrx_text), (6 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_Off), (36 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_On), (36 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_SetPulseMode), (2 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_SetState), (18 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_SetToggleMode), (2 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_Toggle), (32 bytes).
    Removing bsp_uart.o(.rev16_text), (4 bytes).
    Removing bsp_uart.o(.revsh_text), (4 bytes).
    Removing bsp_uart.o(.rrx_text), (6 bytes).
    Removing bsp_uart.o(i.BSP_UART_ReceiveData), (32 bytes).
    Removing app_adc.o(.rev16_text), (4 bytes).
    Removing app_adc.o(.revsh_text), (4 bytes).
    Removing app_adc.o(.rrx_text), (6 bytes).
    Removing app_adc.o(i.APP_ADC_ReadVoltage), (164 bytes).
    Removing app_display.o(.rev16_text), (4 bytes).
    Removing app_display.o(.revsh_text), (4 bytes).
    Removing app_display.o(.rrx_text), (6 bytes).
    Removing app_display.o(i.APP_DISPLAY_SetPage), (24 bytes).
    Removing app_display.o(i.APP_DISPLAY_ShowMessage), (52 bytes).
    Removing app_load_test.o(.rev16_text), (4 bytes).
    Removing app_load_test.o(.revsh_text), (4 bytes).
    Removing app_load_test.o(.rrx_text), (6 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_GetStatus), (16 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Init), (60 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Process), (316 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_SetMode), (28 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_SetParams), (76 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Start), (36 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Stop), (20 bytes).
    Removing app_load_test.o(i.SetPWMDuty), (28 bytes).
    Removing app_load_test.o(.bss), (12 bytes).
    Removing app_load_test.o(.data), (24 bytes).
    Removing app_protocol.o(.rev16_text), (4 bytes).
    Removing app_protocol.o(.revsh_text), (4 bytes).
    Removing app_protocol.o(.rrx_text), (6 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_CalculateChecksum), (26 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_HandleFrame), (576 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_RxCallback), (172 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_SendError), (22 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_SendResponse), (96 bytes).
    Removing app_protocol.o(i.ProcessADCMeasure), (136 bytes).
    Removing app_protocol.o(i.ProcessCurrentMeasure), (168 bytes).
    Removing app_protocol.o(i.ProcessDisplayControl), (158 bytes).
    Removing app_protocol.o(i.ProcessLoadTest), (254 bytes).
    Removing app_protocol.o(i.ProcessSystemStatus), (106 bytes).
    Removing app_protocol.o(i.ProcessTempMeasure), (128 bytes).
    Removing app_protocol.o(i.SendResponse), (112 bytes).
    Removing app_protocol.o(.bss), (66 bytes).
    Removing app_protocol.o(.constdata), (17 bytes).
    Removing drv_ads1115.o(.rev16_text), (4 bytes).
    Removing drv_ads1115.o(.revsh_text), (4 bytes).
    Removing drv_ads1115.o(.rrx_text), (6 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_IsReady), (24 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_ReadChannel), (136 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_ReadVoltage), (84 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_SetDataRate), (12 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_SetGain), (12 bytes).
    Removing drv_ads1115.o(.constdata), (24 bytes).
    Removing drv_ds18b20.o(.rev16_text), (4 bytes).
    Removing drv_ds18b20.o(.revsh_text), (4 bytes).
    Removing drv_ds18b20.o(.rrx_text), (6 bytes).
    Removing drv_ds18b20.o(i.DRV_DS18B20_ReadTemp), (174 bytes).
    Removing drv_ds18b20.o(i.DRV_DS18B20_StartConversion), (34 bytes).
    Removing drv_ina226.o(.rev16_text), (4 bytes).
    Removing drv_ina226.o(.revsh_text), (4 bytes).
    Removing drv_ina226.o(.rrx_text), (6 bytes).
    Removing drv_ina226.o(i.DRV_INA226_IsReady), (26 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadCurrent), (52 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadPower), (52 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadShuntVoltage), (52 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadVoltage), (48 bytes).
    Removing drv_max31856.o(.rev16_text), (4 bytes).
    Removing drv_max31856.o(.revsh_text), (4 bytes).
    Removing drv_max31856.o(.rrx_text), (6 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ClearFault), (62 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ReadColdJunctionTemp), (68 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ReadFault), (22 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ReadTemp), (88 bytes).
    Removing drv_max31856.o(i.MAX31856_ReadMultipleRegisters), (76 bytes).
    Removing drv_max31856.o(i.MAX31856_ReadRegister), (72 bytes).
    Removing drv_oled.o(.rev16_text), (4 bytes).
    Removing drv_oled.o(.revsh_text), (4 bytes).
    Removing drv_oled.o(.rrx_text), (6 bytes).
    Removing drv_oled.o(i.DRV_OLED_DisplayOff), (6 bytes).
    Removing drv_oled.o(i.DRV_OLED_DisplayOn), (6 bytes).
    Removing drv_oled.o(i.DRV_OLED_DrawNumber), (36 bytes).
    Removing drv_oled.o(i.DRV_OLED_SetContrast), (26 bytes).
    Removing srv_system.o(.rev16_text), (4 bytes).
    Removing srv_system.o(.revsh_text), (4 bytes).
    Removing srv_system.o(.rrx_text), (6 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_FeedWatchdog), (2 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_GetStatus), (8 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_ReportError), (84 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_SelfCheck), (44 bytes).
    Removing srv_timer.o(.rev16_text), (4 bytes).
    Removing srv_timer.o(.revsh_text), (4 bytes).
    Removing srv_timer.o(.rrx_text), (6 bytes).
    Removing srv_timer.o(i.SRV_TIMER_Stop), (40 bytes).

726 unused section(s) (total 48965 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_adc.c 0x00000000   Number         0  stm32l4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_adc_ex.c 0x00000000   Number         0  stm32l4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi.c 0x00000000   Number         0  stm32l4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_spi_ex.c 0x00000000   Number         0  stm32l4xx_hal_spi_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart.c 0x00000000   Number         0  stm32l4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32L4xx_HAL_Driver/Src/stm32l4xx_hal_uart_ex.c 0x00000000   Number         0  stm32l4xx_hal_uart_ex.o ABSOLUTE
    ../Src/main.c                            0x00000000   Number         0  main_1.o ABSOLUTE
    ../Src/stm32l4xx_hal_msp.c               0x00000000   Number         0  stm32l4xx_hal_msp_1.o ABSOLUTE
    ../Src/stm32l4xx_it.c                    0x00000000   Number         0  stm32l4xx_it_1.o ABSOLUTE
    ../Src/system_stm32l4xx.c                0x00000000   Number         0  system_stm32l4xx_1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal.c 0x00000000   Number         0  stm32l4xx_hal.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_adc.c 0x00000000   Number         0  stm32l4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_adc_ex.c 0x00000000   Number         0  stm32l4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_cortex.c 0x00000000   Number         0  stm32l4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma.c 0x00000000   Number         0  stm32l4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_dma_ex.c 0x00000000   Number         0  stm32l4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash.c 0x00000000   Number         0  stm32l4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ex.c 0x00000000   Number         0  stm32l4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32l4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_gpio.c 0x00000000   Number         0  stm32l4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c.c 0x00000000   Number         0  stm32l4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32l4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr.c 0x00000000   Number         0  stm32l4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc.c 0x00000000   Number         0  stm32l4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_spi.c 0x00000000   Number         0  stm32l4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_spi_ex.c 0x00000000   Number         0  stm32l4xx_hal_spi_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim.c 0x00000000   Number         0  stm32l4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_tim_ex.c 0x00000000   Number         0  stm32l4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_uart.c 0x00000000   Number         0  stm32l4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32L4xx_HAL_Driver\Src\stm32l4xx_hal_uart_ex.c 0x00000000   Number         0  stm32l4xx_hal_uart_ex.o ABSOLUTE
    ..\Src\app\app_adc.c                     0x00000000   Number         0  app_adc.o ABSOLUTE
    ..\Src\app\app_display.c                 0x00000000   Number         0  app_display.o ABSOLUTE
    ..\Src\app\app_load_test.c               0x00000000   Number         0  app_load_test.o ABSOLUTE
    ..\Src\app\app_protocol.c                0x00000000   Number         0  app_protocol.o ABSOLUTE
    ..\Src\bsp\bsp_io.c                      0x00000000   Number         0  bsp_io.o ABSOLUTE
    ..\Src\bsp\bsp_key.c                     0x00000000   Number         0  bsp_key.o ABSOLUTE
    ..\Src\bsp\bsp_led.c                     0x00000000   Number         0  bsp_led.o ABSOLUTE
    ..\Src\bsp\bsp_relay.c                   0x00000000   Number         0  bsp_relay.o ABSOLUTE
    ..\Src\bsp\bsp_uart.c                    0x00000000   Number         0  bsp_uart.o ABSOLUTE
    ..\Src\drivers\drv_ads1115.c             0x00000000   Number         0  drv_ads1115.o ABSOLUTE
    ..\Src\drivers\drv_ds18b20.c             0x00000000   Number         0  drv_ds18b20.o ABSOLUTE
    ..\Src\drivers\drv_ina226.c              0x00000000   Number         0  drv_ina226.o ABSOLUTE
    ..\Src\drivers\drv_max31856.c            0x00000000   Number         0  drv_max31856.o ABSOLUTE
    ..\Src\drivers\drv_oled.c                0x00000000   Number         0  drv_oled.o ABSOLUTE
    ..\Src\main.c                            0x00000000   Number         0  main_1.o ABSOLUTE
    ..\Src\service\srv_system.c              0x00000000   Number         0  srv_system.o ABSOLUTE
    ..\Src\service\srv_timer.c               0x00000000   Number         0  srv_timer.o ABSOLUTE
    ..\Src\stm32l4xx_hal_msp.c               0x00000000   Number         0  stm32l4xx_hal_msp_1.o ABSOLUTE
    ..\Src\stm32l4xx_it.c                    0x00000000   Number         0  stm32l4xx_it_1.o ABSOLUTE
    ..\Src\system_stm32l4xx.c                0x00000000   Number         0  system_stm32l4xx_1.o ABSOLUTE
    ..\\Src\\app\\app_adc.c                  0x00000000   Number         0  app_adc.o ABSOLUTE
    ..\\Src\\app\\app_display.c              0x00000000   Number         0  app_display.o ABSOLUTE
    ..\\Src\\app\\app_load_test.c            0x00000000   Number         0  app_load_test.o ABSOLUTE
    ..\\Src\\app\\app_protocol.c             0x00000000   Number         0  app_protocol.o ABSOLUTE
    ..\\Src\\bsp\\bsp_io.c                   0x00000000   Number         0  bsp_io.o ABSOLUTE
    ..\\Src\\bsp\\bsp_key.c                  0x00000000   Number         0  bsp_key.o ABSOLUTE
    ..\\Src\\bsp\\bsp_led.c                  0x00000000   Number         0  bsp_led.o ABSOLUTE
    ..\\Src\\bsp\\bsp_relay.c                0x00000000   Number         0  bsp_relay.o ABSOLUTE
    ..\\Src\\bsp\\bsp_uart.c                 0x00000000   Number         0  bsp_uart.o ABSOLUTE
    ..\\Src\\drivers\\drv_ads1115.c          0x00000000   Number         0  drv_ads1115.o ABSOLUTE
    ..\\Src\\drivers\\drv_ds18b20.c          0x00000000   Number         0  drv_ds18b20.o ABSOLUTE
    ..\\Src\\drivers\\drv_ina226.c           0x00000000   Number         0  drv_ina226.o ABSOLUTE
    ..\\Src\\drivers\\drv_max31856.c         0x00000000   Number         0  drv_max31856.o ABSOLUTE
    ..\\Src\\drivers\\drv_oled.c             0x00000000   Number         0  drv_oled.o ABSOLUTE
    ..\\Src\\service\\srv_system.c           0x00000000   Number         0  srv_system.o ABSOLUTE
    ..\\Src\\service\\srv_timer.c            0x00000000   Number         0  srv_timer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32l431xx.s                    0x00000000   Number         0  startup_stm32l431xx.o ABSOLUTE
    RESET                                    0x08000000   Section      396  startup_stm32l431xx.o(RESET)
    !!!main                                  0x0800018c   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000194   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c8   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e4   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000200   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x08000200   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000206   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x0800020c   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x08000212   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000218   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800021c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800021e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x08000222   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x0800022a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800022a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800022a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800022a   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000230   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800023a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800023c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800023e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000240   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000240   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000240   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000246   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000246   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800024a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800024a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000252   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000254   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000254   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000258   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000260   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x08000260   Section       64  startup_stm32l431xx.o(.text)
    $v0                                      0x08000260   Number         0  startup_stm32l431xx.o(.text)
    .text                                    0x080002a0   Section        0  h1_alloc.o(.text)
    .text                                    0x080002fe   Section        0  h1_free.o(.text)
    .text                                    0x0800034c   Section      238  lludivv7m.o(.text)
    .text                                    0x0800043c   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000464   Section        0  _printf_pad.o(.text)
    .text                                    0x080004b2   Section        0  _printf_str.o(.text)
    .text                                    0x08000504   Section        0  _printf_dec.o(.text)
    .text                                    0x0800057c   Section        0  _printf_hex_int.o(.text)
    .text                                    0x080005d4   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x08000712   Section        0  strcpy.o(.text)
    .text                                    0x0800075a   Section        0  strlen.o(.text)
    .text                                    0x08000798   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000822   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000886   Section       68  rt_memclr.o(.text)
    .text                                    0x080008ca   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000918   Section        0  heapauxi.o(.text)
    .text                                    0x08000920   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08000928   Section        0  hguard.o(.text)
    .text                                    0x0800092c   Section        0  init_alloc.o(.text)
    .text                                    0x080009b6   Section        0  h1_init.o(.text)
    .text                                    0x080009c4   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000a76   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000a79   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000e9c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000e9d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000ecc   Section        0  _sputc.o(.text)
    .text                                    0x08000ed6   Section        0  _printf_char.o(.text)
    .text                                    0x08000f04   Section        8  libspace.o(.text)
    .text                                    0x08000f0c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000f14   Section        0  h1_extend.o(.text)
    .text                                    0x08000f48   Section      138  lludiv10.o(.text)
    .text                                    0x08000fd4   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001054   Section        0  bigflt0.o(.text)
    .text                                    0x08001138   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x08001146   Section        2  use_no_semi.o(.text)
    .text                                    0x08001148   Section        0  indicate_semi.o(.text)
    .text                                    0x08001148   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001192   Section        0  exit.o(.text)
    .text                                    0x080011a4   Section        0  defsig_exit.o(.text)
    .text                                    0x080011b0   Section        0  defsig_rtmem_inner.o(.text)
    .text                                    0x08001200   Section      128  strcmpv7m.o(.text)
    .text                                    0x08001280   Section        0  sys_exit.o(.text)
    .text                                    0x0800128c   Section        0  defsig_general.o(.text)
    .text                                    0x080012be   Section        0  sys_wrch.o(.text)
    CL$$btod_d2e                             0x080012cc   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800130a   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001350   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080013b0   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x080016e8   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x080017c4   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x080017ee   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001818   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_Disable                            0x08001a5c   Section        0  stm32l4xx_hal_adc.o(i.ADC_Disable)
    i.ADS1115_ReadRegister                   0x08001ac4   Section        0  drv_ads1115.o(i.ADS1115_ReadRegister)
    ADS1115_ReadRegister                     0x08001ac5   Thumb Code    60  drv_ads1115.o(i.ADS1115_ReadRegister)
    i.APP_ADC_Init                           0x08001b04   Section        0  app_adc.o(i.APP_ADC_Init)
    i.APP_ADC_Process                        0x08001b1c   Section        0  app_adc.o(i.APP_ADC_Process)
    i.APP_DISPLAY_Init                       0x08001b20   Section        0  app_display.o(i.APP_DISPLAY_Init)
    i.APP_DISPLAY_Process                    0x08001b58   Section        0  app_display.o(i.APP_DISPLAY_Process)
    i.APP_DISPLAY_ShowRunningStatus          0x08001bc4   Section        0  app_display.o(i.APP_DISPLAY_ShowRunningStatus)
    i.APP_DISPLAY_UpdateData                 0x08001c14   Section        0  app_display.o(i.APP_DISPLAY_UpdateData)
    i.APP_PROTOCOL_Init                      0x08001c38   Section        0  app_protocol.o(i.APP_PROTOCOL_Init)
    i.APP_PROTOCOL_Process                   0x08001c50   Section        0  app_protocol.o(i.APP_PROTOCOL_Process)
    i.BSP_IO_GetState                        0x08001c54   Section        0  bsp_io.o(i.BSP_IO_GetState)
    i.BSP_IO_Init                            0x08001c70   Section        0  bsp_io.o(i.BSP_IO_Init)
    i.BSP_IO_RegisterCallback                0x08001c74   Section        0  bsp_io.o(i.BSP_IO_RegisterCallback)
    i.BSP_IO_Scan                            0x08001c8c   Section        0  bsp_io.o(i.BSP_IO_Scan)
    i.BSP_KEY_GetState                       0x08001cd4   Section        0  bsp_key.o(i.BSP_KEY_GetState)
    i.BSP_KEY_Init                           0x08001cec   Section        0  bsp_key.o(i.BSP_KEY_Init)
    i.BSP_KEY_Scan                           0x08001cf0   Section        0  bsp_key.o(i.BSP_KEY_Scan)
    i.BSP_LED_Init                           0x08001d38   Section        0  bsp_led.o(i.BSP_LED_Init)
    i.BSP_LED_Process                        0x08001d3c   Section        0  bsp_led.o(i.BSP_LED_Process)
    i.BSP_LED_SetBlink                       0x08001d74   Section        0  bsp_led.o(i.BSP_LED_SetBlink)
    i.BSP_LED_Toggle                         0x08001d94   Section        0  bsp_led.o(i.BSP_LED_Toggle)
    i.BSP_RELAY_GetState                     0x08001dac   Section        0  bsp_relay.o(i.BSP_RELAY_GetState)
    i.BSP_RELAY_Init                         0x08001dc4   Section        0  bsp_relay.o(i.BSP_RELAY_Init)
    i.BSP_RELAY_Process                      0x08001dc8   Section        0  bsp_relay.o(i.BSP_RELAY_Process)
    i.BSP_UART_Init                          0x08001dcc   Section        0  bsp_uart.o(i.BSP_UART_Init)
    i.BSP_UART_SendData                      0x08001dd8   Section        0  bsp_uart.o(i.BSP_UART_SendData)
    i.BusFault_Handler                       0x08001dfc   Section        0  stm32l4xx_it_1.o(i.BusFault_Handler)
    i.DRV_ADS1115_Init                       0x08001e00   Section        0  drv_ads1115.o(i.DRV_ADS1115_Init)
    i.DRV_DS18B20_Init                       0x08001e24   Section        0  drv_ds18b20.o(i.DRV_DS18B20_Init)
    i.DRV_DS18B20_IsPresent                  0x08001e8c   Section        0  drv_ds18b20.o(i.DRV_DS18B20_IsPresent)
    i.DRV_DS18B20_SetResolution              0x08001e90   Section        0  drv_ds18b20.o(i.DRV_DS18B20_SetResolution)
    i.DRV_INA226_Init                        0x08001ee4   Section        0  drv_ina226.o(i.DRV_INA226_Init)
    i.DRV_INA226_SetCalibration              0x08001f58   Section        0  drv_ina226.o(i.DRV_INA226_SetCalibration)
    i.DRV_MAX31856_Config                    0x08001f98   Section        0  drv_max31856.o(i.DRV_MAX31856_Config)
    i.DRV_MAX31856_Init                      0x08001fcc   Section        0  drv_max31856.o(i.DRV_MAX31856_Init)
    i.DRV_OLED_Clear                         0x0800209c   Section        0  drv_oled.o(i.DRV_OLED_Clear)
    i.DRV_OLED_DrawFloat                     0x080020b0   Section        0  drv_oled.o(i.DRV_OLED_DrawFloat)
    i.DRV_OLED_DrawPixel                     0x080020ec   Section        0  drv_oled.o(i.DRV_OLED_DrawPixel)
    i.DRV_OLED_DrawString                    0x08002120   Section        0  drv_oled.o(i.DRV_OLED_DrawString)
    i.DRV_OLED_Init                          0x080021d4   Section        0  drv_oled.o(i.DRV_OLED_Init)
    i.DRV_OLED_Update                        0x080022c4   Section        0  drv_oled.o(i.DRV_OLED_Update)
    i.DS18B20_DelayUs                        0x08002340   Section        0  drv_ds18b20.o(i.DS18B20_DelayUs)
    DS18B20_DelayUs                          0x08002341   Thumb Code    28  drv_ds18b20.o(i.DS18B20_DelayUs)
    i.DS18B20_ReadPin                        0x08002368   Section        0  drv_ds18b20.o(i.DS18B20_ReadPin)
    DS18B20_ReadPin                          0x08002369   Thumb Code    16  drv_ds18b20.o(i.DS18B20_ReadPin)
    i.DS18B20_Reset                          0x0800237c   Section        0  drv_ds18b20.o(i.DS18B20_Reset)
    DS18B20_Reset                            0x0800237d   Thumb Code    60  drv_ds18b20.o(i.DS18B20_Reset)
    i.DS18B20_SetPinInput                    0x080023b8   Section        0  drv_ds18b20.o(i.DS18B20_SetPinInput)
    DS18B20_SetPinInput                      0x080023b9   Thumb Code    46  drv_ds18b20.o(i.DS18B20_SetPinInput)
    i.DS18B20_SetPinOutput                   0x080023ec   Section        0  drv_ds18b20.o(i.DS18B20_SetPinOutput)
    DS18B20_SetPinOutput                     0x080023ed   Thumb Code    50  drv_ds18b20.o(i.DS18B20_SetPinOutput)
    i.DS18B20_WriteByte                      0x08002424   Section        0  drv_ds18b20.o(i.DS18B20_WriteByte)
    DS18B20_WriteByte                        0x08002425   Thumb Code    80  drv_ds18b20.o(i.DS18B20_WriteByte)
    i.DS18B20_WritePin                       0x08002474   Section        0  drv_ds18b20.o(i.DS18B20_WritePin)
    DS18B20_WritePin                         0x08002475   Thumb Code    18  drv_ds18b20.o(i.DS18B20_WritePin)
    i.DebugMon_Handler                       0x0800248c   Section        0  stm32l4xx_it_1.o(i.DebugMon_Handler)
    i.DisplayMainPage                        0x08002490   Section        0  app_display.o(i.DisplayMainPage)
    DisplayMainPage                          0x08002491   Thumb Code   142  app_display.o(i.DisplayMainPage)
    i.DisplaySensorPage                      0x08002554   Section        0  app_display.o(i.DisplaySensorPage)
    DisplaySensorPage                        0x08002555   Thumb Code   136  app_display.o(i.DisplaySensorPage)
    i.DisplayStatusPage                      0x080025f8   Section        0  app_display.o(i.DisplayStatusPage)
    DisplayStatusPage                        0x080025f9   Thumb Code   134  app_display.o(i.DisplayStatusPage)
    i.EXTI3_IRQHandler                       0x080026b8   Section        0  stm32l4xx_it_1.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x080026be   Section        0  stm32l4xx_it_1.o(i.EXTI4_IRQHandler)
    i.HAL_ADCEx_Calibration_Start            0x080026c4   Section        0  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    i.HAL_ADC_ConfigChannel                  0x08002750   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_Init                           0x08002b60   Section        0  stm32l4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08002d00   Section        0  stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspInit)
    i.HAL_Delay                              0x08002d44   Section        0  stm32l4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x08002d60   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x08002d64   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x08002d7c   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08002f12   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_TogglePin                     0x08002f1c   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08002f24   Section        0  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002f30   Section        0  stm32l4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x08002f3c   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08002f92   Section        0  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x08002fe4   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Receive                 0x0800309c   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    i.HAL_I2C_Master_Transmit                0x080031d4   Section        0  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspInit                        0x08003308   Section        0  stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08003380   Section        0  stm32l4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003390   Section        0  stm32l4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080033a8   Section        0  stm32l4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080033d0   Section        0  stm32l4xx_hal_msp_1.o(i.HAL_MspInit)
    i.HAL_NVIC_SetPriority                   0x08003450   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003490   Section        0  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x080034b4   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_GetVoltageRange              0x08003518   Section        0  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    i.HAL_RCCEx_PeriphCLKConfig              0x08003528   Section        0  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x080037dc   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x0800394c   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003958   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003978   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003998   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003a5c   Section        0  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08003f04   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08003fb0   Section        0  stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Transmit                       0x08003ff8   Section        0  stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SYSTICK_CLKSourceConfig            0x08004170   Section        0  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Callback                   0x08004188   Section        0  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback)
    i.HAL_SYSTICK_Config                     0x0800418a   Section        0  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_SYSTICK_IRQHandler                 0x080041b2   Section        0  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler)
    i.HAL_UART_Init                          0x080041ba   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004224   Section        0  stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x0800426c   Section        0  stm32l4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HardFault_Handler                      0x0800431c   Section        0  stm32l4xx_it_1.o(i.HardFault_Handler)
    i.I2C_Flush_TXDR                         0x0800431e   Section        0  stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x0800431f   Thumb Code    34  stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_IsAcknowledgeFailed                0x08004340   Section        0  stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08004341   Thumb Code   122  stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_TransferConfig                     0x080043c0   Section        0  stm32l4xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x080043c1   Thumb Code    40  stm32l4xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_WaitOnFlagUntilTimeout             0x080043ec   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x080043ed   Thumb Code    82  stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08004440   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08004441   Thumb Code   136  stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x080044cc   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x080044cd   Thumb Code    78  stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x0800451a   Section        0  stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x0800451b   Thumb Code    82  stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.INA226_ReadRegister                    0x0800456c   Section        0  drv_ina226.o(i.INA226_ReadRegister)
    INA226_ReadRegister                      0x0800456d   Thumb Code    60  drv_ina226.o(i.INA226_ReadRegister)
    i.INA226_WriteRegister                   0x080045ac   Section        0  drv_ina226.o(i.INA226_WriteRegister)
    INA226_WriteRegister                     0x080045ad   Thumb Code    36  drv_ina226.o(i.INA226_WriteRegister)
    i.IOScanTask                             0x080045d4   Section        0  main_1.o(i.IOScanTask)
    i.IOStateChangeCallback                  0x080045d8   Section        0  main_1.o(i.IOStateChangeCallback)
    i.KeyScanTask                            0x08004624   Section        0  main_1.o(i.KeyScanTask)
    i.LL_ADC_GetOffsetChannel                0x08004628   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    LL_ADC_GetOffsetChannel                  0x08004629   Thumb Code    12  stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel)
    i.LL_ADC_REG_IsConversionOngoing         0x08004634   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    LL_ADC_REG_IsConversionOngoing           0x08004635   Thumb Code     8  stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing)
    i.LL_ADC_SetChannelSamplingTime          0x0800463c   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    LL_ADC_SetChannelSamplingTime            0x0800463d   Thumb Code    70  stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime)
    i.LL_ADC_SetOffsetState                  0x08004682   Section        0  stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    LL_ADC_SetOffsetState                    0x08004683   Thumb Code    16  stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState)
    i.MAX31856_CS_Control                    0x08004694   Section        0  drv_max31856.o(i.MAX31856_CS_Control)
    MAX31856_CS_Control                      0x08004695   Thumb Code    24  drv_max31856.o(i.MAX31856_CS_Control)
    i.MAX31856_WriteRegister                 0x080046b0   Section        0  drv_max31856.o(i.MAX31856_WriteRegister)
    MAX31856_WriteRegister                   0x080046b1   Thumb Code    52  drv_max31856.o(i.MAX31856_WriteRegister)
    i.MX_ADC1_Init                           0x080046e8   Section        0  main_1.o(i.MX_ADC1_Init)
    MX_ADC1_Init                             0x080046e9   Thumb Code    80  main_1.o(i.MX_ADC1_Init)
    i.MX_GPIO_Init                           0x08004744   Section        0  main_1.o(i.MX_GPIO_Init)
    MX_GPIO_Init                             0x08004745   Thumb Code   264  main_1.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x0800485c   Section        0  main_1.o(i.MX_I2C1_Init)
    MX_I2C1_Init                             0x0800485d   Thumb Code    54  main_1.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x080048a0   Section        0  main_1.o(i.MX_I2C2_Init)
    MX_I2C2_Init                             0x080048a1   Thumb Code    54  main_1.o(i.MX_I2C2_Init)
    i.MX_LPUART1_UART_Init                   0x080048e4   Section        0  main_1.o(i.MX_LPUART1_UART_Init)
    MX_LPUART1_UART_Init                     0x080048e5   Thumb Code    36  main_1.o(i.MX_LPUART1_UART_Init)
    i.MX_SPI1_Init                           0x08004914   Section        0  main_1.o(i.MX_SPI1_Init)
    MX_SPI1_Init                             0x08004915   Thumb Code    54  main_1.o(i.MX_SPI1_Init)
    i.MemManage_Handler                      0x08004954   Section        0  stm32l4xx_it_1.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004956   Section        0  stm32l4xx_it_1.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08004958   Section        0  stm32l4xx_hal_cortex.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08004959   Thumb Code    32  stm32l4xx_hal_cortex.o(i.NVIC_SetPriority)
    i.OLED_WriteCommand                      0x08004978   Section        0  drv_oled.o(i.OLED_WriteCommand)
    OLED_WriteCommand                        0x08004979   Thumb Code    30  drv_oled.o(i.OLED_WriteCommand)
    i.PendSV_Handler                         0x0800499c   Section        0  stm32l4xx_it_1.o(i.PendSV_Handler)
    i.RCCEx_PLLSAI1_Config                   0x080049a0   Section        0  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    RCCEx_PLLSAI1_Config                     0x080049a1   Thumb Code   276  stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config)
    i.RCC_SetFlashLatencyFromMSIRange        0x08004ac8   Section        0  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    RCC_SetFlashLatencyFromMSIRange          0x08004ac9   Thumb Code   116  stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange)
    i.SPI_EndRxTxTransaction                 0x08004b44   Section        0  stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x08004b45   Thumb Code    72  stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFifoStateUntilTimeout          0x08004b8c   Section        0  stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout)
    SPI_WaitFifoStateUntilTimeout            0x08004b8d   Thumb Code   158  stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout)
    i.SPI_WaitFlagStateUntilTimeout          0x08004c2a   Section        0  stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08004c2b   Thumb Code   148  stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SRV_SYSTEM_Init                        0x08004cc0   Section        0  srv_system.o(i.SRV_SYSTEM_Init)
    i.SRV_SYSTEM_Process                     0x08004cf8   Section        0  srv_system.o(i.SRV_SYSTEM_Process)
    i.SRV_TIMER_Create                       0x08004d3c   Section        0  srv_timer.o(i.SRV_TIMER_Create)
    i.SRV_TIMER_Init                         0x08004d78   Section        0  srv_timer.o(i.SRV_TIMER_Init)
    i.SRV_TIMER_Process                      0x08004d9c   Section        0  srv_timer.o(i.SRV_TIMER_Process)
    i.SRV_TIMER_Start                        0x08004ddc   Section        0  srv_timer.o(i.SRV_TIMER_Start)
    i.SVC_Handler                            0x08004e0c   Section        0  stm32l4xx_it_1.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08004e0e   Section        0  stm32l4xx_it_1.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08004e1c   Section        0  main_1.o(i.SystemClock_Config)
    i.SystemInit                             0x08004ec0   Section        0  system_stm32l4xx_1.o(i.SystemInit)
    i.UART_AdvFeatureConfig                  0x08004f04   Section        0  stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08004fcc   Section        0  stm32l4xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_SetConfig                         0x08005028   Section        0  stm32l4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x0800529c   Section        0  stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.UsageFault_Handler                     0x080052fe   Section        0  stm32l4xx_it_1.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08005300   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08005330   Section        0  __printf_wp.o(i._is_digit)
    i.main                                   0x08005340   Section        0  main_1.o(i.main)
    locale$$code                             0x080056ec   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dretinf                            0x08005718   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08005718   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08005724   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08005724   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800577a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800577a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08005806   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08005806   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x08005810   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08005810   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x08005814   Section       64  system_stm32l4xx_1.o(.constdata)
    x$fpl$usenofp                            0x08005814   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08005854   Section        8  system_stm32l4xx_1.o(.constdata)
    .constdata                               0x0800585c   Section       16  drv_ds18b20.o(.constdata)
    ds18b20_pins                             0x0800585c   Data          16  drv_ds18b20.o(.constdata)
    .constdata                               0x0800586c   Section       16  drv_max31856.o(.constdata)
    cs_pins                                  0x0800586c   Data          16  drv_max31856.o(.constdata)
    .constdata                               0x0800587c   Section      120  drv_oled.o(.constdata)
    font_6x8                                 0x0800587c   Data         120  drv_oled.o(.constdata)
    .constdata                               0x080058f4   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x080058f4   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08005908   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800591c   Section       17  __printf_flags_wp.o(.constdata)
    maptable                                 0x0800591c   Data          17  __printf_flags_wp.o(.constdata)
    .constdata                               0x08005930   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08005930   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800596c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080059e4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080059e8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080059f0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080059fc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080059fe   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080059ff   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08005a00   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        8  main_1.o(.data)
    last_display_update                      0x20000000   Data           4  main_1.o(.data)
    last_key_state                           0x20000004   Data           4  main_1.o(.data)
    .data                                    0x20000008   Section        4  stm32l4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32l4xx_1.o(.data)
    .data                                    0x20000010   Section       24  bsp_io.o(.data)
    io_config                                0x20000010   Data          24  bsp_io.o(.data)
    .data                                    0x20000028   Section       64  bsp_key.o(.data)
    key_config                               0x20000028   Data          64  bsp_key.o(.data)
    .data                                    0x20000068   Section       32  bsp_led.o(.data)
    led_config                               0x20000068   Data          32  bsp_led.o(.data)
    .data                                    0x20000088   Section       16  bsp_relay.o(.data)
    relay_config                             0x20000088   Data          16  bsp_relay.o(.data)
    .data                                    0x20000098   Section        4  bsp_uart.o(.data)
    g_huart                                  0x20000098   Data           4  bsp_uart.o(.data)
    .data                                    0x2000009c   Section       12  app_adc.o(.data)
    app_hadc                                 0x2000009c   Data           4  app_adc.o(.data)
    adc_channels                             0x200000a0   Data           8  app_adc.o(.data)
    .data                                    0x200000a8   Section       12  app_display.o(.data)
    current_page                             0x200000a8   Data           1  app_display.o(.data)
    update_flag                              0x200000a9   Data           1  app_display.o(.data)
    page_switch_counter                      0x200000ac   Data           4  app_display.o(.data)
    last_update_time                         0x200000b0   Data           4  app_display.o(.data)
    .data                                    0x200000b4   Section       12  app_protocol.o(.data)
    protocol_state                           0x200000b4   Data           1  app_protocol.o(.data)
    rx_data_index                            0x200000b5   Data           1  app_protocol.o(.data)
    rx_checksum                              0x200000b6   Data           2  app_protocol.o(.data)
    calc_checksum                            0x200000b8   Data           2  app_protocol.o(.data)
    protocol_huart                           0x200000bc   Data           4  app_protocol.o(.data)
    .data                                    0x200000c0   Section        8  drv_ads1115.o(.data)
    current_gain                             0x200000c0   Data           1  drv_ads1115.o(.data)
    current_data_rate                        0x200000c1   Data           1  drv_ads1115.o(.data)
    ads1115_hi2c                             0x200000c4   Data           4  drv_ads1115.o(.data)
    .data                                    0x200000c8   Section       12  drv_ina226.o(.data)
    current_lsb                              0x200000c8   Data           4  drv_ina226.o(.data)
    power_lsb                                0x200000cc   Data           4  drv_ina226.o(.data)
    ina226_hi2c                              0x200000d0   Data           4  drv_ina226.o(.data)
    .data                                    0x200000d4   Section        4  drv_max31856.o(.data)
    max31856_hspi                            0x200000d4   Data           4  drv_max31856.o(.data)
    .data                                    0x200000d8   Section        4  drv_oled.o(.data)
    oled_hi2c                                0x200000d8   Data           4  drv_oled.o(.data)
    .data                                    0x200000dc   Section        8  srv_system.o(.data)
    system_uptime                            0x200000dc   Data           4  srv_system.o(.data)
    last_uptime_update                       0x200000e0   Data           4  srv_system.o(.data)
    .data                                    0x200000e4   Section        1  srv_timer.o(.data)
    timer_count                              0x200000e4   Data           1  srv_timer.o(.data)
    .bss                                     0x200000e8   Section      476  main_1.o(.bss)
    .bss                                     0x200002c4   Section       88  app_display.o(.bss)
    display_data                             0x200002c4   Data          88  app_display.o(.bss)
    .bss                                     0x2000031c   Section     1024  drv_oled.o(.bss)
    oled_buffer                              0x2000031c   Data        1024  drv_oled.o(.bss)
    .bss                                     0x2000071c   Section       28  srv_system.o(.bss)
    system_status                            0x2000071c   Data          28  srv_system.o(.bss)
    .bss                                     0x20000738   Section      160  srv_timer.o(.bss)
    timers                                   0x20000738   Data         160  srv_timer.o(.bss)
    .bss                                     0x200007d8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000838   Section      512  startup_stm32l431xx.o(HEAP)
    Heap_Mem                                 0x20000838   Data         512  startup_stm32l431xx.o(HEAP)
    STACK                                    0x20000a38   Section     1024  startup_stm32l431xx.o(STACK)
    Stack_Mem                                0x20000a38   Data        1024  startup_stm32l431xx.o(STACK)
    __initial_sp                             0x20000e38   Data           0  startup_stm32l431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main_1.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x0000018c   Number         0  startup_stm32l431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32l431xx.o(RESET)
    __Vectors_End                            0x0800018c   Data           0  startup_stm32l431xx.o(RESET)
    __main                                   0x0800018d   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000195   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000195   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000195   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001a3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c9   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e5   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000201   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x08000201   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000207   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_x                                0x0800020d   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x08000213   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000219   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800021d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_heap_1                     0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800023d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000241   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000241   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000241   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000247   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000247   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000253   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000255   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000255   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000259   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000261   Thumb Code     8  startup_stm32l431xx.o(.text)
    _maybe_terminate_alloc                   0x08000261   Thumb Code     0  maybetermalloc1.o(.emb_text)
    ADC1_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    COMP_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    CRS_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    DMA2_Channel7_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI0_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI1_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI2_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    FLASH_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    FPU_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    LPTIM1_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    LPTIM2_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    LPUART1_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    PVD_PVM_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    QUADSPI_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    RCC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    RNG_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    SAI1_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    SDMMC1_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    SPI1_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    SPI2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    SPI3_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    SWPMI1_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TIM7_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    TSC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    USART1_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    USART2_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    USART3_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    WWDG_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32l431xx.o(.text)
    __user_initial_stackheap                 0x0800027d   Thumb Code     0  startup_stm32l431xx.o(.text)
    malloc                                   0x080002a1   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x080002ff   Thumb Code    78  h1_free.o(.text)
    __aeabi_uldivmod                         0x0800034d   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x0800034d   Thumb Code   238  lludivv7m.o(.text)
    __2sprintf                               0x0800043d   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x08000465   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000491   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x080004b3   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000505   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x0800057d   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800057d   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x080005d5   Thumb Code   308  __printf_flags_wp.o(.text)
    strcpy                                   0x08000713   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x0800075b   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x08000799   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000799   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080007ff   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08000823   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000823   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000823   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800086b   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x08000887   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000887   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800088b   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080008cb   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080008cb   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080008cb   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080008cf   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000919   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x0800091b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x0800091d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_descriptor                     0x08000921   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x08000929   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x0800092b   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x0800092d   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x0800092f   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x08000931   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x08000953   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x08000959   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x080009b7   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x080009c1   Thumb Code     4  h1_init.o(.text)
    _printf_int_common                       0x080009c5   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000a77   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000c29   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000ea7   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000ecd   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000ed7   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000eeb   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000efb   Thumb Code     8  _printf_char.o(.text)
    __user_libspace                          0x08000f05   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000f05   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000f05   Thumb Code     0  libspace.o(.text)
    __rt_locale                              0x08000f0d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __Heap_ProvideMemory                     0x08000f15   Thumb Code    52  h1_extend.o(.text)
    _ll_udiv10                               0x08000f49   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000fd5   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001055   Thumb Code   224  bigflt0.o(.text)
    __rt_SIGRTMEM                            0x08001139   Thumb Code    14  defsig_rtmem_outer.o(.text)
    __I$use$semihosting                      0x08001147   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001147   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001149   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x08001149   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001193   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x080011a5   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTMEM_inner                      0x080011b1   Thumb Code    22  defsig_rtmem_inner.o(.text)
    strcmp                                   0x08001201   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08001281   Thumb Code     8  sys_exit.o(.text)
    __default_signal_display                 0x0800128d   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x080012bf   Thumb Code    14  sys_wrch.o(.text)
    _btod_d2e                                0x080012cd   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800130b   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001351   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080013b1   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x080016e9   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x080017c5   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x080017ef   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001819   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_Disable                              0x08001a5d   Thumb Code    98  stm32l4xx_hal_adc.o(i.ADC_Disable)
    APP_ADC_Init                             0x08001b05   Thumb Code    20  app_adc.o(i.APP_ADC_Init)
    APP_ADC_Process                          0x08001b1d   Thumb Code     2  app_adc.o(i.APP_ADC_Process)
    APP_DISPLAY_Init                         0x08001b21   Thumb Code    36  app_display.o(i.APP_DISPLAY_Init)
    APP_DISPLAY_Process                      0x08001b59   Thumb Code   104  app_display.o(i.APP_DISPLAY_Process)
    APP_DISPLAY_ShowRunningStatus            0x08001bc5   Thumb Code    50  app_display.o(i.APP_DISPLAY_ShowRunningStatus)
    APP_DISPLAY_UpdateData                   0x08001c15   Thumb Code    28  app_display.o(i.APP_DISPLAY_UpdateData)
    APP_PROTOCOL_Init                        0x08001c39   Thumb Code    20  app_protocol.o(i.APP_PROTOCOL_Init)
    APP_PROTOCOL_Process                     0x08001c51   Thumb Code     2  app_protocol.o(i.APP_PROTOCOL_Process)
    BSP_IO_GetState                          0x08001c55   Thumb Code    22  bsp_io.o(i.BSP_IO_GetState)
    BSP_IO_Init                              0x08001c71   Thumb Code     4  bsp_io.o(i.BSP_IO_Init)
    BSP_IO_RegisterCallback                  0x08001c75   Thumb Code    18  bsp_io.o(i.BSP_IO_RegisterCallback)
    BSP_IO_Scan                              0x08001c8d   Thumb Code    66  bsp_io.o(i.BSP_IO_Scan)
    BSP_KEY_GetState                         0x08001cd5   Thumb Code    18  bsp_key.o(i.BSP_KEY_GetState)
    BSP_KEY_Init                             0x08001ced   Thumb Code     4  bsp_key.o(i.BSP_KEY_Init)
    BSP_KEY_Scan                             0x08001cf1   Thumb Code    66  bsp_key.o(i.BSP_KEY_Scan)
    BSP_LED_Init                             0x08001d39   Thumb Code     4  bsp_led.o(i.BSP_LED_Init)
    BSP_LED_Process                          0x08001d3d   Thumb Code    52  bsp_led.o(i.BSP_LED_Process)
    BSP_LED_SetBlink                         0x08001d75   Thumb Code    26  bsp_led.o(i.BSP_LED_SetBlink)
    BSP_LED_Toggle                           0x08001d95   Thumb Code    20  bsp_led.o(i.BSP_LED_Toggle)
    BSP_RELAY_GetState                       0x08001dad   Thumb Code    18  bsp_relay.o(i.BSP_RELAY_GetState)
    BSP_RELAY_Init                           0x08001dc5   Thumb Code     4  bsp_relay.o(i.BSP_RELAY_Init)
    BSP_RELAY_Process                        0x08001dc9   Thumb Code     2  bsp_relay.o(i.BSP_RELAY_Process)
    BSP_UART_Init                            0x08001dcd   Thumb Code     8  bsp_uart.o(i.BSP_UART_Init)
    BSP_UART_SendData                        0x08001dd9   Thumb Code    30  bsp_uart.o(i.BSP_UART_SendData)
    BusFault_Handler                         0x08001dfd   Thumb Code     2  stm32l4xx_it_1.o(i.BusFault_Handler)
    DRV_ADS1115_Init                         0x08001e01   Thumb Code    30  drv_ads1115.o(i.DRV_ADS1115_Init)
    DRV_DS18B20_Init                         0x08001e25   Thumb Code    92  drv_ds18b20.o(i.DRV_DS18B20_Init)
    DRV_DS18B20_IsPresent                    0x08001e8d   Thumb Code     4  drv_ds18b20.o(i.DRV_DS18B20_IsPresent)
    DRV_DS18B20_SetResolution                0x08001e91   Thumb Code    84  drv_ds18b20.o(i.DRV_DS18B20_SetResolution)
    DRV_INA226_Init                          0x08001ee5   Thumb Code   106  drv_ina226.o(i.DRV_INA226_Init)
    DRV_INA226_SetCalibration                0x08001f59   Thumb Code    50  drv_ina226.o(i.DRV_INA226_SetCalibration)
    DRV_MAX31856_Config                      0x08001f99   Thumb Code    50  drv_max31856.o(i.DRV_MAX31856_Config)
    DRV_MAX31856_Init                        0x08001fcd   Thumb Code   190  drv_max31856.o(i.DRV_MAX31856_Init)
    DRV_OLED_Clear                           0x0800209d   Thumb Code    16  drv_oled.o(i.DRV_OLED_Clear)
    DRV_OLED_DrawFloat                       0x080020b1   Thumb Code    50  drv_oled.o(i.DRV_OLED_DrawFloat)
    DRV_OLED_DrawPixel                       0x080020ed   Thumb Code    46  drv_oled.o(i.DRV_OLED_DrawPixel)
    DRV_OLED_DrawString                      0x08002121   Thumb Code   166  drv_oled.o(i.DRV_OLED_DrawString)
    DRV_OLED_Init                            0x080021d5   Thumb Code   234  drv_oled.o(i.DRV_OLED_Init)
    DRV_OLED_Update                          0x080022c5   Thumb Code   114  drv_oled.o(i.DRV_OLED_Update)
    DebugMon_Handler                         0x0800248d   Thumb Code     2  stm32l4xx_it_1.o(i.DebugMon_Handler)
    EXTI3_IRQHandler                         0x080026b9   Thumb Code     6  stm32l4xx_it_1.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x080026bf   Thumb Code     6  stm32l4xx_it_1.o(i.EXTI4_IRQHandler)
    HAL_ADCEx_Calibration_Start              0x080026c5   Thumb Code   138  stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    HAL_ADC_ConfigChannel                    0x08002751   Thumb Code   998  stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_Init                             0x08002b61   Thumb Code   388  stm32l4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08002d01   Thumb Code    54  stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspInit)
    HAL_Delay                                0x08002d45   Thumb Code    28  stm32l4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x08002d61   Thumb Code     2  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x08002d65   Thumb Code    18  stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08002d7d   Thumb Code   370  stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002f13   Thumb Code    10  stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x08002f1d   Thumb Code     8  stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08002f25   Thumb Code    10  stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002f31   Thumb Code     6  stm32l4xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x08002f3d   Thumb Code    86  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08002f93   Thumb Code    82  stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x08002fe5   Thumb Code   178  stm32l4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Receive                   0x0800309d   Thumb Code   294  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    HAL_I2C_Master_Transmit                  0x080031d5   Thumb Code   292  stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspInit                          0x08003309   Thumb Code   104  stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08003381   Thumb Code    10  stm32l4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003391   Thumb Code    22  stm32l4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080033a9   Thumb Code    34  stm32l4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080033d1   Thumb Code   122  stm32l4xx_hal_msp_1.o(i.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x08003451   Thumb Code    60  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003491   Thumb Code    26  stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x080034b5   Thumb Code    86  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_GetVoltageRange                0x08003519   Thumb Code    10  stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange)
    HAL_RCCEx_PeriphCLKConfig                0x08003529   Thumb Code   676  stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x080037dd   Thumb Code   352  stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x0800394d   Thumb Code     6  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003959   Thumb Code    20  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003979   Thumb Code    20  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003999   Thumb Code   182  stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003a5d   Thumb Code  1186  stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08003f05   Thumb Code   172  stm32l4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08003fb1   Thumb Code    64  stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspInit)
    HAL_SPI_Transmit                         0x08003ff9   Thumb Code   370  stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SYSTICK_CLKSourceConfig              0x08004171   Thumb Code    24  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Callback                     0x08004189   Thumb Code     2  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback)
    HAL_SYSTICK_Config                       0x0800418b   Thumb Code    40  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_SYSTICK_IRQHandler                   0x080041b3   Thumb Code     8  stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler)
    HAL_UART_Init                            0x080041bb   Thumb Code   106  stm32l4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004225   Thumb Code    60  stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x0800426d   Thumb Code   176  stm32l4xx_hal_uart.o(i.HAL_UART_Transmit)
    HardFault_Handler                        0x0800431d   Thumb Code     2  stm32l4xx_it_1.o(i.HardFault_Handler)
    IOScanTask                               0x080045d5   Thumb Code     4  main_1.o(i.IOScanTask)
    IOStateChangeCallback                    0x080045d9   Thumb Code    42  main_1.o(i.IOStateChangeCallback)
    KeyScanTask                              0x08004625   Thumb Code     4  main_1.o(i.KeyScanTask)
    MemManage_Handler                        0x08004955   Thumb Code     2  stm32l4xx_it_1.o(i.MemManage_Handler)
    NMI_Handler                              0x08004957   Thumb Code     2  stm32l4xx_it_1.o(i.NMI_Handler)
    PendSV_Handler                           0x0800499d   Thumb Code     2  stm32l4xx_it_1.o(i.PendSV_Handler)
    SRV_SYSTEM_Init                          0x08004cc1   Thumb Code    44  srv_system.o(i.SRV_SYSTEM_Init)
    SRV_SYSTEM_Process                       0x08004cf9   Thumb Code    56  srv_system.o(i.SRV_SYSTEM_Process)
    SRV_TIMER_Create                         0x08004d3d   Thumb Code    52  srv_timer.o(i.SRV_TIMER_Create)
    SRV_TIMER_Init                           0x08004d79   Thumb Code    28  srv_timer.o(i.SRV_TIMER_Init)
    SRV_TIMER_Process                        0x08004d9d   Thumb Code    56  srv_timer.o(i.SRV_TIMER_Process)
    SRV_TIMER_Start                          0x08004ddd   Thumb Code    38  srv_timer.o(i.SRV_TIMER_Start)
    SVC_Handler                              0x08004e0d   Thumb Code     2  stm32l4xx_it_1.o(i.SVC_Handler)
    SysTick_Handler                          0x08004e0f   Thumb Code    14  stm32l4xx_it_1.o(i.SysTick_Handler)
    SystemClock_Config                       0x08004e1d   Thumb Code   162  main_1.o(i.SystemClock_Config)
    SystemInit                               0x08004ec1   Thumb Code    56  system_stm32l4xx_1.o(i.SystemInit)
    UART_AdvFeatureConfig                    0x08004f05   Thumb Code   200  stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08004fcd   Thumb Code    92  stm32l4xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08005029   Thumb Code   586  stm32l4xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x0800529d   Thumb Code    98  stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UsageFault_Handler                       0x080052ff   Thumb Code     2  stm32l4xx_it_1.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08005301   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08005331   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x08005341   Thumb Code   626  main_1.o(i.main)
    _get_lc_numeric                          0x080056ed   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dretinf                            0x08005719   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08005725   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08005725   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800577b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08005807   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800580f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800580f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x08005811   Thumb Code     4  printf1.o(x$fpl$printf1)
    AHBPrescTable                            0x08005814   Data          16  system_stm32l4xx_1.o(.constdata)
    __I$use$fp                               0x08005814   Number         0  usenofp.o(x$fpl$usenofp)
    MSIRangeTable                            0x08005824   Data          48  system_stm32l4xx_1.o(.constdata)
    APBPrescTable                            0x08005854   Data           8  system_stm32l4xx_1.o(.constdata)
    Region$$Table$$Base                      0x080059c4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080059e4   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000008   Data           4  stm32l4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32l4xx_1.o(.data)
    hadc1                                    0x200000e8   Data         104  main_1.o(.bss)
    hi2c1                                    0x20000150   Data          76  main_1.o(.bss)
    hi2c2                                    0x2000019c   Data          76  main_1.o(.bss)
    hlpuart1                                 0x200001e8   Data         120  main_1.o(.bss)
    hspi1                                    0x20000260   Data         100  main_1.o(.bss)
    __libspace_start                         0x200007d8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000838   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x0800018d

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005ae8, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005a00, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x0000018c   Data   RO            3    RESET               startup_stm32l431xx.o
    0x0800018c   0x0800018c   0x00000008   Code   RO         5462  * !!!main             c_w.l(__main.o)
    0x08000194   0x08000194   0x00000034   Code   RO         5778    !!!scatter          c_w.l(__scatter.o)
    0x080001c8   0x080001c8   0x0000001a   Code   RO         5780    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001e2   0x080001e2   0x00000002   PAD
    0x080001e4   0x080001e4   0x0000001c   Code   RO         5782    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000200   0x08000200   0x00000000   Code   RO         5447    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000200   0x08000200   0x00000006   Code   RO         5446    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000206   0x08000206   0x00000006   Code   RO         5445    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800020c   0x0800020c   0x00000006   Code   RO         5444    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000212   0x08000212   0x00000006   Code   RO         5443    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000218   0x08000218   0x00000004   Code   RO         5524    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800021c   0x0800021c   0x00000002   Code   RO         5715    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800021e   0x0800021e   0x00000004   Code   RO         5527    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         5530    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000222   0x08000222   0x00000008   Code   RO         5531    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         5533    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         5535    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000000   Code   RO         5537    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         5538    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         5540    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         5542    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         5544    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x0000000a   Code   RO         5545    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5546    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5548    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5550    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5552    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5554    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5556    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5558    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5560    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5564    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5566    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5568    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5570    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000002   Code   RO         5571    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000002   Code   RO         5775    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5731    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5733    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5735    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5738    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5741    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5743    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5746    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000002   Code   RO         5747    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         5482    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000240   0x08000240   0x00000000   Code   RO         5602    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000240   0x08000240   0x00000006   Code   RO         5614    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000246   0x08000246   0x00000000   Code   RO         5604    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000246   0x08000246   0x00000004   Code   RO         5605    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800024a   0x0800024a   0x00000000   Code   RO         5607    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800024a   0x0800024a   0x00000008   Code   RO         5608    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000252   0x08000252   0x00000002   Code   RO         5718    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000254   0x08000254   0x00000000   Code   RO         5751    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000254   0x08000254   0x00000004   Code   RO         5752    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000258   0x08000258   0x00000006   Code   RO         5753    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x00000000   Code   RO         5630    .emb_text           c_w.l(maybetermalloc1.o)
    0x08000260   0x08000260   0x00000040   Code   RO            4    .text               startup_stm32l431xx.o
    0x080002a0   0x080002a0   0x0000005e   Code   RO         5330    .text               c_w.l(h1_alloc.o)
    0x080002fe   0x080002fe   0x0000004e   Code   RO         5332    .text               c_w.l(h1_free.o)
    0x0800034c   0x0800034c   0x000000ee   Code   RO         5388    .text               c_w.l(lludivv7m.o)
    0x0800043a   0x0800043a   0x00000002   PAD
    0x0800043c   0x0800043c   0x00000028   Code   RO         5392    .text               c_w.l(noretval__2sprintf.o)
    0x08000464   0x08000464   0x0000004e   Code   RO         5396    .text               c_w.l(_printf_pad.o)
    0x080004b2   0x080004b2   0x00000052   Code   RO         5398    .text               c_w.l(_printf_str.o)
    0x08000504   0x08000504   0x00000078   Code   RO         5400    .text               c_w.l(_printf_dec.o)
    0x0800057c   0x0800057c   0x00000058   Code   RO         5405    .text               c_w.l(_printf_hex_int.o)
    0x080005d4   0x080005d4   0x0000013e   Code   RO         5435    .text               c_w.l(__printf_flags_wp.o)
    0x08000712   0x08000712   0x00000048   Code   RO         5448    .text               c_w.l(strcpy.o)
    0x0800075a   0x0800075a   0x0000003e   Code   RO         5450    .text               c_w.l(strlen.o)
    0x08000798   0x08000798   0x0000008a   Code   RO         5452    .text               c_w.l(rt_memcpy_v6.o)
    0x08000822   0x08000822   0x00000064   Code   RO         5454    .text               c_w.l(rt_memcpy_w.o)
    0x08000886   0x08000886   0x00000044   Code   RO         5456    .text               c_w.l(rt_memclr.o)
    0x080008ca   0x080008ca   0x0000004e   Code   RO         5458    .text               c_w.l(rt_memclr_w.o)
    0x08000918   0x08000918   0x00000006   Code   RO         5460    .text               c_w.l(heapauxi.o)
    0x0800091e   0x0800091e   0x00000002   PAD
    0x08000920   0x08000920   0x00000008   Code   RO         5490    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08000928   0x08000928   0x00000004   Code   RO         5492    .text               c_w.l(hguard.o)
    0x0800092c   0x0800092c   0x0000008a   Code   RO         5494    .text               c_w.l(init_alloc.o)
    0x080009b6   0x080009b6   0x0000000e   Code   RO         5498    .text               c_w.l(h1_init.o)
    0x080009c4   0x080009c4   0x000000b2   Code   RO         5514    .text               c_w.l(_printf_intcommon.o)
    0x08000a76   0x08000a76   0x00000424   Code   RO         5516    .text               c_w.l(_printf_fp_dec.o)
    0x08000e9a   0x08000e9a   0x00000002   PAD
    0x08000e9c   0x08000e9c   0x00000030   Code   RO         5518    .text               c_w.l(_printf_char_common.o)
    0x08000ecc   0x08000ecc   0x0000000a   Code   RO         5520    .text               c_w.l(_sputc.o)
    0x08000ed6   0x08000ed6   0x0000002c   Code   RO         5522    .text               c_w.l(_printf_char.o)
    0x08000f02   0x08000f02   0x00000002   PAD
    0x08000f04   0x08000f04   0x00000008   Code   RO         5598    .text               c_w.l(libspace.o)
    0x08000f0c   0x08000f0c   0x00000008   Code   RO         5621    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000f14   0x08000f14   0x00000034   Code   RO         5632    .text               c_w.l(h1_extend.o)
    0x08000f48   0x08000f48   0x0000008a   Code   RO         5636    .text               c_w.l(lludiv10.o)
    0x08000fd2   0x08000fd2   0x00000002   PAD
    0x08000fd4   0x08000fd4   0x00000080   Code   RO         5638    .text               c_w.l(_printf_fp_infnan.o)
    0x08001054   0x08001054   0x000000e4   Code   RO         5642    .text               c_w.l(bigflt0.o)
    0x08001138   0x08001138   0x0000000e   Code   RO         5670    .text               c_w.l(defsig_rtmem_outer.o)
    0x08001146   0x08001146   0x00000002   Code   RO         5685    .text               c_w.l(use_no_semi.o)
    0x08001148   0x08001148   0x00000000   Code   RO         5687    .text               c_w.l(indicate_semi.o)
    0x08001148   0x08001148   0x0000004a   Code   RO         5688    .text               c_w.l(sys_stackheap_outer.o)
    0x08001192   0x08001192   0x00000012   Code   RO         5694    .text               c_w.l(exit.o)
    0x080011a4   0x080011a4   0x0000000a   Code   RO         5696    .text               c_w.l(defsig_exit.o)
    0x080011ae   0x080011ae   0x00000002   PAD
    0x080011b0   0x080011b0   0x00000050   Code   RO         5702    .text               c_w.l(defsig_rtmem_inner.o)
    0x08001200   0x08001200   0x00000080   Code   RO         5704    .text               c_w.l(strcmpv7m.o)
    0x08001280   0x08001280   0x0000000c   Code   RO         5716    .text               c_w.l(sys_exit.o)
    0x0800128c   0x0800128c   0x00000032   Code   RO         5726    .text               c_w.l(defsig_general.o)
    0x080012be   0x080012be   0x0000000e   Code   RO         5748    .text               c_w.l(sys_wrch.o)
    0x080012cc   0x080012cc   0x0000003e   Code   RO         5645    CL$$btod_d2e        c_w.l(btod.o)
    0x0800130a   0x0800130a   0x00000046   Code   RO         5647    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001350   0x08001350   0x00000060   Code   RO         5646    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080013b0   0x080013b0   0x00000338   Code   RO         5655    CL$$btod_div_common  c_w.l(btod.o)
    0x080016e8   0x080016e8   0x000000dc   Code   RO         5652    CL$$btod_e2e        c_w.l(btod.o)
    0x080017c4   0x080017c4   0x0000002a   Code   RO         5649    CL$$btod_ediv       c_w.l(btod.o)
    0x080017ee   0x080017ee   0x0000002a   Code   RO         5648    CL$$btod_emul       c_w.l(btod.o)
    0x08001818   0x08001818   0x00000244   Code   RO         5654    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001a5c   0x08001a5c   0x00000068   Code   RO          433    i.ADC_Disable       stm32l4xx_hal_adc.o
    0x08001ac4   0x08001ac4   0x00000040   Code   RO         4838    i.ADS1115_ReadRegister  drv_ads1115.o
    0x08001b04   0x08001b04   0x00000018   Code   RO         4559    i.APP_ADC_Init      app_adc.o
    0x08001b1c   0x08001b1c   0x00000002   Code   RO         4560    i.APP_ADC_Process   app_adc.o
    0x08001b1e   0x08001b1e   0x00000002   PAD
    0x08001b20   0x08001b20   0x00000038   Code   RO         4597    i.APP_DISPLAY_Init  app_display.o
    0x08001b58   0x08001b58   0x0000006c   Code   RO         4598    i.APP_DISPLAY_Process  app_display.o
    0x08001bc4   0x08001bc4   0x00000050   Code   RO         4601    i.APP_DISPLAY_ShowRunningStatus  app_display.o
    0x08001c14   0x08001c14   0x00000024   Code   RO         4602    i.APP_DISPLAY_UpdateData  app_display.o
    0x08001c38   0x08001c38   0x00000018   Code   RO         4743    i.APP_PROTOCOL_Init  app_protocol.o
    0x08001c50   0x08001c50   0x00000002   Code   RO         4744    i.APP_PROTOCOL_Process  app_protocol.o
    0x08001c52   0x08001c52   0x00000002   PAD
    0x08001c54   0x08001c54   0x0000001c   Code   RO         4304    i.BSP_IO_GetState   bsp_io.o
    0x08001c70   0x08001c70   0x00000004   Code   RO         4305    i.BSP_IO_Init       bsp_io.o
    0x08001c74   0x08001c74   0x00000018   Code   RO         4306    i.BSP_IO_RegisterCallback  bsp_io.o
    0x08001c8c   0x08001c8c   0x00000048   Code   RO         4307    i.BSP_IO_Scan       bsp_io.o
    0x08001cd4   0x08001cd4   0x00000018   Code   RO         4354    i.BSP_KEY_GetState  bsp_key.o
    0x08001cec   0x08001cec   0x00000004   Code   RO         4355    i.BSP_KEY_Init      bsp_key.o
    0x08001cf0   0x08001cf0   0x00000048   Code   RO         4356    i.BSP_KEY_Scan      bsp_key.o
    0x08001d38   0x08001d38   0x00000004   Code   RO         4392    i.BSP_LED_Init      bsp_led.o
    0x08001d3c   0x08001d3c   0x00000038   Code   RO         4395    i.BSP_LED_Process   bsp_led.o
    0x08001d74   0x08001d74   0x00000020   Code   RO         4396    i.BSP_LED_SetBlink  bsp_led.o
    0x08001d94   0x08001d94   0x00000018   Code   RO         4397    i.BSP_LED_Toggle    bsp_led.o
    0x08001dac   0x08001dac   0x00000018   Code   RO         4448    i.BSP_RELAY_GetState  bsp_relay.o
    0x08001dc4   0x08001dc4   0x00000004   Code   RO         4449    i.BSP_RELAY_Init    bsp_relay.o
    0x08001dc8   0x08001dc8   0x00000002   Code   RO         4452    i.BSP_RELAY_Process  bsp_relay.o
    0x08001dca   0x08001dca   0x00000002   PAD
    0x08001dcc   0x08001dcc   0x0000000c   Code   RO         4521    i.BSP_UART_Init     bsp_uart.o
    0x08001dd8   0x08001dd8   0x00000024   Code   RO         4523    i.BSP_UART_SendData  bsp_uart.o
    0x08001dfc   0x08001dfc   0x00000002   Code   RO          270    i.BusFault_Handler  stm32l4xx_it_1.o
    0x08001dfe   0x08001dfe   0x00000002   PAD
    0x08001e00   0x08001e00   0x00000024   Code   RO         4839    i.DRV_ADS1115_Init  drv_ads1115.o
    0x08001e24   0x08001e24   0x00000068   Code   RO         4899    i.DRV_DS18B20_Init  drv_ds18b20.o
    0x08001e8c   0x08001e8c   0x00000004   Code   RO         4900    i.DRV_DS18B20_IsPresent  drv_ds18b20.o
    0x08001e90   0x08001e90   0x00000054   Code   RO         4902    i.DRV_DS18B20_SetResolution  drv_ds18b20.o
    0x08001ee4   0x08001ee4   0x00000074   Code   RO         4984    i.DRV_INA226_Init   drv_ina226.o
    0x08001f58   0x08001f58   0x00000040   Code   RO         4990    i.DRV_INA226_SetCalibration  drv_ina226.o
    0x08001f98   0x08001f98   0x00000032   Code   RO         5057    i.DRV_MAX31856_Config  drv_max31856.o
    0x08001fca   0x08001fca   0x00000002   PAD
    0x08001fcc   0x08001fcc   0x000000d0   Code   RO         5058    i.DRV_MAX31856_Init  drv_max31856.o
    0x0800209c   0x0800209c   0x00000014   Code   RO         5133    i.DRV_OLED_Clear    drv_oled.o
    0x080020b0   0x080020b0   0x0000003c   Code   RO         5136    i.DRV_OLED_DrawFloat  drv_oled.o
    0x080020ec   0x080020ec   0x00000034   Code   RO         5138    i.DRV_OLED_DrawPixel  drv_oled.o
    0x08002120   0x08002120   0x000000b2   Code   RO         5139    i.DRV_OLED_DrawString  drv_oled.o
    0x080021d2   0x080021d2   0x00000002   PAD
    0x080021d4   0x080021d4   0x000000f0   Code   RO         5140    i.DRV_OLED_Init     drv_oled.o
    0x080022c4   0x080022c4   0x0000007c   Code   RO         5142    i.DRV_OLED_Update   drv_oled.o
    0x08002340   0x08002340   0x00000028   Code   RO         4904    i.DS18B20_DelayUs   drv_ds18b20.o
    0x08002368   0x08002368   0x00000014   Code   RO         4905    i.DS18B20_ReadPin   drv_ds18b20.o
    0x0800237c   0x0800237c   0x0000003c   Code   RO         4906    i.DS18B20_Reset     drv_ds18b20.o
    0x080023b8   0x080023b8   0x00000034   Code   RO         4907    i.DS18B20_SetPinInput  drv_ds18b20.o
    0x080023ec   0x080023ec   0x00000038   Code   RO         4908    i.DS18B20_SetPinOutput  drv_ds18b20.o
    0x08002424   0x08002424   0x00000050   Code   RO         4909    i.DS18B20_WriteByte  drv_ds18b20.o
    0x08002474   0x08002474   0x00000018   Code   RO         4910    i.DS18B20_WritePin  drv_ds18b20.o
    0x0800248c   0x0800248c   0x00000002   Code   RO          271    i.DebugMon_Handler  stm32l4xx_it_1.o
    0x0800248e   0x0800248e   0x00000002   PAD
    0x08002490   0x08002490   0x000000c4   Code   RO         4603    i.DisplayMainPage   app_display.o
    0x08002554   0x08002554   0x000000a4   Code   RO         4604    i.DisplaySensorPage  app_display.o
    0x080025f8   0x080025f8   0x000000c0   Code   RO         4605    i.DisplayStatusPage  app_display.o
    0x080026b8   0x080026b8   0x00000006   Code   RO          272    i.EXTI3_IRQHandler  stm32l4xx_it_1.o
    0x080026be   0x080026be   0x00000006   Code   RO          273    i.EXTI4_IRQHandler  stm32l4xx_it_1.o
    0x080026c4   0x080026c4   0x0000008a   Code   RO          650    i.HAL_ADCEx_Calibration_Start  stm32l4xx_hal_adc_ex.o
    0x0800274e   0x0800274e   0x00000002   PAD
    0x08002750   0x08002750   0x00000410   Code   RO          436    i.HAL_ADC_ConfigChannel  stm32l4xx_hal_adc.o
    0x08002b60   0x08002b60   0x000001a0   Code   RO          445    i.HAL_ADC_Init      stm32l4xx_hal_adc.o
    0x08002d00   0x08002d00   0x00000044   Code   RO          358    i.HAL_ADC_MspInit   stm32l4xx_hal_msp_1.o
    0x08002d44   0x08002d44   0x0000001c   Code   RO          828    i.HAL_Delay         stm32l4xx_hal.o
    0x08002d60   0x08002d60   0x00000002   Code   RO         2000    i.HAL_GPIO_EXTI_Callback  stm32l4xx_hal_gpio.o
    0x08002d62   0x08002d62   0x00000002   PAD
    0x08002d64   0x08002d64   0x00000018   Code   RO         2001    i.HAL_GPIO_EXTI_IRQHandler  stm32l4xx_hal_gpio.o
    0x08002d7c   0x08002d7c   0x00000196   Code   RO         2002    i.HAL_GPIO_Init     stm32l4xx_hal_gpio.o
    0x08002f12   0x08002f12   0x0000000a   Code   RO         2004    i.HAL_GPIO_ReadPin  stm32l4xx_hal_gpio.o
    0x08002f1c   0x08002f1c   0x00000008   Code   RO         2005    i.HAL_GPIO_TogglePin  stm32l4xx_hal_gpio.o
    0x08002f24   0x08002f24   0x0000000a   Code   RO         2006    i.HAL_GPIO_WritePin  stm32l4xx_hal_gpio.o
    0x08002f2e   0x08002f2e   0x00000002   PAD
    0x08002f30   0x08002f30   0x0000000c   Code   RO          832    i.HAL_GetTick       stm32l4xx_hal.o
    0x08002f3c   0x08002f3c   0x00000056   Code   RO         1462    i.HAL_I2CEx_ConfigAnalogFilter  stm32l4xx_hal_i2c_ex.o
    0x08002f92   0x08002f92   0x00000052   Code   RO         1463    i.HAL_I2CEx_ConfigDigitalFilter  stm32l4xx_hal_i2c_ex.o
    0x08002fe4   0x08002fe4   0x000000b8   Code   RO         1045    i.HAL_I2C_Init      stm32l4xx_hal_i2c.o
    0x0800309c   0x0800309c   0x00000136   Code   RO         1051    i.HAL_I2C_Master_Receive  stm32l4xx_hal_i2c.o
    0x080031d2   0x080031d2   0x00000002   PAD
    0x080031d4   0x080031d4   0x00000132   Code   RO         1056    i.HAL_I2C_Master_Transmit  stm32l4xx_hal_i2c.o
    0x08003306   0x08003306   0x00000002   PAD
    0x08003308   0x08003308   0x00000078   Code   RO          360    i.HAL_I2C_MspInit   stm32l4xx_hal_msp_1.o
    0x08003380   0x08003380   0x00000010   Code   RO          836    i.HAL_IncTick       stm32l4xx_hal.o
    0x08003390   0x08003390   0x00000016   Code   RO          837    i.HAL_Init          stm32l4xx_hal.o
    0x080033a6   0x080033a6   0x00000002   PAD
    0x080033a8   0x080033a8   0x00000028   Code   RO          838    i.HAL_InitTick      stm32l4xx_hal.o
    0x080033d0   0x080033d0   0x00000080   Code   RO          361    i.HAL_MspInit       stm32l4xx_hal_msp_1.o
    0x08003450   0x08003450   0x00000040   Code   RO         2489    i.HAL_NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x08003490   0x08003490   0x00000024   Code   RO         2490    i.HAL_NVIC_SetPriorityGrouping  stm32l4xx_hal_cortex.o
    0x080034b4   0x080034b4   0x00000064   Code   RO         2293    i.HAL_PWREx_ControlVoltageScaling  stm32l4xx_hal_pwr_ex.o
    0x08003518   0x08003518   0x00000010   Code   RO         2316    i.HAL_PWREx_GetVoltageRange  stm32l4xx_hal_pwr_ex.o
    0x08003528   0x08003528   0x000002b2   Code   RO         1639    i.HAL_RCCEx_PeriphCLKConfig  stm32l4xx_hal_rcc_ex.o
    0x080037da   0x080037da   0x00000002   PAD
    0x080037dc   0x080037dc   0x00000170   Code   RO         1517    i.HAL_RCC_ClockConfig  stm32l4xx_hal_rcc.o
    0x0800394c   0x0800394c   0x0000000c   Code   RO         1521    i.HAL_RCC_GetHCLKFreq  stm32l4xx_hal_rcc.o
    0x08003958   0x08003958   0x00000020   Code   RO         1523    i.HAL_RCC_GetPCLK1Freq  stm32l4xx_hal_rcc.o
    0x08003978   0x08003978   0x00000020   Code   RO         1524    i.HAL_RCC_GetPCLK2Freq  stm32l4xx_hal_rcc.o
    0x08003998   0x08003998   0x000000c4   Code   RO         1525    i.HAL_RCC_GetSysClockFreq  stm32l4xx_hal_rcc.o
    0x08003a5c   0x08003a5c   0x000004a8   Code   RO         1528    i.HAL_RCC_OscConfig  stm32l4xx_hal_rcc.o
    0x08003f04   0x08003f04   0x000000ac   Code   RO         3034    i.HAL_SPI_Init      stm32l4xx_hal_spi.o
    0x08003fb0   0x08003fb0   0x00000048   Code   RO          363    i.HAL_SPI_MspInit   stm32l4xx_hal_msp_1.o
    0x08003ff8   0x08003ff8   0x00000178   Code   RO         3042    i.HAL_SPI_Transmit  stm32l4xx_hal_spi.o
    0x08004170   0x08004170   0x00000018   Code   RO         2492    i.HAL_SYSTICK_CLKSourceConfig  stm32l4xx_hal_cortex.o
    0x08004188   0x08004188   0x00000002   Code   RO         2493    i.HAL_SYSTICK_Callback  stm32l4xx_hal_cortex.o
    0x0800418a   0x0800418a   0x00000028   Code   RO         2494    i.HAL_SYSTICK_Config  stm32l4xx_hal_cortex.o
    0x080041b2   0x080041b2   0x00000008   Code   RO         2495    i.HAL_SYSTICK_IRQHandler  stm32l4xx_hal_cortex.o
    0x080041ba   0x080041ba   0x0000006a   Code   RO         2639    i.HAL_UART_Init     stm32l4xx_hal_uart.o
    0x08004224   0x08004224   0x00000048   Code   RO          365    i.HAL_UART_MspInit  stm32l4xx_hal_msp_1.o
    0x0800426c   0x0800426c   0x000000b0   Code   RO         2647    i.HAL_UART_Transmit  stm32l4xx_hal_uart.o
    0x0800431c   0x0800431c   0x00000002   Code   RO          274    i.HardFault_Handler  stm32l4xx_it_1.o
    0x0800431e   0x0800431e   0x00000022   Code   RO         1087    i.I2C_Flush_TXDR    stm32l4xx_hal_i2c.o
    0x08004340   0x08004340   0x00000080   Code   RO         1095    i.I2C_IsAcknowledgeFailed  stm32l4xx_hal_i2c.o
    0x080043c0   0x080043c0   0x0000002c   Code   RO         1102    i.I2C_TransferConfig  stm32l4xx_hal_i2c.o
    0x080043ec   0x080043ec   0x00000052   Code   RO         1103    i.I2C_WaitOnFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x0800443e   0x0800443e   0x00000002   PAD
    0x08004440   0x08004440   0x0000008c   Code   RO         1104    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x080044cc   0x080044cc   0x0000004e   Code   RO         1105    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x0800451a   0x0800451a   0x00000052   Code   RO         1106    i.I2C_WaitOnTXISFlagUntilTimeout  stm32l4xx_hal_i2c.o
    0x0800456c   0x0800456c   0x00000040   Code   RO         4991    i.INA226_ReadRegister  drv_ina226.o
    0x080045ac   0x080045ac   0x00000028   Code   RO         4992    i.INA226_WriteRegister  drv_ina226.o
    0x080045d4   0x080045d4   0x00000004   Code   RO           13    i.IOScanTask        main_1.o
    0x080045d8   0x080045d8   0x0000004c   Code   RO           14    i.IOStateChangeCallback  main_1.o
    0x08004624   0x08004624   0x00000004   Code   RO           15    i.KeyScanTask       main_1.o
    0x08004628   0x08004628   0x0000000c   Code   RO          458    i.LL_ADC_GetOffsetChannel  stm32l4xx_hal_adc.o
    0x08004634   0x08004634   0x00000008   Code   RO          459    i.LL_ADC_REG_IsConversionOngoing  stm32l4xx_hal_adc.o
    0x0800463c   0x0800463c   0x00000046   Code   RO          461    i.LL_ADC_SetChannelSamplingTime  stm32l4xx_hal_adc.o
    0x08004682   0x08004682   0x00000010   Code   RO          462    i.LL_ADC_SetOffsetState  stm32l4xx_hal_adc.o
    0x08004692   0x08004692   0x00000002   PAD
    0x08004694   0x08004694   0x0000001c   Code   RO         5062    i.MAX31856_CS_Control  drv_max31856.o
    0x080046b0   0x080046b0   0x00000038   Code   RO         5065    i.MAX31856_WriteRegister  drv_max31856.o
    0x080046e8   0x080046e8   0x0000005c   Code   RO           16    i.MX_ADC1_Init      main_1.o
    0x08004744   0x08004744   0x00000118   Code   RO           17    i.MX_GPIO_Init      main_1.o
    0x0800485c   0x0800485c   0x00000044   Code   RO           18    i.MX_I2C1_Init      main_1.o
    0x080048a0   0x080048a0   0x00000044   Code   RO           19    i.MX_I2C2_Init      main_1.o
    0x080048e4   0x080048e4   0x00000030   Code   RO           20    i.MX_LPUART1_UART_Init  main_1.o
    0x08004914   0x08004914   0x00000040   Code   RO           21    i.MX_SPI1_Init      main_1.o
    0x08004954   0x08004954   0x00000002   Code   RO          275    i.MemManage_Handler  stm32l4xx_it_1.o
    0x08004956   0x08004956   0x00000002   Code   RO          276    i.NMI_Handler       stm32l4xx_it_1.o
    0x08004958   0x08004958   0x00000020   Code   RO         2496    i.NVIC_SetPriority  stm32l4xx_hal_cortex.o
    0x08004978   0x08004978   0x00000024   Code   RO         5143    i.OLED_WriteCommand  drv_oled.o
    0x0800499c   0x0800499c   0x00000002   Code   RO          277    i.PendSV_Handler    stm32l4xx_it_1.o
    0x0800499e   0x0800499e   0x00000002   PAD
    0x080049a0   0x080049a0   0x00000128   Code   RO         1642    i.RCCEx_PLLSAI1_Config  stm32l4xx_hal_rcc_ex.o
    0x08004ac8   0x08004ac8   0x0000007c   Code   RO         1529    i.RCC_SetFlashLatencyFromMSIRange  stm32l4xx_hal_rcc.o
    0x08004b44   0x08004b44   0x00000048   Code   RO         3072    i.SPI_EndRxTxTransaction  stm32l4xx_hal_spi.o
    0x08004b8c   0x08004b8c   0x0000009e   Code   RO         3077    i.SPI_WaitFifoStateUntilTimeout  stm32l4xx_hal_spi.o
    0x08004c2a   0x08004c2a   0x00000094   Code   RO         3078    i.SPI_WaitFlagStateUntilTimeout  stm32l4xx_hal_spi.o
    0x08004cbe   0x08004cbe   0x00000002   PAD
    0x08004cc0   0x08004cc0   0x00000038   Code   RO         5221    i.SRV_SYSTEM_Init   srv_system.o
    0x08004cf8   0x08004cf8   0x00000044   Code   RO         5222    i.SRV_SYSTEM_Process  srv_system.o
    0x08004d3c   0x08004d3c   0x0000003c   Code   RO         5276    i.SRV_TIMER_Create  srv_timer.o
    0x08004d78   0x08004d78   0x00000024   Code   RO         5277    i.SRV_TIMER_Init    srv_timer.o
    0x08004d9c   0x08004d9c   0x00000040   Code   RO         5278    i.SRV_TIMER_Process  srv_timer.o
    0x08004ddc   0x08004ddc   0x00000030   Code   RO         5279    i.SRV_TIMER_Start   srv_timer.o
    0x08004e0c   0x08004e0c   0x00000002   Code   RO          278    i.SVC_Handler       stm32l4xx_it_1.o
    0x08004e0e   0x08004e0e   0x0000000e   Code   RO          279    i.SysTick_Handler   stm32l4xx_it_1.o
    0x08004e1c   0x08004e1c   0x000000a2   Code   RO           22    i.SystemClock_Config  main_1.o
    0x08004ebe   0x08004ebe   0x00000002   PAD
    0x08004ec0   0x08004ec0   0x00000044   Code   RO         4267    i.SystemInit        system_stm32l4xx_1.o
    0x08004f04   0x08004f04   0x000000c8   Code   RO         2652    i.UART_AdvFeatureConfig  stm32l4xx_hal_uart.o
    0x08004fcc   0x08004fcc   0x0000005c   Code   RO         2653    i.UART_CheckIdleState  stm32l4xx_hal_uart.o
    0x08005028   0x08005028   0x00000274   Code   RO         2668    i.UART_SetConfig    stm32l4xx_hal_uart.o
    0x0800529c   0x0800529c   0x00000062   Code   RO         2671    i.UART_WaitOnFlagUntilTimeout  stm32l4xx_hal_uart.o
    0x080052fe   0x080052fe   0x00000002   Code   RO          280    i.UsageFault_Handler  stm32l4xx_it_1.o
    0x08005300   0x08005300   0x00000030   Code   RO         5683    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08005330   0x08005330   0x0000000e   Code   RO         5433    i._is_digit         c_w.l(__printf_wp.o)
    0x0800533e   0x0800533e   0x00000002   PAD
    0x08005340   0x08005340   0x000003ac   Code   RO           24    i.main              main_1.o
    0x080056ec   0x080056ec   0x0000002c   Code   RO         5668    locale$$code        c_w.l(lc_numeric_c.o)
    0x08005718   0x08005718   0x0000000c   Code   RO         5572    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08005724   0x08005724   0x00000056   Code   RO         5464    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800577a   0x0800577a   0x0000008c   Code   RO         5574    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08005806   0x08005806   0x0000000a   Code   RO         5679    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08005810   0x08005810   0x00000004   Code   RO         5466    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08005814   0x08005814   0x00000000   Code   RO         5576    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08005814   0x08005814   0x00000040   Data   RO         4268    .constdata          system_stm32l4xx_1.o
    0x08005854   0x08005854   0x00000008   Data   RO         4269    .constdata          system_stm32l4xx_1.o
    0x0800585c   0x0800585c   0x00000010   Data   RO         4911    .constdata          drv_ds18b20.o
    0x0800586c   0x0800586c   0x00000010   Data   RO         5066    .constdata          drv_max31856.o
    0x0800587c   0x0800587c   0x00000078   Data   RO         5145    .constdata          drv_oled.o
    0x080058f4   0x080058f4   0x00000028   Data   RO         5406    .constdata          c_w.l(_printf_hex_int.o)
    0x0800591c   0x0800591c   0x00000011   Data   RO         5436    .constdata          c_w.l(__printf_flags_wp.o)
    0x0800592d   0x0800592d   0x00000003   PAD
    0x08005930   0x08005930   0x00000094   Data   RO         5643    .constdata          c_w.l(bigflt0.o)
    0x080059c4   0x080059c4   0x00000020   Data   RO         5776    Region$$Table       anon$$obj.o
    0x080059e4   0x080059e4   0x0000001c   Data   RO         5667    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08005ae8, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005a00, Size: 0x00000e38, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08005a00   0x00000008   Data   RW           26    .data               main_1.o
    0x20000008   0x08005a08   0x00000004   Data   RW          853    .data               stm32l4xx_hal.o
    0x2000000c   0x08005a0c   0x00000004   Data   RW         4270    .data               system_stm32l4xx_1.o
    0x20000010   0x08005a10   0x00000018   Data   RW         4309    .data               bsp_io.o
    0x20000028   0x08005a28   0x00000040   Data   RW         4357    .data               bsp_key.o
    0x20000068   0x08005a68   0x00000020   Data   RW         4398    .data               bsp_led.o
    0x20000088   0x08005a88   0x00000010   Data   RW         4457    .data               bsp_relay.o
    0x20000098   0x08005a98   0x00000004   Data   RW         4524    .data               bsp_uart.o
    0x2000009c   0x08005a9c   0x0000000c   Data   RW         4562    .data               app_adc.o
    0x200000a8   0x08005aa8   0x0000000c   Data   RW         4607    .data               app_display.o
    0x200000b4   0x08005ab4   0x0000000c   Data   RW         4757    .data               app_protocol.o
    0x200000c0   0x08005ac0   0x00000008   Data   RW         4846    .data               drv_ads1115.o
    0x200000c8   0x08005ac8   0x0000000c   Data   RW         4993    .data               drv_ina226.o
    0x200000d4   0x08005ad4   0x00000004   Data   RW         5067    .data               drv_max31856.o
    0x200000d8   0x08005ad8   0x00000004   Data   RW         5146    .data               drv_oled.o
    0x200000dc   0x08005adc   0x00000008   Data   RW         5226    .data               srv_system.o
    0x200000e4   0x08005ae4   0x00000001   Data   RW         5282    .data               srv_timer.o
    0x200000e5   0x08005ae5   0x00000003   PAD
    0x200000e8        -       0x000001dc   Zero   RW           25    .bss                main_1.o
    0x200002c4        -       0x00000058   Zero   RW         4606    .bss                app_display.o
    0x2000031c        -       0x00000400   Zero   RW         5144    .bss                drv_oled.o
    0x2000071c        -       0x0000001c   Zero   RW         5225    .bss                srv_system.o
    0x20000738        -       0x000000a0   Zero   RW         5281    .bss                srv_timer.o
    0x200007d8        -       0x00000060   Zero   RW         5599    .bss                c_w.l(libspace.o)
    0x20000838        -       0x00000200   Zero   RW            2    HEAP                startup_stm32l431xx.o
    0x20000a38        -       0x00000400   Zero   RW            1    STACK               startup_stm32l431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        26          4          0         12          0       1477   app_adc.o
       832        202          0         12         88       4782   app_display.o
        26          4          0         12          0       3898   app_protocol.o
       128         18          0         24          0       2824   bsp_io.o
       100         12          0         64          0       2277   bsp_key.o
       116         14          0         32          0       2677   bsp_led.o
        30          6          0         16          0       2183   bsp_relay.o
        48         10          0          4          0       1628   bsp_uart.o
       100         10          0          8          0       2285   drv_ads1115.o
       524         46         16          0          0       6795   drv_ds18b20.o
       284         32          0         12          0       3266   drv_ina226.o
       342         26         16          4          0       3358   drv_max31856.o
       710         48        120          4       1024       5998   drv_oled.o
      1806        426          0          8        476     694611   main_1.o
       124         24          0          8         28       1375   srv_system.o
       208         34          0          1        160       3098   srv_timer.o
        64         26        396          0       1536        844   startup_stm32l431xx.o
       118         18          0          4          0       4147   stm32l4xx_hal.o
      1666         76          0          0          0      57249   stm32l4xx_hal_adc.o
       138          0          0          0          0       1582   stm32l4xx_hal_adc_ex.o
       206         14          0          0          0      31440   stm32l4xx_hal_cortex.o
       460         36          0          0          0       5064   stm32l4xx_hal_gpio.o
      1388         38          0          0          0      10970   stm32l4xx_hal_i2c.o
       168          0          0          0          0       1911   stm32l4xx_hal_i2c_ex.o
       460         56          0          0          0       3635   stm32l4xx_hal_msp_1.o
       116         20          0          0          0       1362   stm32l4xx_hal_pwr_ex.o
      1956         94          0          0          0       7440   stm32l4xx_hal_rcc.o
       986         28          0          0          0       3072   stm32l4xx_hal_rcc_ex.o
       926          0          0          0          0       5246   stm32l4xx_hal_spi.o
      1300         62          0          0          0       6417   stm32l4xx_hal_uart.o
        42          0          0          0          0       5032   stm32l4xx_it_1.o
        68         12         72          4          0       1213   system_stm32l4xx_1.o

    ----------------------------------------------------------------------
     15504       <USER>        <GROUP>        232       3312     889156   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       318          4         17          0          0         92   __printf_flags_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1060          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        18          0          0          0          0         80   exit.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
         0          0          0          0          0          0   maybetermalloc1.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      6648        <USER>        <GROUP>          0         96       5276   Library Totals
        18          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6330        276        233          0         96       4540   c_w.l
       252          8          0          0          0        612   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      6648        <USER>        <GROUP>          0         96       5276   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     22152       1680        888        232       3408     877232   Grand Totals
     22152       1680        888        232       3408     877232   ELF Image Totals
     22152       1680        888        232          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                23040 (  22.50kB)
    Total RW  Size (RW Data + ZI Data)              3640 (   3.55kB)
    Total ROM Size (Code + RO Data + RW Data)      23272 (  22.73kB)

==============================================================================

