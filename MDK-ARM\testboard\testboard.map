Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32l431xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(RESET) refers to startup_stm32l431xx.o(STACK) for __initial_sp
    startup_stm32l431xx.o(RESET) refers to startup_stm32l431xx.o(.text) for Reset_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.NMI_Handler) for NMI_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.SVC_Handler) for SVC_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32l431xx.o(RESET) refers to stm32l4xx_it_1.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32l431xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32l431xx.o(.text) refers to system_stm32l4xx_1.o(i.SystemInit) for SystemInit
    startup_stm32l431xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32l431xx.o(.text) refers to startup_stm32l431xx.o(HEAP) for Heap_Mem
    startup_stm32l431xx.o(.text) refers to startup_stm32l431xx.o(STACK) for Stack_Mem
    main_1.o(i.IOScanTask) refers to bsp_io.o(i.BSP_IO_Scan) for BSP_IO_Scan
    main_1.o(i.IOStateChangeCallback) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main_1.o(i.IOStateChangeCallback) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main_1.o(i.IOStateChangeCallback) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main_1.o(i.IOStateChangeCallback) refers to _printf_dec.o(.text) for _printf_int_dec
    main_1.o(i.IOStateChangeCallback) refers to _printf_str.o(.text) for _printf_str
    main_1.o(i.IOStateChangeCallback) refers to noretval__2sprintf.o(.text) for __2sprintf
    main_1.o(i.IOStateChangeCallback) refers to strlen.o(.text) for strlen
    main_1.o(i.IOStateChangeCallback) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    main_1.o(i.KeyScanTask) refers to bsp_key.o(i.BSP_KEY_Scan) for BSP_KEY_Scan
    main_1.o(i.MX_ADC1_Init) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    main_1.o(i.MX_ADC1_Init) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    main_1.o(i.MX_ADC1_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main_1.o(i.MX_GPIO_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main_1.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    main_1.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    main_1.o(i.MX_I2C1_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    main_1.o(i.MX_I2C1_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    main_1.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    main_1.o(i.MX_I2C2_Init) refers to stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    main_1.o(i.MX_I2C2_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_LPUART1_UART_Init) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    main_1.o(i.MX_LPUART1_UART_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.MX_SPI1_Init) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    main_1.o(i.MX_SPI1_Init) refers to main_1.o(.bss) for .bss
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    main_1.o(i.SystemClock_Config) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    main_1.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main_1.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main_1.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main_1.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Init) for HAL_Init
    main_1.o(i.main) refers to main_1.o(i.SystemClock_Config) for SystemClock_Config
    main_1.o(i.main) refers to main_1.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main_1.o(i.main) refers to main_1.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main_1.o(i.main) refers to main_1.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main_1.o(i.main) refers to main_1.o(i.MX_LPUART1_UART_Init) for MX_LPUART1_UART_Init
    main_1.o(i.main) refers to main_1.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main_1.o(i.main) refers to main_1.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Init) for SRV_TIMER_Init
    main_1.o(i.main) refers to srv_system.o(i.SRV_SYSTEM_Init) for SRV_SYSTEM_Init
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_Init) for BSP_LED_Init
    main_1.o(i.main) refers to bsp_uart.o(i.BSP_UART_Init) for BSP_UART_Init
    main_1.o(i.main) refers to bsp_key.o(i.BSP_KEY_Init) for BSP_KEY_Init
    main_1.o(i.main) refers to bsp_io.o(i.BSP_IO_Init) for BSP_IO_Init
    main_1.o(i.main) refers to bsp_relay.o(i.BSP_RELAY_Init) for BSP_RELAY_Init
    main_1.o(i.main) refers to app_protocol.o(i.APP_PROTOCOL_Init) for APP_PROTOCOL_Init
    main_1.o(i.main) refers to bsp_io.o(i.BSP_IO_RegisterCallback) for BSP_IO_RegisterCallback
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Create) for SRV_TIMER_Create
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Start) for SRV_TIMER_Start
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_SetBlink) for BSP_LED_SetBlink
    main_1.o(i.main) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main_1.o(i.main) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    main_1.o(i.main) refers to drv_max31856.o(i.DRV_MAX31856_Init) for DRV_MAX31856_Init
    main_1.o(i.main) refers to drv_ds18b20.o(i.DRV_DS18B20_Init) for DRV_DS18B20_Init
    main_1.o(i.main) refers to drv_ads1115.o(i.DRV_ADS1115_Init) for DRV_ADS1115_Init
    main_1.o(i.main) refers to drv_ina226.o(i.DRV_INA226_Init) for DRV_INA226_Init
    main_1.o(i.main) refers to app_adc.o(i.APP_ADC_Init) for APP_ADC_Init
    main_1.o(i.main) refers to app_display.o(i.APP_DISPLAY_Init) for APP_DISPLAY_Init
    main_1.o(i.main) refers to srv_timer.o(i.SRV_TIMER_Process) for SRV_TIMER_Process
    main_1.o(i.main) refers to srv_system.o(i.SRV_SYSTEM_Process) for SRV_SYSTEM_Process
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_Process) for BSP_LED_Process
    main_1.o(i.main) refers to bsp_relay.o(i.BSP_RELAY_Process) for BSP_RELAY_Process
    main_1.o(i.main) refers to app_protocol.o(i.APP_PROTOCOL_Process) for APP_PROTOCOL_Process
    main_1.o(i.main) refers to app_adc.o(i.APP_ADC_Process) for APP_ADC_Process
    main_1.o(i.main) refers to app_display.o(i.APP_DISPLAY_Process) for APP_DISPLAY_Process
    main_1.o(i.main) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main_1.o(i.main) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main_1.o(i.main) refers to strcpy.o(.text) for strcpy
    main_1.o(i.main) refers to bsp_key.o(i.BSP_KEY_GetState) for BSP_KEY_GetState
    main_1.o(i.main) refers to bsp_io.o(i.BSP_IO_GetState) for BSP_IO_GetState
    main_1.o(i.main) refers to bsp_relay.o(i.BSP_RELAY_GetState) for BSP_RELAY_GetState
    main_1.o(i.main) refers to app_display.o(i.APP_DISPLAY_UpdateData) for APP_DISPLAY_UpdateData
    main_1.o(i.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    main_1.o(i.main) refers to strlen.o(.text) for strlen
    main_1.o(i.main) refers to bsp_led.o(i.BSP_LED_Toggle) for BSP_LED_Toggle
    main_1.o(i.main) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main_1.o(i.main) refers to main_1.o(.bss) for .bss
    main_1.o(i.main) refers to main_1.o(i.IOStateChangeCallback) for IOStateChangeCallback
    main_1.o(i.main) refers to main_1.o(i.KeyScanTask) for KeyScanTask
    main_1.o(i.main) refers to main_1.o(i.IOScanTask) for IOScanTask
    main_1.o(i.main) refers to main_1.o(.data) for .data
    stm32l4xx_it_1.o(i.EXTI3_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it_1.o(i.EXTI4_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32l4xx_it_1.o(i.SysTick_Handler) refers to stm32l4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32l4xx_it_1.o(i.SysTick_Handler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) for HAL_SYSTICK_IRQHandler
    stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp_1.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal_msp_1.o(i.HAL_MspInit) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_msp_1.o(i.HAL_UART_MspDeInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAError) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32l4xx_hal_adc.o(i.ADC_Disable) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_Enable) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.ADC_Enable) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels) for LL_ADC_SetAnalogWDMonitChannels
    stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig) refers to stm32l4xx_hal_adc.o(i.LL_ADC_ConfigAnalogWDThresholds) for LL_ADC_ConfigAnalogWDThresholds
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l4xx_hal_adc.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l4xx_hal_adc.o(i.LL_ADC_REG_IsConversionOngoing) for LL_ADC_REG_IsConversionOngoing
    stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime) for LL_ADC_SetChannelSamplingTime
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel) for LL_ADC_GetOffsetChannel
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState) for LL_ADC_SetOffsetState
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing) for LL_ADC_INJ_IsConversionOngoing
    stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32l4xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l4xx_hal.o(i.HAL_DeInit) refers to stm32l4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l4xx_hal.o(i.HAL_Delay) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal.o(i.HAL_GetTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_IncTick) refers to stm32l4xx_hal.o(.data) for .data
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal.o(i.HAL_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_MspInit) for HAL_MspInit
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l4xx_hal.o(i.HAL_InitTick) refers to stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l4xx_hal.o(i.HAL_InitTick) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32l4xx_hal_i2c.o(i.I2C_ITError) refers to stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt) for I2C_ITMasterSequentialCplt
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt) for I2C_ITSlaveSequentialCplt
    stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32l4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx_1.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l4xx_1.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l4xx_1.o(.constdata) for APBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l4xx_1.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) for RCC_SetFlashLatencyFromMSIRange
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx_1.o(.constdata) for AHBPrescTable
    stm32l4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_rcc.o(i.RCC_SetFlashLatencyFromMSIRange) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange) for HAL_PWREx_GetVoltageRange
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to system_stm32l4xx_1.o(.constdata) for MSIRangeTable
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) for RCCEx_PLLSAI1_Config
    stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_rcc_ex.o(i.RCCEx_PLLSAI1_Config) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l4xx_hal_flash.o(.bss) for .bss
    stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l4xx_hal_flash.o(.bss) for pFlash
    stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l4xx_hal_cortex.o(i.NVIC_SetPriority) for NVIC_SetPriority
    stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l4xx_hal_uart.o(i.UART_DMAError) refers to stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32l4xx_hal_uart.o(i.UART_SetConfig) refers to stm32l4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l4xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Init) refers to stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32l4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAError) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) for SPI_WaitFifoStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32l4xx_hal_spi.o(i.SPI_WaitFifoStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32l4xx_1.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx_1.o(.constdata) for .constdata
    system_stm32l4xx_1.o(i.SystemCoreClockUpdate) refers to system_stm32l4xx_1.o(.data) for .data
    bsp_io.o(i.BSP_IO_GetState) refers to bsp_io.o(.data) for .data
    bsp_io.o(i.BSP_IO_RegisterCallback) refers to bsp_io.o(.data) for .data
    bsp_io.o(i.BSP_IO_Scan) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bsp_io.o(i.BSP_IO_Scan) refers to bsp_io.o(.data) for .data
    bsp_key.o(i.BSP_KEY_GetState) refers to bsp_key.o(.data) for .data
    bsp_key.o(i.BSP_KEY_Scan) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bsp_key.o(i.BSP_KEY_Scan) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_key.o(i.BSP_KEY_Scan) refers to bsp_key.o(.data) for .data
    bsp_led.o(i.BSP_LED_Off) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_led.o(i.BSP_LED_Off) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_On) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_led.o(i.BSP_LED_On) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_led.o(i.BSP_LED_Process) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    bsp_led.o(i.BSP_LED_Process) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_SetBlink) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_led.o(i.BSP_LED_SetBlink) refers to bsp_led.o(.data) for .data
    bsp_led.o(i.BSP_LED_Toggle) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    bsp_led.o(i.BSP_LED_Toggle) refers to bsp_led.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_GetState) refers to bsp_relay.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_Off) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_relay.o(i.BSP_RELAY_Off) refers to bsp_relay.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_On) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bsp_relay.o(i.BSP_RELAY_On) refers to bsp_relay.o(.data) for .data
    bsp_relay.o(i.BSP_RELAY_SetState) refers to bsp_relay.o(i.BSP_RELAY_Off) for BSP_RELAY_Off
    bsp_relay.o(i.BSP_RELAY_SetState) refers to bsp_relay.o(i.BSP_RELAY_On) for BSP_RELAY_On
    bsp_relay.o(i.BSP_RELAY_Toggle) refers to bsp_relay.o(i.BSP_RELAY_On) for BSP_RELAY_On
    bsp_relay.o(i.BSP_RELAY_Toggle) refers to bsp_relay.o(i.BSP_RELAY_Off) for BSP_RELAY_Off
    bsp_relay.o(i.BSP_RELAY_Toggle) refers to bsp_relay.o(.data) for .data
    bsp_uart.o(i.BSP_UART_Init) refers to bsp_uart.o(.data) for .data
    bsp_uart.o(i.BSP_UART_ReceiveData) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    bsp_uart.o(i.BSP_UART_ReceiveData) refers to bsp_uart.o(.data) for .data
    bsp_uart.o(i.BSP_UART_SendData) refers to stm32l4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    bsp_uart.o(i.BSP_UART_SendData) refers to bsp_uart.o(.data) for .data
    app_adc.o(i.APP_ADC_Init) refers to stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    app_adc.o(i.APP_ADC_Init) refers to app_adc.o(.data) for .data
    app_adc.o(i.APP_ADC_ReadVoltage) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    app_adc.o(i.APP_ADC_ReadVoltage) refers to stm32l4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    app_adc.o(i.APP_ADC_ReadVoltage) refers to app_adc.o(.data) for .data
    app_display.o(i.APP_DISPLAY_Init) refers to drv_oled.o(i.DRV_OLED_Init) for DRV_OLED_Init
    app_display.o(i.APP_DISPLAY_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    app_display.o(i.APP_DISPLAY_Init) refers to strcpy.o(.text) for strcpy
    app_display.o(i.APP_DISPLAY_Init) refers to app_display.o(i.APP_DISPLAY_ShowRunningStatus) for APP_DISPLAY_ShowRunningStatus
    app_display.o(i.APP_DISPLAY_Init) refers to app_display.o(.bss) for .bss
    app_display.o(i.APP_DISPLAY_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(i.DisplayMainPage) for DisplayMainPage
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(i.DisplaySensorPage) for DisplaySensorPage
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(i.DisplayStatusPage) for DisplayStatusPage
    app_display.o(i.APP_DISPLAY_Process) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    app_display.o(i.APP_DISPLAY_Process) refers to app_display.o(.data) for .data
    app_display.o(i.APP_DISPLAY_SetPage) refers to app_display.o(.data) for .data
    app_display.o(i.APP_DISPLAY_ShowMessage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.APP_DISPLAY_ShowMessage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.APP_DISPLAY_ShowMessage) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    app_display.o(i.APP_DISPLAY_ShowRunningStatus) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.APP_DISPLAY_ShowRunningStatus) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.APP_DISPLAY_ShowRunningStatus) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    app_display.o(i.APP_DISPLAY_UpdateData) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    app_display.o(i.APP_DISPLAY_UpdateData) refers to app_display.o(.bss) for .bss
    app_display.o(i.APP_DISPLAY_UpdateData) refers to app_display.o(.data) for .data
    app_display.o(i.DisplayMainPage) refers to _printf_pad.o(.text) for _printf_pre_padding
    app_display.o(i.DisplayMainPage) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    app_display.o(i.DisplayMainPage) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    app_display.o(i.DisplayMainPage) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    app_display.o(i.DisplayMainPage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.DisplayMainPage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.DisplayMainPage) refers to noretval__2sprintf.o(.text) for __2sprintf
    app_display.o(i.DisplayMainPage) refers to drv_oled.o(i.DRV_OLED_DrawFloat) for DRV_OLED_DrawFloat
    app_display.o(i.DisplayMainPage) refers to app_display.o(.bss) for .bss
    app_display.o(i.DisplaySensorPage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.DisplaySensorPage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.DisplaySensorPage) refers to drv_oled.o(i.DRV_OLED_DrawFloat) for DRV_OLED_DrawFloat
    app_display.o(i.DisplaySensorPage) refers to app_display.o(.bss) for .bss
    app_display.o(i.DisplayStatusPage) refers to _printf_pad.o(.text) for _printf_pre_padding
    app_display.o(i.DisplayStatusPage) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    app_display.o(i.DisplayStatusPage) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    app_display.o(i.DisplayStatusPage) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    app_display.o(i.DisplayStatusPage) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    app_display.o(i.DisplayStatusPage) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    app_display.o(i.DisplayStatusPage) refers to noretval__2sprintf.o(.text) for __2sprintf
    app_display.o(i.DisplayStatusPage) refers to drv_oled.o(i.DRV_OLED_DrawFloat) for DRV_OLED_DrawFloat
    app_display.o(i.DisplayStatusPage) refers to app_display.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_GetStatus) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Init) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(i.APP_LOAD_TEST_Stop) for APP_LOAD_TEST_Stop
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Process) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_SetMode) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_SetMode) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_SetParams) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_SetParams) refers to app_load_test.o(.bss) for .bss
    app_load_test.o(i.APP_LOAD_TEST_Start) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Start) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.APP_LOAD_TEST_Stop) refers to app_load_test.o(i.SetPWMDuty) for SetPWMDuty
    app_load_test.o(i.APP_LOAD_TEST_Stop) refers to app_load_test.o(.data) for .data
    app_load_test.o(i.SetPWMDuty) refers to app_load_test.o(.data) for .data
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.APP_PROTOCOL_SendResponse) for APP_PROTOCOL_SendResponse
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_Off) for BSP_LED_Off
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_On) for BSP_LED_On
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_Toggle) for BSP_LED_Toggle
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_led.o(i.BSP_LED_SetBlink) for BSP_LED_SetBlink
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.APP_PROTOCOL_SendError) for APP_PROTOCOL_SendError
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_key.o(i.BSP_KEY_GetState) for BSP_KEY_GetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_io.o(i.BSP_IO_GetState) for BSP_IO_GetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_io.o(i.BSP_IO_SetDebounceThreshold) for BSP_IO_SetDebounceThreshold
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_SetState) for BSP_RELAY_SetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_Toggle) for BSP_RELAY_Toggle
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_SetToggleMode) for BSP_RELAY_SetToggleMode
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_SetPulseMode) for BSP_RELAY_SetPulseMode
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to bsp_relay.o(i.BSP_RELAY_GetState) for BSP_RELAY_GetState
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessTempMeasure) for ProcessTempMeasure
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessADCMeasure) for ProcessADCMeasure
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessCurrentMeasure) for ProcessCurrentMeasure
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessDisplayControl) for ProcessDisplayControl
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessLoadTest) for ProcessLoadTest
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(i.ProcessSystemStatus) for ProcessSystemStatus
    app_protocol.o(i.APP_PROTOCOL_HandleFrame) refers to app_protocol.o(.constdata) for .constdata
    app_protocol.o(i.APP_PROTOCOL_Init) refers to app_protocol.o(.data) for .data
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(i.APP_PROTOCOL_HandleFrame) for APP_PROTOCOL_HandleFrame
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(i.APP_PROTOCOL_SendError) for APP_PROTOCOL_SendError
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(.data) for .data
    app_protocol.o(i.APP_PROTOCOL_RxCallback) refers to app_protocol.o(.bss) for .bss
    app_protocol.o(i.APP_PROTOCOL_SendError) refers to app_protocol.o(i.APP_PROTOCOL_SendResponse) for APP_PROTOCOL_SendResponse
    app_protocol.o(i.APP_PROTOCOL_SendResponse) refers to app_protocol.o(i.APP_PROTOCOL_CalculateChecksum) for APP_PROTOCOL_CalculateChecksum
    app_protocol.o(i.APP_PROTOCOL_SendResponse) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    app_protocol.o(i.ProcessADCMeasure) refers to drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) for DRV_ADS1115_ReadVoltage
    app_protocol.o(i.ProcessADCMeasure) refers to app_adc.o(i.APP_ADC_ReadVoltage) for APP_ADC_ReadVoltage
    app_protocol.o(i.ProcessADCMeasure) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessCurrentMeasure) refers to drv_ina226.o(i.DRV_INA226_ReadVoltage) for DRV_INA226_ReadVoltage
    app_protocol.o(i.ProcessCurrentMeasure) refers to drv_ina226.o(i.DRV_INA226_ReadCurrent) for DRV_INA226_ReadCurrent
    app_protocol.o(i.ProcessCurrentMeasure) refers to drv_ina226.o(i.DRV_INA226_ReadPower) for DRV_INA226_ReadPower
    app_protocol.o(i.ProcessCurrentMeasure) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessDisplayControl) refers to app_display.o(i.APP_DISPLAY_SetPage) for APP_DISPLAY_SetPage
    app_protocol.o(i.ProcessDisplayControl) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    app_protocol.o(i.ProcessDisplayControl) refers to app_display.o(i.APP_DISPLAY_ShowMessage) for APP_DISPLAY_ShowMessage
    app_protocol.o(i.ProcessDisplayControl) refers to drv_oled.o(i.DRV_OLED_DisplayOn) for DRV_OLED_DisplayOn
    app_protocol.o(i.ProcessDisplayControl) refers to drv_oled.o(i.DRV_OLED_DisplayOff) for DRV_OLED_DisplayOff
    app_protocol.o(i.ProcessDisplayControl) refers to drv_oled.o(i.DRV_OLED_SetContrast) for DRV_OLED_SetContrast
    app_protocol.o(i.ProcessDisplayControl) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_SetMode) for APP_LOAD_TEST_SetMode
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_SetParams) for APP_LOAD_TEST_SetParams
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_Start) for APP_LOAD_TEST_Start
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_Stop) for APP_LOAD_TEST_Stop
    app_protocol.o(i.ProcessLoadTest) refers to app_load_test.o(i.APP_LOAD_TEST_GetStatus) for APP_LOAD_TEST_GetStatus
    app_protocol.o(i.ProcessLoadTest) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessSystemStatus) refers to srv_system.o(i.SRV_SYSTEM_GetStatus) for SRV_SYSTEM_GetStatus
    app_protocol.o(i.ProcessSystemStatus) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.ProcessTempMeasure) refers to drv_max31856.o(i.DRV_MAX31856_ReadTemp) for DRV_MAX31856_ReadTemp
    app_protocol.o(i.ProcessTempMeasure) refers to drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) for DRV_DS18B20_ReadTemp
    app_protocol.o(i.ProcessTempMeasure) refers to app_protocol.o(i.SendResponse) for SendResponse
    app_protocol.o(i.SendResponse) refers to app_protocol.o(i.APP_PROTOCOL_CalculateChecksum) for APP_PROTOCOL_CalculateChecksum
    app_protocol.o(i.SendResponse) refers to bsp_uart.o(i.BSP_UART_SendData) for BSP_UART_SendData
    drv_ads1115.o(i.ADS1115_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ads1115.o(i.ADS1115_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    drv_ads1115.o(i.ADS1115_ReadRegister) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_Init) refers to drv_ads1115.o(i.ADS1115_ReadRegister) for ADS1115_ReadRegister
    drv_ads1115.o(i.DRV_ADS1115_Init) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_IsReady) refers to drv_ads1115.o(i.ADS1115_ReadRegister) for ADS1115_ReadRegister
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to drv_ads1115.o(i.ADS1115_ReadRegister) for ADS1115_ReadRegister
    drv_ads1115.o(i.DRV_ADS1115_ReadChannel) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) refers to drv_ads1115.o(i.DRV_ADS1115_ReadChannel) for DRV_ADS1115_ReadChannel
    drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) refers to drv_ads1115.o(.constdata) for .constdata
    drv_ads1115.o(i.DRV_ADS1115_ReadVoltage) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_SetDataRate) refers to drv_ads1115.o(.data) for .data
    drv_ads1115.o(i.DRV_ADS1115_SetGain) refers to drv_ads1115.o(.data) for .data
    drv_ds18b20.o(i.DRV_DS18B20_Init) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DRV_DS18B20_Init) refers to drv_ds18b20.o(i.DRV_DS18B20_IsPresent) for DRV_DS18B20_IsPresent
    drv_ds18b20.o(i.DRV_DS18B20_Init) refers to drv_ds18b20.o(i.DRV_DS18B20_SetResolution) for DRV_DS18B20_SetResolution
    drv_ds18b20.o(i.DRV_DS18B20_IsPresent) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_WriteByte) for DS18B20_WriteByte
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_SetPinOutput) for DS18B20_SetPinOutput
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_WritePin) for DS18B20_WritePin
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_DelayUs) for DS18B20_DelayUs
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DRV_DS18B20_ReadTemp) refers to drv_ds18b20.o(i.DS18B20_ReadPin) for DS18B20_ReadPin
    drv_ds18b20.o(i.DRV_DS18B20_SetResolution) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_SetResolution) refers to drv_ds18b20.o(i.DS18B20_WriteByte) for DS18B20_WriteByte
    drv_ds18b20.o(i.DRV_DS18B20_StartConversion) refers to drv_ds18b20.o(i.DS18B20_Reset) for DS18B20_Reset
    drv_ds18b20.o(i.DRV_DS18B20_StartConversion) refers to drv_ds18b20.o(i.DS18B20_WriteByte) for DS18B20_WriteByte
    drv_ds18b20.o(i.DS18B20_DelayUs) refers to system_stm32l4xx_1.o(.data) for SystemCoreClock
    drv_ds18b20.o(i.DS18B20_ReadPin) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    drv_ds18b20.o(i.DS18B20_ReadPin) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_SetPinOutput) for DS18B20_SetPinOutput
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_WritePin) for DS18B20_WritePin
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_DelayUs) for DS18B20_DelayUs
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DS18B20_Reset) refers to drv_ds18b20.o(i.DS18B20_ReadPin) for DS18B20_ReadPin
    drv_ds18b20.o(i.DS18B20_SetPinInput) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_ds18b20.o(i.DS18B20_SetPinInput) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    drv_ds18b20.o(i.DS18B20_SetPinInput) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ds18b20.o(i.DS18B20_SetPinOutput) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_ds18b20.o(i.DS18B20_SetPinOutput) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    drv_ds18b20.o(i.DS18B20_SetPinOutput) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_SetPinOutput) for DS18B20_SetPinOutput
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_WritePin) for DS18B20_WritePin
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_DelayUs) for DS18B20_DelayUs
    drv_ds18b20.o(i.DS18B20_WriteByte) refers to drv_ds18b20.o(i.DS18B20_SetPinInput) for DS18B20_SetPinInput
    drv_ds18b20.o(i.DS18B20_WritePin) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    drv_ds18b20.o(i.DS18B20_WritePin) refers to drv_ds18b20.o(.constdata) for .constdata
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(i.INA226_WriteRegister) for INA226_WriteRegister
    drv_ina226.o(i.DRV_INA226_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(i.DRV_INA226_SetCalibration) for DRV_INA226_SetCalibration
    drv_ina226.o(i.DRV_INA226_Init) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.DRV_INA226_IsReady) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadCurrent) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadCurrent) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.DRV_INA226_ReadPower) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadPower) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.DRV_INA226_ReadShuntVoltage) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_ReadVoltage) refers to drv_ina226.o(i.INA226_ReadRegister) for INA226_ReadRegister
    drv_ina226.o(i.DRV_INA226_SetCalibration) refers to drv_ina226.o(i.INA226_WriteRegister) for INA226_WriteRegister
    drv_ina226.o(i.DRV_INA226_SetCalibration) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.INA226_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ina226.o(i.INA226_ReadRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    drv_ina226.o(i.INA226_ReadRegister) refers to drv_ina226.o(.data) for .data
    drv_ina226.o(i.INA226_WriteRegister) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_ina226.o(i.INA226_WriteRegister) refers to drv_ina226.o(.data) for .data
    drv_max31856.o(i.DRV_MAX31856_ClearFault) refers to drv_max31856.o(i.MAX31856_ReadRegister) for MAX31856_ReadRegister
    drv_max31856.o(i.DRV_MAX31856_ClearFault) refers to drv_max31856.o(i.MAX31856_WriteRegister) for MAX31856_WriteRegister
    drv_max31856.o(i.DRV_MAX31856_Config) refers to drv_max31856.o(i.MAX31856_WriteRegister) for MAX31856_WriteRegister
    drv_max31856.o(i.DRV_MAX31856_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    drv_max31856.o(i.DRV_MAX31856_Init) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    drv_max31856.o(i.DRV_MAX31856_Init) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.DRV_MAX31856_Init) refers to stm32l4xx_hal.o(i.HAL_Delay) for HAL_Delay
    drv_max31856.o(i.DRV_MAX31856_Init) refers to drv_max31856.o(i.DRV_MAX31856_Config) for DRV_MAX31856_Config
    drv_max31856.o(i.DRV_MAX31856_Init) refers to drv_max31856.o(.data) for .data
    drv_max31856.o(i.DRV_MAX31856_ReadColdJunctionTemp) refers to drv_max31856.o(i.MAX31856_ReadMultipleRegisters) for MAX31856_ReadMultipleRegisters
    drv_max31856.o(i.DRV_MAX31856_ReadFault) refers to drv_max31856.o(i.MAX31856_ReadRegister) for MAX31856_ReadRegister
    drv_max31856.o(i.DRV_MAX31856_ReadTemp) refers to drv_max31856.o(i.MAX31856_ReadMultipleRegisters) for MAX31856_ReadMultipleRegisters
    drv_max31856.o(i.MAX31856_CS_Control) refers to stm32l4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    drv_max31856.o(i.MAX31856_CS_Control) refers to drv_max31856.o(.constdata) for .constdata
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    drv_max31856.o(i.MAX31856_ReadMultipleRegisters) refers to drv_max31856.o(.data) for .data
    drv_max31856.o(i.MAX31856_ReadRegister) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.MAX31856_ReadRegister) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    drv_max31856.o(i.MAX31856_ReadRegister) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    drv_max31856.o(i.MAX31856_ReadRegister) refers to drv_max31856.o(.data) for .data
    drv_max31856.o(i.MAX31856_WriteRegister) refers to drv_max31856.o(i.MAX31856_CS_Control) for MAX31856_CS_Control
    drv_max31856.o(i.MAX31856_WriteRegister) refers to stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    drv_max31856.o(i.MAX31856_WriteRegister) refers to drv_max31856.o(.data) for .data
    drv_oled.o(i.DRV_OLED_Clear) refers to rt_memclr.o(.text) for __aeabi_memclr
    drv_oled.o(i.DRV_OLED_Clear) refers to drv_oled.o(.bss) for .bss
    drv_oled.o(i.DRV_OLED_DisplayOff) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_DisplayOn) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to noretval__2sprintf.o(.text) for __2sprintf
    drv_oled.o(i.DRV_OLED_DrawFloat) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to _printf_dec.o(.text) for _printf_int_dec
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to noretval__2sprintf.o(.text) for __2sprintf
    drv_oled.o(i.DRV_OLED_DrawNumber) refers to drv_oled.o(i.DRV_OLED_DrawString) for DRV_OLED_DrawString
    drv_oled.o(i.DRV_OLED_DrawPixel) refers to drv_oled.o(.bss) for .bss
    drv_oled.o(i.DRV_OLED_DrawString) refers to drv_oled.o(i.DRV_OLED_DrawPixel) for DRV_OLED_DrawPixel
    drv_oled.o(i.DRV_OLED_DrawString) refers to drv_oled.o(.constdata) for .constdata
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(i.DRV_OLED_Clear) for DRV_OLED_Clear
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(i.DRV_OLED_Update) for DRV_OLED_Update
    drv_oled.o(i.DRV_OLED_Init) refers to drv_oled.o(.data) for .data
    drv_oled.o(i.DRV_OLED_SetContrast) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_Update) refers to drv_oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    drv_oled.o(i.DRV_OLED_Update) refers to h1_alloc.o(.text) for malloc
    drv_oled.o(i.DRV_OLED_Update) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    drv_oled.o(i.DRV_OLED_Update) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_oled.o(i.DRV_OLED_Update) refers to h1_free.o(.text) for free
    drv_oled.o(i.DRV_OLED_Update) refers to drv_oled.o(.bss) for .bss
    drv_oled.o(i.DRV_OLED_Update) refers to drv_oled.o(.data) for .data
    drv_oled.o(i.OLED_WriteCommand) refers to stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    drv_oled.o(i.OLED_WriteCommand) refers to drv_oled.o(.data) for .data
    srv_system.o(i.SRV_SYSTEM_GetStatus) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_Init) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_system.o(i.SRV_SYSTEM_Init) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_Init) refers to srv_system.o(.data) for .data
    srv_system.o(i.SRV_SYSTEM_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_system.o(i.SRV_SYSTEM_Process) refers to srv_system.o(.data) for .data
    srv_system.o(i.SRV_SYSTEM_Process) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_ReportError) refers to bsp_led.o(i.BSP_LED_SetBlink) for BSP_LED_SetBlink
    srv_system.o(i.SRV_SYSTEM_ReportError) refers to srv_system.o(.bss) for .bss
    srv_system.o(i.SRV_SYSTEM_SelfCheck) refers to srv_system.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Create) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_timer.o(i.SRV_TIMER_Create) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Create) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Init) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Init) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Process) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_timer.o(i.SRV_TIMER_Process) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Process) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Start) refers to stm32l4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    srv_timer.o(i.SRV_TIMER_Start) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Start) refers to srv_timer.o(.bss) for .bss
    srv_timer.o(i.SRV_TIMER_Stop) refers to srv_timer.o(.data) for .data
    srv_timer.o(i.SRV_TIMER_Stop) refers to srv_timer.o(.bss) for .bss
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main_1.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32l431xx.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing main_1.o(.rev16_text), (4 bytes).
    Removing main_1.o(.revsh_text), (4 bytes).
    Removing main_1.o(.rrx_text), (6 bytes).
    Removing main_1.o(i._Error_Handler), (2 bytes).
    Removing stm32l4xx_it_1.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_it_1.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_it_1.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp_1.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_msp_1.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_msp_1.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_ADC_MspDeInit), (40 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing stm32l4xx_hal_msp_1.o(i.HAL_UART_MspDeInit), (40 bytes).
    Removing stm32l4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_ConversionStop), (192 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAConvCplt), (104 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32l4xx_hal_adc.o(i.ADC_Enable), (152 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (550 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_DeInit), (364 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (486 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (164 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (190 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start), (136 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (196 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Start_IT), (232 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (116 bytes).
    Removing stm32l4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32l4xx_hal_adc.o(i.LL_ADC_ConfigAnalogWDThresholds), (40 bytes).
    Removing stm32l4xx_hal_adc.o(i.LL_ADC_SetAnalogWDMonitChannels), (56 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (28 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (108 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (26 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (34 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (34 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (34 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (1212 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (46 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (160 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (142 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (200 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (80 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (90 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (92 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (138 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (102 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_GetOffsetChannel), (12 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_INJ_IsConversionOngoing), (8 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetChannelSamplingTime), (70 bytes).
    Removing stm32l4xx_hal_adc_ex.o(i.LL_ADC_SetOffsetState), (16 bytes).
    Removing stm32l4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_DeInit), (44 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32l4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableIOAnalogSwitchBooster), (16 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (12 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_SRAM2Erase), (28 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32l4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32l4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (98 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (294 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (244 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Receive_IT), (128 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Sequential_Transmit_IT), (124 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (248 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (374 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (270 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (210 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (366 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (266 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (206 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (318 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (164 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (92 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Receive_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Sequential_Transmit_IT), (152 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (320 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (156 bytes).
    Removing stm32l4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (92 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAAbort), (50 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAError), (18 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (66 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (66 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (2 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Disable_IRQ), (88 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Enable_IRQ), (96 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITAddrCplt), (136 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITError), (224 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITListenCplt), (92 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterCplt), (180 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITMasterSequentialCplt), (76 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveCplt), (240 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_ITSlaveSequentialCplt), (84 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (196 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Master_ISR_IT), (288 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (112 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (112 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (106 bytes).
    Removing stm32l4xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (272 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32l4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (78 bytes).
    Removing stm32l4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_DeInit), (212 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (228 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32l4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (132 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI1), (76 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (56 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableMSIPLLMode), (16 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI1), (140 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (204 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (916 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_StandbyMSIRangeConfig), (24 bytes).
    Removing stm32l4xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32l4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_Program_Fast), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_SetErrorCode), (204 bytes).
    Removing stm32l4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (136 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (284 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program), (170 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (116 bytes).
    Removing stm32l4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32l4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_MassErase), (28 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (104 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (248 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.FLASH_PageErase), (48 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (226 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (148 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (140 bytes).
    Removing stm32l4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (278 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32l4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (246 bytes).
    Removing stm32l4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32l4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Abort), (54 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_DeInit), (140 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (170 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Init), (216 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (202 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32l4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (68 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32l4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (212 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (88 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (84 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (64 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (128 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (128 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (16 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP2Mode), (56 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (60 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32l4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32l4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (32 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (32 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (84 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (22 bytes).
    Removing stm32l4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32l4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (58 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (58 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (44 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (44 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort), (124 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive), (86 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (124 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (58 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (88 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Abort_IT), (196 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAPause), (98 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAResume), (92 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DMAStop), (84 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_DeInit), (64 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_IRQHandler), (332 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive), (238 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (148 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Receive_IT), (172 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (136 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (104 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAAbortOnError), (20 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMAReceiveCplt), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxAbortCallback), (62 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (38 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATransmitCplt), (48 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxAbortCallback), (52 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (22 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_EndRxTransfer), (32 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_EndTxTransfer), (18 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_RxISR_16BIT), (92 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_RxISR_8BIT), (90 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_16BIT), (66 bytes).
    Removing stm32l4xx_hal_uart.o(i.UART_TxISR_8BIT), (60 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (138 bytes).
    Removing stm32l4xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort), (352 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (348 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DMAStop), (40 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (216 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive), (358 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (320 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (192 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (560 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (424 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (200 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (252 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (148 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (54 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (50 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (70 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortRx_ISR), (128 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_AbortTx_ISR), (124 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (82 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseRx_ISR), (64 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_CloseTx_ISR), (76 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAAbortOnError), (18 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (86 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (128 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (92 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (124 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_EndRxTransaction), (116 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_16BIT), (38 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_RxISR_8BIT), (36 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32l4xx_hal_spi.o(i.SPI_TxISR_8BIT), (30 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_spi_ex.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_spi_ex.o(i.HAL_SPIEx_FlushRxFifo), (36 bytes).
    Removing stm32l4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing system_stm32l4xx_1.o(.rev16_text), (4 bytes).
    Removing system_stm32l4xx_1.o(.revsh_text), (4 bytes).
    Removing system_stm32l4xx_1.o(.rrx_text), (6 bytes).
    Removing system_stm32l4xx_1.o(i.SystemCoreClockUpdate), (168 bytes).
    Removing bsp_io.o(.rev16_text), (4 bytes).
    Removing bsp_io.o(.revsh_text), (4 bytes).
    Removing bsp_io.o(.rrx_text), (6 bytes).
    Removing bsp_io.o(i.BSP_IO_SetDebounceThreshold), (2 bytes).
    Removing bsp_key.o(.rev16_text), (4 bytes).
    Removing bsp_key.o(.revsh_text), (4 bytes).
    Removing bsp_key.o(.rrx_text), (6 bytes).
    Removing bsp_led.o(.rev16_text), (4 bytes).
    Removing bsp_led.o(.revsh_text), (4 bytes).
    Removing bsp_led.o(.rrx_text), (6 bytes).
    Removing bsp_led.o(i.BSP_LED_Off), (32 bytes).
    Removing bsp_led.o(i.BSP_LED_On), (32 bytes).
    Removing bsp_relay.o(.rev16_text), (4 bytes).
    Removing bsp_relay.o(.revsh_text), (4 bytes).
    Removing bsp_relay.o(.rrx_text), (6 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_Off), (36 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_On), (36 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_SetPulseMode), (2 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_SetState), (18 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_SetToggleMode), (2 bytes).
    Removing bsp_relay.o(i.BSP_RELAY_Toggle), (32 bytes).
    Removing bsp_uart.o(.rev16_text), (4 bytes).
    Removing bsp_uart.o(.revsh_text), (4 bytes).
    Removing bsp_uart.o(.rrx_text), (6 bytes).
    Removing bsp_uart.o(i.BSP_UART_ReceiveData), (32 bytes).
    Removing app_adc.o(.rev16_text), (4 bytes).
    Removing app_adc.o(.revsh_text), (4 bytes).
    Removing app_adc.o(.rrx_text), (6 bytes).
    Removing app_adc.o(i.APP_ADC_ReadVoltage), (164 bytes).
    Removing app_display.o(.rev16_text), (4 bytes).
    Removing app_display.o(.revsh_text), (4 bytes).
    Removing app_display.o(.rrx_text), (6 bytes).
    Removing app_display.o(i.APP_DISPLAY_SetPage), (24 bytes).
    Removing app_display.o(i.APP_DISPLAY_ShowMessage), (52 bytes).
    Removing app_load_test.o(.rev16_text), (4 bytes).
    Removing app_load_test.o(.revsh_text), (4 bytes).
    Removing app_load_test.o(.rrx_text), (6 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_GetStatus), (16 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Init), (60 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Process), (316 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_SetMode), (28 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_SetParams), (76 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Start), (36 bytes).
    Removing app_load_test.o(i.APP_LOAD_TEST_Stop), (20 bytes).
    Removing app_load_test.o(i.SetPWMDuty), (28 bytes).
    Removing app_load_test.o(.bss), (12 bytes).
    Removing app_load_test.o(.data), (24 bytes).
    Removing app_protocol.o(.rev16_text), (4 bytes).
    Removing app_protocol.o(.revsh_text), (4 bytes).
    Removing app_protocol.o(.rrx_text), (6 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_CalculateChecksum), (26 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_HandleFrame), (576 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_RxCallback), (172 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_SendError), (22 bytes).
    Removing app_protocol.o(i.APP_PROTOCOL_SendResponse), (84 bytes).
    Removing app_protocol.o(i.ProcessADCMeasure), (136 bytes).
    Removing app_protocol.o(i.ProcessCurrentMeasure), (168 bytes).
    Removing app_protocol.o(i.ProcessDisplayControl), (158 bytes).
    Removing app_protocol.o(i.ProcessLoadTest), (254 bytes).
    Removing app_protocol.o(i.ProcessSystemStatus), (106 bytes).
    Removing app_protocol.o(i.ProcessTempMeasure), (128 bytes).
    Removing app_protocol.o(i.SendResponse), (98 bytes).
    Removing app_protocol.o(.bss), (66 bytes).
    Removing app_protocol.o(.constdata), (17 bytes).
    Removing drv_ads1115.o(.rev16_text), (4 bytes).
    Removing drv_ads1115.o(.revsh_text), (4 bytes).
    Removing drv_ads1115.o(.rrx_text), (6 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_IsReady), (24 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_ReadChannel), (136 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_ReadVoltage), (84 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_SetDataRate), (12 bytes).
    Removing drv_ads1115.o(i.DRV_ADS1115_SetGain), (12 bytes).
    Removing drv_ads1115.o(.constdata), (24 bytes).
    Removing drv_ds18b20.o(.rev16_text), (4 bytes).
    Removing drv_ds18b20.o(.revsh_text), (4 bytes).
    Removing drv_ds18b20.o(.rrx_text), (6 bytes).
    Removing drv_ds18b20.o(i.DRV_DS18B20_ReadTemp), (174 bytes).
    Removing drv_ds18b20.o(i.DRV_DS18B20_StartConversion), (34 bytes).
    Removing drv_ina226.o(.rev16_text), (4 bytes).
    Removing drv_ina226.o(.revsh_text), (4 bytes).
    Removing drv_ina226.o(.rrx_text), (6 bytes).
    Removing drv_ina226.o(i.DRV_INA226_IsReady), (26 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadCurrent), (52 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadPower), (52 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadShuntVoltage), (52 bytes).
    Removing drv_ina226.o(i.DRV_INA226_ReadVoltage), (48 bytes).
    Removing drv_max31856.o(.rev16_text), (4 bytes).
    Removing drv_max31856.o(.revsh_text), (4 bytes).
    Removing drv_max31856.o(.rrx_text), (6 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ClearFault), (62 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ReadColdJunctionTemp), (68 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ReadFault), (22 bytes).
    Removing drv_max31856.o(i.DRV_MAX31856_ReadTemp), (88 bytes).
    Removing drv_max31856.o(i.MAX31856_ReadMultipleRegisters), (76 bytes).
    Removing drv_max31856.o(i.MAX31856_ReadRegister), (72 bytes).
    Removing drv_oled.o(.rev16_text), (4 bytes).
    Removing drv_oled.o(.revsh_text), (4 bytes).
    Removing drv_oled.o(.rrx_text), (6 bytes).
    Removing drv_oled.o(i.DRV_OLED_DisplayOff), (6 bytes).
    Removing drv_oled.o(i.DRV_OLED_DisplayOn), (6 bytes).
    Removing drv_oled.o(i.DRV_OLED_DrawNumber), (36 bytes).
    Removing drv_oled.o(i.DRV_OLED_SetContrast), (26 bytes).
    Removing srv_system.o(.rev16_text), (4 bytes).
    Removing srv_system.o(.revsh_text), (4 bytes).
    Removing srv_system.o(.rrx_text), (6 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_FeedWatchdog), (2 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_GetStatus), (8 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_ReportError), (84 bytes).
    Removing srv_system.o(i.SRV_SYSTEM_SelfCheck), (44 bytes).
    Removing srv_timer.o(.rev16_text), (4 bytes).
    Removing srv_timer.o(.revsh_text), (4 bytes).
    Removing srv_timer.o(.rrx_text), (6 bytes).
    Removing srv_timer.o(i.SRV_TIMER_Stop), (40 bytes).

581 unused section(s) (total 37415 bytes) removed from the image.
