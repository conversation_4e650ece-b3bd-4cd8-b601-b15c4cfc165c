
#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "stm32l4xx_hal.h"

/* 按键ID定义 */
typedef enum {
    KEY1 = 0,
    KEY2,
    KEY3,
    KEY4
} KEY_ID_TypeDef;

/* 按键状态定义 */
typedef enum {
    KEY_RELEASED = 0,
    KEY_PRESSED
} KEY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_KEY_Init(void);
void BSP_KEY_Scan(void);
KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id);

#endif /* __BSP_KEY_H__ */


#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "stm32l4xx_hal.h"

/* 按键ID定义 */
typedef enum {
    KEY1 = 0,
    KEY2,
    KEY3,
    <PERSON>EY4
} KEY_ID_TypeDef;

/* 按键状态定义 */
typedef enum {
    KEY_RELEASED = 0,
    KEY_PRESSED
} KEY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_KEY_Init(void);
void BSP_KEY_Scan(void);
KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id);

#endif /* __BSP_KEY_H__ */


#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "stm32l4xx_hal.h"

/* 按键ID定义 */
typedef enum {
    KEY1 = 0,
    KEY2,
    KEY3,
    KEY4
} KEY_ID_TypeDef;

/* 按键状态定义 */
typedef enum {
    KEY_RELEASED = 0,
    KEY_PRESSED
} KEY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_KEY_Init(void);
void BSP_KEY_Scan(void);
KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id);

#endif /* __BSP_KEY_H__ */


#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "stm32l4xx_hal.h"

/* 按键ID定义 */
typedef enum {
    KEY1 = 0,
    KEY2,
    KEY3,
    KEY4
} KEY_ID_TypeDef;

/* 按键状态定义 */
typedef enum {
    KEY_RELEASED = 0,
    KEY_PRESSED
} KEY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_KEY_Init(void);
void BSP_KEY_Scan(void);
KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id);

#endif /* __BSP_KEY_H__ */


#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "stm32l4xx_hal.h"

/* 按键ID定义 */
typedef enum {
    KEY1 = 0,
    KEY2,
    KEY3,
    KEY4
} KEY_ID_TypeDef;

/* 按键状态定义 */
typedef enum {
    KEY_RELEASED = 0,
    KEY_PRESSED
} KEY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_KEY_Init(void);
void BSP_KEY_Scan(void);
KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id);

#endif /* __BSP_KEY_H__ */


#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#include "stm32l4xx_hal.h"

/* 按键ID定义 */
typedef enum {
    KEY1 = 0,
    KEY2,
    KEY3,
    KEY4
} KEY_ID_TypeDef;

/* 按键状态定义 */
typedef enum {
    KEY_RELEASED = 0,
    KEY_PRESSED
} KEY_State_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef BSP_KEY_Init(void);
void BSP_KEY_Scan(void);
KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id);

#endif /* __BSP_KEY_H__ */




