#include "bsp/bsp_io.h"

/* IO配置结构体 */
typedef struct {
    GPIO_TypeDef* GPIOx;
    uint16_t GPIO_Pin;
    IO_State_TypeDef state;
    IO_State_TypeDef last_state;
    IO_StateChangeCallback_TypeDef callback;
} IO_Config_TypeDef;

/* IO配置表 */
static IO_Config_TypeDef io_config[2] = {
    {GPIOA, GPIO_PIN_2, IO_LOW, IO_LOW, NULL},  // IO_CHANNEL1
    {GPIOA, GPIO_PIN_3, IO_LOW, IO_LOW, NULL}   // IO_CHANNEL2
};

HAL_StatusTypeDef BSP_IO_Init(void)
{
    return HAL_OK;
}

void BSP_IO_Scan(void)
{
    for(int i = 0; i < 2; i++) {
        GPIO_PinState pin_state = HAL_GPIO_ReadPin(io_config[i].GPIOx, io_config[i].GPIO_Pin);
        IO_State_TypeDef new_state = (pin_state == GPIO_PIN_SET) ? IO_HIGH : IO_LOW;
        
        if(new_state != io_config[i].last_state) {
            io_config[i].state = new_state;
            if(io_config[i].callback != NULL) {
                io_config[i].callback((IO_Channel_TypeDef)i, new_state);
            }
            io_config[i].last_state = new_state;
        }
    }
}

IO_State_TypeDef BSP_IO_GetState(IO_Channel_TypeDef channel)
{
    if(channel < 2) {
        return io_config[channel].state;
    }
    return IO_LOW;
}

void BSP_IO_RegisterCallback(IO_Channel_TypeDef channel, IO_StateChangeCallback_TypeDef callback)
{
    if(channel < 2) {
        io_config[channel].callback = callback;
    }
}

void BSP_IO_SetDebounceThreshold(IO_Channel_TypeDef channel, uint8_t threshold)
{
    // 设置消抖阈值的实现
    // 这里可以添加具体的消抖逻辑
    (void)channel;
    (void)threshold;
}




