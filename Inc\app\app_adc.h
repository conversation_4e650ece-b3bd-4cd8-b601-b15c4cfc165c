/**
 * @file    app_adc.h
 * @brief   内部ADC应用层头文件
 * <AUTHOR>
 * @date    2025-01-29
 */

#ifndef __APP_ADC_H
#define __APP_ADC_H

#include "main.h"

/* ADC通道定义 */
typedef enum {
    INTERNAL_ADC_CH1 = 0,
    INTERNAL_ADC_CH2 = 1,
    INTERNAL_ADC_CH_MAX
} InternalADC_Channel_TypeDef;

/* 函数声明 */
HAL_StatusTypeDef APP_ADC_Init(ADC_HandleTypeDef *hadc);
HAL_StatusTypeDef APP_ADC_ReadVoltage(InternalADC_Channel_TypeDef channel, float *voltage);
void APP_ADC_Process(void);

#endif /* __APP_ADC_H */

