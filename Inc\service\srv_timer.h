#ifndef __SRV_TIMER_H__
#define __SRV_TIMER_H__

#include "stm32l4xx_hal.h"

#define MAX_TIMERS 10

/* 定时器回调函数类型 */
typedef void (*TimerCallback_TypeDef)(void);

/* 函数声明 */
HAL_StatusTypeDef SRV_TIMER_Init(void);
int8_t SRV_TIMER_Create(uint32_t period_ms, TimerCallback_TypeDef callback);
HAL_StatusTypeDef SRV_TIMER_Start(int8_t timer_id);
HAL_StatusTypeDef SRV_TIMER_Stop(int8_t timer_id);
void SRV_TIMER_Process(void);

#endif /* __SRV_TIMER_H__ */

