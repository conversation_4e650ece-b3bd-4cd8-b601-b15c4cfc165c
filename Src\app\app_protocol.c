/**
 * @file    app_protocol.c
 * @brief   通信协议处理模块实现
 * <AUTHOR>
 * @date    2025-01-28
 */

#include "app/app_protocol.h"
#include "bsp/bsp_uart.h"
#include "bsp/bsp_led.h"
#include "bsp/bsp_key.h"
#include "bsp/bsp_io.h"
#include "bsp/bsp_relay.h"
#include "drivers/drv_max31856.h"
#include "drivers/drv_ds18b20.h"
#include "drivers/drv_ads1115.h"
#include "drivers/drv_ina226.h"
#include "app/app_adc.h"
#include "app/app_display.h"
#include "app/app_load_test.h"
#include "service/srv_system.h"

/* 协议解析状态机 */
typedef enum {
    PROTOCOL_STATE_IDLE = 0,       // 空闲状态
    PROTOCOL_STATE_FUNC_CODE,      // 功能码
    PROTOCOL_STATE_DATA_LEN,       // 数据长度
    PROTOCOL_STATE_DATA,           // 数据
    PROTOCOL_STATE_CHECKSUM_H,     // 校验和高字节
    PROTOCOL_STATE_CHECKSUM_L,     // 校验和低字节
    PROTOCOL_STATE_TAIL            // 帧尾
} Protocol_State_TypeDef;

/* 协议解析状态机变量 */
static Protocol_State_TypeDef protocol_state = PROTOCOL_STATE_IDLE;
static Protocol_Frame_TypeDef rx_frame;
static uint8_t rx_data_index = 0;
static uint16_t rx_checksum = 0;

/* 串口句柄 */
static UART_HandleTypeDef *protocol_huart;

/* 软件版本信息 */
static const uint8_t software_version[] = "TestBoard V1.0.0";

/* Forward declarations */
static void ProcessTempMeasure(uint8_t port, uint8_t *control_info, uint8_t control_len);
static void ProcessADCMeasure(uint8_t port, uint8_t *control_info, uint8_t control_len);
static void ProcessCurrentMeasure(uint8_t port, uint8_t *control_info, uint8_t control_len);
static void ProcessDisplayControl(uint8_t port, uint8_t *control_info, uint8_t control_len);
static void ProcessLoadTest(uint8_t port, uint8_t *control_info, uint8_t control_len);
static void ProcessSystemStatus(uint8_t port, uint8_t *control_info, uint8_t control_len);
static void SendResponse(uint8_t func_code, uint8_t port, uint8_t error_code, uint8_t *data, uint8_t data_len);

/**
 * @brief  计算校验和
 * @param  data: 数据指针
 * @param  len: 数据长度
 * @retval 校验和
 */
static uint16_t APP_PROTOCOL_CalculateChecksum(uint8_t *data, uint16_t len)
{
    uint16_t sum = 0;
    for(uint16_t i = 0; i < len; i++) {
        sum += data[i];
    }
    return sum;
}

/**
 * @brief  发送响应
 * @param  func_code: 功能码
 * @param  port: 端口号
 * @param  error_code: 错误码
 * @param  data: 数据指针
 * @param  data_len: 数据长度
 * @retval None
 */
static void SendResponse(uint8_t func_code, uint8_t port, uint8_t error_code, uint8_t *data, uint8_t data_len)
{
    uint8_t response_frame[PROTOCOL_MAX_FRAME_LEN];
    uint8_t frame_len = 0;
    
    response_frame[frame_len++] = PROTOCOL_FRAME_HEADER;
    response_frame[frame_len++] = func_code;
    response_frame[frame_len++] = port;
    response_frame[frame_len++] = error_code;
    response_frame[frame_len++] = data_len;
    
    if(data != NULL && data_len > 0) {
        for(uint8_t i = 0; i < data_len; i++) {
            response_frame[frame_len++] = data[i];
        }
    }
    
    uint16_t checksum = APP_PROTOCOL_CalculateChecksum(&response_frame[1], frame_len - 1);
    response_frame[frame_len++] = (checksum >> 8) & 0xFF;
    response_frame[frame_len++] = checksum & 0xFF;
    response_frame[frame_len++] = PROTOCOL_FRAME_TAIL;
    
    if(protocol_huart != NULL) {
        BSP_UART_SendData(response_frame, frame_len);
    }
}

/**
 * @brief  处理接收到的协议帧
 * @param  frame: 协议帧指针
 * @retval None
 */
static void APP_PROTOCOL_HandleFrame(Protocol_Frame_TypeDef *frame)
{
    uint8_t response_data[PROTOCOL_MAX_DATA_LEN];
    uint8_t response_len = 0;
    
    switch(frame->func_code) {
        case FUNC_HEARTBEAT:
            /* 心跳包，直接回复 */
            response_data[0] = 0x01;  // 心跳响应
            response_len = 1;
            APP_PROTOCOL_SendResponse(FUNC_HEARTBEAT, response_data, response_len);
            break;
            
        case FUNC_GET_VERSION:
            /* 获取版本信息 */
            for(int i = 0; i < sizeof(software_version) - 1; i++) {
                response_data[i] = software_version[i];
            }
            response_len = sizeof(software_version) - 1;
            APP_PROTOCOL_SendResponse(FUNC_GET_VERSION, response_data, response_len);
            break;
            
        case FUNC_LED_CONTROL:
            /* LED控制 */
            if(frame->data_len >= 2) {
                LED_TypeDef led_id = (LED_TypeDef)frame->data[0];
                uint8_t led_state = frame->data[1];
                
                if(led_id <= BSP_DEBUG_LED) {
                    switch(led_state) {
                        case 0:  // 关闭
                            BSP_LED_Off(led_id);
                            break;
                        case 1:  // 打开
                            BSP_LED_On(led_id);
                            break;
                        case 2:  // 翻转
                            BSP_LED_Toggle(led_id);
                            break;
                        case 3:  // 闪烁
                            if(frame->data_len >= 3) {
                                uint8_t period = frame->data[2];
                                BSP_LED_SetBlink(led_id, period * 100);  // 转换为毫秒
                            }
                            break;
                        default:
                            APP_PROTOCOL_SendError(FUNC_LED_CONTROL, ERR_DATA_VALUE);
                            return;
                    }
                    
                    response_data[0] = 0x00;  // 成功
                    response_len = 1;
                    APP_PROTOCOL_SendResponse(FUNC_LED_CONTROL, response_data, response_len);
                } else {
                    APP_PROTOCOL_SendError(FUNC_LED_CONTROL, ERR_DATA_VALUE);
                }
            } else {
                APP_PROTOCOL_SendError(FUNC_LED_CONTROL, ERR_DATA_LEN);
            }
            break;
            
        case FUNC_KEY_STATUS:
            /* 获取按键状态 */
            response_data[0] = BSP_KEY_GetState(KEY1);
            response_data[1] = BSP_KEY_GetState(KEY2);
            response_data[2] = BSP_KEY_GetState(KEY3);
            response_data[3] = BSP_KEY_GetState(KEY4);
            response_len = 4;
            APP_PROTOCOL_SendResponse(FUNC_KEY_STATUS, response_data, response_len);
            break;
            
        case FUNC_IO_CONTROL:
            /* IO控制/状态 */
            if(frame->data_len >= 1) {
                uint8_t operation = frame->data[0];
                
                if(operation == 0) {  // 获取状态
                    response_data[0] = BSP_IO_GetState(IO_CHANNEL1);
                    response_data[1] = BSP_IO_GetState(IO_CHANNEL2);
                    response_len = 2;
                    APP_PROTOCOL_SendResponse(FUNC_IO_CONTROL, response_data, response_len);
                } else if(operation == 1 && frame->data_len >= 3) {  // 设置消抖时间
                    IO_Channel_TypeDef channel = (IO_Channel_TypeDef)frame->data[1];
                    uint8_t debounce_time = frame->data[2];
                    
                    if(channel <= IO_CHANNEL2) {
                        BSP_IO_SetDebounceThreshold(channel, debounce_time);
                        response_data[0] = 0x00;  // 成功
                        response_len = 1;
                        APP_PROTOCOL_SendResponse(FUNC_IO_CONTROL, response_data, response_len);
                    } else {
                        APP_PROTOCOL_SendError(FUNC_IO_CONTROL, ERR_DATA_VALUE);
                    }
                } else {
                    APP_PROTOCOL_SendError(FUNC_IO_CONTROL, ERR_DATA_VALUE);
                }
            } else {
                APP_PROTOCOL_SendError(FUNC_IO_CONTROL, ERR_DATA_LEN);
            }
            break;
            
        case FUNC_RELAY_CONTROL:
            /* 继电器控制 */
            if(frame->data_len >= 2) {
                RELAY_Channel_TypeDef channel = (RELAY_Channel_TypeDef)frame->data[0];
                uint8_t operation = frame->data[1];
                
                if(channel <= RELAY_CHANNEL2) {
                    switch(operation) {
                        case 0:  // 关闭
                            BSP_RELAY_SetState(channel, RELAY_OFF);
                            break;
                        case 1:  // 打开
                            BSP_RELAY_SetState(channel, RELAY_ON);
                            break;
                        case 2:  // 翻转
                            BSP_RELAY_Toggle(channel);
                            break;
                        case 3:  // 定时翻转模式
                            if(frame->data_len >= 4) {
                                uint16_t period = (frame->data[2] << 8) | frame->data[3];
                                BSP_RELAY_SetToggleMode(channel, period);
                            } else {
                                APP_PROTOCOL_SendError(FUNC_RELAY_CONTROL, ERR_DATA_LEN);
                                return;
                            }
                            break;
                        case 4:  // 脉冲模式
                            if(frame->data_len >= 4) {
                                uint16_t pulse_width = (frame->data[2] << 8) | frame->data[3];
                                BSP_RELAY_SetPulseMode(channel, pulse_width);
                            } else {
                                APP_PROTOCOL_SendError(FUNC_RELAY_CONTROL, ERR_DATA_LEN);
                                return;
                            }
                            break;
                        case 5:  // 获取状态
                            response_data[0] = BSP_RELAY_GetState(channel);
                            response_len = 1;
                            APP_PROTOCOL_SendResponse(FUNC_RELAY_CONTROL, response_data, response_len);
                            return;
                        default:
                            APP_PROTOCOL_SendError(FUNC_RELAY_CONTROL, ERR_DATA_VALUE);
                            return;
                    }
                    
                    response_data[0] = 0x00;  // 成功
                    response_len = 1;
                    APP_PROTOCOL_SendResponse(FUNC_RELAY_CONTROL, response_data, response_len);
                } else {
                    APP_PROTOCOL_SendError(FUNC_RELAY_CONTROL, ERR_DATA_VALUE);
                }
            } else {
                APP_PROTOCOL_SendError(FUNC_RELAY_CONTROL, ERR_DATA_LEN);
            }
            break;
            
        case FUNC_TEMP_MEASURE:
            /* 温度测量 */
            ProcessTempMeasure(frame->data[0], &frame->data[1], frame->data_len - 1);
            break;
            
        case FUNC_ADC_MEASURE:
            /* ADC测量 */
            ProcessADCMeasure(frame->data[0], &frame->data[1], frame->data_len - 1);
            break;
            
        case FUNC_CURRENT_MEASURE:
            /* 电流测量 */
            ProcessCurrentMeasure(frame->data[0], &frame->data[1], frame->data_len - 1);
            break;
            
        case FUNC_DISPLAY_CONTROL:
            /* 显示控制 */
            ProcessDisplayControl(frame->data[0], &frame->data[1], frame->data_len - 1);
            break;
            
        case FUNC_LOAD_TEST:
            /* 动态负载测试 */
            ProcessLoadTest(frame->data[0], &frame->data[1], frame->data_len - 1);
            break;
            
        case FUNC_SYSTEM_STATUS:  // 0x03
            ProcessSystemStatus(frame->data[0], &frame->data[1], frame->data_len - 1);
            break;
            
        default:
            /* 不支持的功能码 */
            APP_PROTOCOL_SendError(frame->func_code, ERR_FUNC_CODE);
            break;
    }
}

/**
 * @brief  处理温度测量命令
 * @param  port: 端口号
 * @param  control_info: 控制信息
 * @param  control_len: 控制信息长度
 * @retval None
 */
static void ProcessTempMeasure(uint8_t port, uint8_t *control_info, uint8_t control_len)
{
    float temperature = 0.0f;
    HAL_StatusTypeDef status = HAL_ERROR;
    uint8_t response_data[8];
    uint8_t response_len = 0;

    switch(port) {
        case TEMP_PORT_MAX31856_CH1:
            status = DRV_MAX31856_ReadTemp(MAX31856_CHANNEL1, &temperature);
            break;

        case TEMP_PORT_MAX31856_CH2:
            status = DRV_MAX31856_ReadTemp(MAX31856_CHANNEL2, &temperature);
            break;

        case TEMP_PORT_DS18B20_CH1:
            status = DRV_DS18B20_ReadTemp(DS18B20_CHANNEL1, &temperature);
            break;

        case TEMP_PORT_DS18B20_CH2:
            status = DRV_DS18B20_ReadTemp(DS18B20_CHANNEL2, &temperature);
            break;

        default:
            SendResponse(FUNC_TEMP_MEASURE, port, ERROR_INVALID_PORT, NULL, 0);
            return;
    }

    if(status == HAL_OK) {
        /* 将浮点温度值转换为4字节数据 */
        union {
            float f;
            uint8_t bytes[4];
        } temp_union;

        temp_union.f = temperature;
        response_data[0] = temp_union.bytes[0];
        response_data[1] = temp_union.bytes[1];
        response_data[2] = temp_union.bytes[2];
        response_data[3] = temp_union.bytes[3];
        response_len = 4;

        SendResponse(FUNC_TEMP_MEASURE, port, ERROR_SUCCESS, response_data, response_len);
    } else {
        SendResponse(FUNC_TEMP_MEASURE, port, ERROR_DEVICE_FAULT, NULL, 0);
    }
}

/**
 * @brief  处理ADC测量命令
 * @param  port: 端口号
 * @param  control_info: 控制信息
 * @param  control_len: 控制信息长度
 * @retval None
 */
static void ProcessADCMeasure(uint8_t port, uint8_t *control_info, uint8_t control_len)
{
    float voltage = 0.0f;
    HAL_StatusTypeDef status = HAL_ERROR;
    uint8_t response_data[8];
    uint8_t response_len = 0;

    switch(port) {
        case ADC_PORT_ADS1115_CH0:
            status = DRV_ADS1115_ReadVoltage(ADS1115_CHANNEL_0, &voltage);
            break;

        case ADC_PORT_ADS1115_CH1:
            status = DRV_ADS1115_ReadVoltage(ADS1115_CHANNEL_1, &voltage);
            break;

        case ADC_PORT_ADS1115_CH2:
            status = DRV_ADS1115_ReadVoltage(ADS1115_CHANNEL_2, &voltage);
            break;

        case ADC_PORT_ADS1115_CH3:
            status = DRV_ADS1115_ReadVoltage(ADS1115_CHANNEL_3, &voltage);
            break;

        case ADC_PORT_INTERNAL_CH1:
            status = APP_ADC_ReadVoltage(INTERNAL_ADC_CH1, &voltage);
            break;

        case ADC_PORT_INTERNAL_CH2:
            status = APP_ADC_ReadVoltage(INTERNAL_ADC_CH2, &voltage);
            break;

        default:
            SendResponse(FUNC_ADC_MEASURE, port, ERROR_INVALID_PORT, NULL, 0);
            return;
    }

    if(status == HAL_OK) {
        /* 将浮点电压值转换为4字节数据 */
        union {
            float f;
            uint8_t bytes[4];
        } voltage_union;

        voltage_union.f = voltage;
        response_data[0] = voltage_union.bytes[0];
        response_data[1] = voltage_union.bytes[1];
        response_data[2] = voltage_union.bytes[2];
        response_data[3] = voltage_union.bytes[3];
        response_len = 4;

        SendResponse(FUNC_ADC_MEASURE, port, ERROR_SUCCESS, response_data, response_len);
    } else {
        SendResponse(FUNC_ADC_MEASURE, port, ERROR_DEVICE_FAULT, NULL, 0);
    }
}

/**
 * @brief  处理电流测量命令
 * @param  port: 端口号
 * @param  control_info: 控制信息
 * @param  control_len: 控制信息长度
 * @retval None
 */
static void ProcessCurrentMeasure(uint8_t port, uint8_t *control_info, uint8_t control_len)
{
    float voltage = 0.0f, current = 0.0f, power = 0.0f;
    HAL_StatusTypeDef status = HAL_ERROR;
    uint8_t response_data[16];
    uint8_t response_len = 0;

    if(port != CURRENT_PORT_INA226) {
        SendResponse(FUNC_CURRENT_MEASURE, port, ERROR_INVALID_PORT, NULL, 0);
        return;
    }

    /* 读取电压、电流、功率 */
    status = DRV_INA226_ReadVoltage(&voltage);
    if(status == HAL_OK) {
        status = DRV_INA226_ReadCurrent(&current);
    }
    if(status == HAL_OK) {
        status = DRV_INA226_ReadPower(&power);
    }

    if(status == HAL_OK) {
        /* 将三个浮点值转换为12字节数据 */
        union {
            float f;
            uint8_t bytes[4];
        } data_union;

        /* 电压 */
        data_union.f = voltage;
        response_data[0] = data_union.bytes[0];
        response_data[1] = data_union.bytes[1];
        response_data[2] = data_union.bytes[2];
        response_data[3] = data_union.bytes[3];

        /* 电流 */
        data_union.f = current;
        response_data[4] = data_union.bytes[0];
        response_data[5] = data_union.bytes[1];
        response_data[6] = data_union.bytes[2];
        response_data[7] = data_union.bytes[3];

        /* 功率 */
        data_union.f = power;
        response_data[8] = data_union.bytes[0];
        response_data[9] = data_union.bytes[1];
        response_data[10] = data_union.bytes[2];
        response_data[11] = data_union.bytes[3];

        response_len = 12;

        SendResponse(FUNC_CURRENT_MEASURE, port, ERROR_SUCCESS, response_data, response_len);
    } else {
        SendResponse(FUNC_CURRENT_MEASURE, port, ERROR_DEVICE_FAULT, NULL, 0);
    }
}

/**
 * @brief  处理显示控制命令
 * @param  port: 端口号
 * @param  control_info: 控制信息
 * @param  control_len: 控制信息长度
 * @retval None
 */
static void ProcessDisplayControl(uint8_t port, uint8_t *control_info, uint8_t control_len)
{
    uint8_t response_data[32];
    uint8_t response_len = 0;
    HAL_StatusTypeDef status = HAL_OK;

    if(control_len < 1) {
        SendResponse(FUNC_DISPLAY_CONTROL, port, ERROR_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t display_cmd = control_info[0];

    switch(display_cmd) {
        case 0x01:  // 设置显示页面
            if(control_len >= 2) {
                status = APP_DISPLAY_SetPage((Display_Page_TypeDef)control_info[1]);
            } else {
                status = HAL_ERROR;
            }
            break;

        case 0x02:  // 显示消息
            if(control_len >= 2) {
                char message[32];
                uint8_t msg_len = control_len - 1;
                if(msg_len > 31) msg_len = 31;
                memcpy(message, &control_info[1], msg_len);
                message[msg_len] = '\0';
                status = APP_DISPLAY_ShowMessage(message);
            } else {
                status = HAL_ERROR;
            }
            break;

        case 0x03:  // 显示开关
            if(control_len >= 2) {
                if(control_info[1]) {
                    status = DRV_OLED_DisplayOn();
                } else {
                    status = DRV_OLED_DisplayOff();
                }
            } else {
                status = HAL_ERROR;
            }
            break;

        case 0x04:  // 设置对比度
            if(control_len >= 2) {
                status = DRV_OLED_SetContrast(control_info[1]);
            } else {
                status = HAL_ERROR;
            }
            break;

        default:
            SendResponse(FUNC_DISPLAY_CONTROL, port, ERROR_INVALID_PARAM, NULL, 0);
            return;
    }

    if(status == HAL_OK) {
        response_data[0] = 0x01;  // 成功
        response_len = 1;
        SendResponse(FUNC_DISPLAY_CONTROL, port, ERROR_SUCCESS, response_data, response_len);
    } else {
        SendResponse(FUNC_DISPLAY_CONTROL, port, ERROR_DEVICE_FAULT, NULL, 0);
    }
}

/**
 * @brief  处理动态负载测试命令
 * @param  port: 端口号
 * @param  control_info: 控制信息
 * @param  control_len: 控制信息长度
 * @retval None
 */
static void ProcessLoadTest(uint8_t port, uint8_t *control_info, uint8_t control_len)
{
    uint8_t response_data[32];
    uint8_t response_len = 0;
    HAL_StatusTypeDef status = HAL_OK;

    if(control_len < 1) {
        SendResponse(FUNC_LOAD_TEST, port, ERROR_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t load_cmd = control_info[0];

    switch(load_cmd) {
        case 0x01:  // 设置测试模式
            if(control_len >= 2) {
                status = APP_LOAD_TEST_SetMode((Load_Mode_TypeDef)control_info[1]);
            } else {
                status = HAL_ERROR;
            }
            break;

        case 0x02:  // 设置测试参数
            if(control_len >= 11) {
                Load_Param_TypeDef params;
                params.mode = (Load_Mode_TypeDef)control_info[1];
                params.duty = (control_info[2] << 8) | control_info[3];
                params.frequency = (control_info[4] << 8) | control_info[5];
                params.amplitude = (control_info[6] << 8) | control_info[7];
                params.offset = (control_info[8] << 8) | control_info[9];
                params.duration = (control_info[10] << 8) | control_info[11];

                status = APP_LOAD_TEST_SetParams(&params);
            } else {
                status = HAL_ERROR;
            }
            break;

        case 0x03:  // 启动测试
            if(control_len >= 9) {
                LoadTest_Config_TypeDef config;
                config.mode = (Load_Mode_TypeDef)control_info[1];
                config.duty = (control_info[2] << 8) | control_info[3];
                config.frequency = (control_info[4] << 8) | control_info[5];
                config.duration = (control_info[6] << 24) | (control_info[7] << 16) | (control_info[8] << 8) | control_info[9];

                status = APP_LOAD_TEST_Start(&config);
            } else {
                status = HAL_ERROR;
            }
            break;

        case 0x04:  // 停止测试
            status = APP_LOAD_TEST_Stop();
            break;

        case 0x05:  // 获取测试状态
            {
                LoadTest_Status_TypeDef test_status = APP_LOAD_TEST_GetStatus();
                response_data[0] = test_status.is_running;
                response_data[1] = (uint8_t)test_status.mode;
                response_data[2] = (test_status.current_duty >> 8) & 0xFF;
                response_data[3] = test_status.current_duty & 0xFF;
                response_len = 4;
                status = HAL_OK;
            }
            break;

        default:
            SendResponse(FUNC_LOAD_TEST, port, ERROR_INVALID_PARAM, NULL, 0);
            return;
    }

    if(status == HAL_OK) {
        if(load_cmd == 0x05) {
            SendResponse(FUNC_LOAD_TEST, port, ERROR_SUCCESS, response_data, response_len);
        } else {
            response_data[0] = 0x01;  // 成功
            response_len = 1;
            SendResponse(FUNC_LOAD_TEST, port, ERROR_SUCCESS, response_data, response_len);
        }
    } else {
        SendResponse(FUNC_LOAD_TEST, port, ERROR_DEVICE_FAULT, NULL, 0);
    }
}

/**
 * @brief  处理系统状态查询命令
 * @param  port: 端口号
 * @param  control_info: 控制信息
 * @param  control_len: 控制信息长度
 * @retval None
 */
static void ProcessSystemStatus(uint8_t port, uint8_t *control_info, uint8_t control_len)
{
    uint8_t response_data[16];
    uint8_t response_len = 0;

    System_Status_TypeDef *status = SRV_SYSTEM_GetStatus();
    if(status != NULL) {
        /* 打包系统状态数据 */
        response_data[0] = (status->uptime_seconds >> 24) & 0xFF;
        response_data[1] = (status->uptime_seconds >> 16) & 0xFF;
        response_data[2] = (status->uptime_seconds >> 8) & 0xFF;
        response_data[3] = status->uptime_seconds & 0xFF;

        response_data[4] = (status->error_count >> 24) & 0xFF;
        response_data[5] = (status->error_count >> 16) & 0xFF;
        response_data[6] = (status->error_count >> 8) & 0xFF;
        response_data[7] = status->error_count & 0xFF;

        response_data[8] = status->system_health;

        response_len = 9;

        SendResponse(FUNC_SYSTEM_STATUS, port, ERROR_SUCCESS, response_data, response_len);
    } else {
        SendResponse(FUNC_SYSTEM_STATUS, port, ERROR_DEVICE_FAULT, NULL, 0);
    }
}

/**
 * @brief  协议初始化
 * @param  huart: 串口句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_PROTOCOL_Init(UART_HandleTypeDef *huart)
{
    if(huart == NULL) {
        return HAL_ERROR;
    }

    protocol_huart = huart;
    protocol_state = PROTOCOL_STATE_IDLE;
    rx_data_index = 0;
    rx_checksum = 0;

    return HAL_OK;
}

/**
 * @brief  协议处理函数，需要在主循环中调用
 * @param  None
 * @retval None
 */
void APP_PROTOCOL_Process(void)
{
    /* 协议处理主要在接收回调函数中完成 */
    /* 这里可以添加超时处理等功能 */
}

/**
 * @brief  发送响应帧
 * @param  func_code: 功能码
 * @param  data: 数据指针
 * @param  data_len: 数据长度
 * @retval None
 */
void APP_PROTOCOL_SendResponse(uint8_t func_code, uint8_t *data, uint8_t data_len)
{
    uint8_t tx_buffer[PROTOCOL_MAX_FRAME_LEN];
    uint8_t tx_index = 0;
    uint16_t checksum = 0;

    /* 帧头 */
    tx_buffer[tx_index++] = PROTOCOL_FRAME_HEADER;

    /* 功能码 */
    tx_buffer[tx_index++] = func_code;

    /* 数据长度 */
    tx_buffer[tx_index++] = data_len;

    /* 数据 */
    for(uint8_t i = 0; i < data_len; i++) {
        tx_buffer[tx_index++] = data[i];
    }

    /* 计算校验和 */
    checksum = APP_PROTOCOL_CalculateChecksum(&tx_buffer[1], tx_index - 1);

    /* 校验和 */
    tx_buffer[tx_index++] = (checksum >> 8) & 0xFF;  // 高字节
    tx_buffer[tx_index++] = checksum & 0xFF;         // 低字节

    /* 帧尾 */
    tx_buffer[tx_index++] = PROTOCOL_FRAME_TAIL;

    /* 发送数据 */
    if(protocol_huart != NULL) {
        BSP_UART_SendData(tx_buffer, tx_index);
    }
}

/**
 * @brief  发送错误响应
 * @param  func_code: 功能码
 * @param  error_code: 错误码
 * @retval None
 */
void APP_PROTOCOL_SendError(uint8_t func_code, Protocol_ErrorCode_TypeDef error_code)
{
    uint8_t error_data[2];
    error_data[0] = func_code;    // 原功能码
    error_data[1] = error_code;   // 错误码

    APP_PROTOCOL_SendResponse(FUNC_ERROR, error_data, 2);
}

/**
 * @brief  串口接收回调函数
 * @param  data: 接收到的数据
 * @retval None
 */
void APP_PROTOCOL_RxCallback(uint8_t data)
{
    static uint16_t calc_checksum = 0;

    switch(protocol_state) {
        case PROTOCOL_STATE_IDLE:
            if(data == PROTOCOL_FRAME_HEADER) {
                protocol_state = PROTOCOL_STATE_FUNC_CODE;
                calc_checksum = 0;
            }
            break;

        case PROTOCOL_STATE_FUNC_CODE:
            rx_frame.func_code = data;
            calc_checksum += data;
            protocol_state = PROTOCOL_STATE_DATA_LEN;
            break;

        case PROTOCOL_STATE_DATA_LEN:
            rx_frame.data_len = data;
            calc_checksum += data;
            if(rx_frame.data_len == 0) {
                protocol_state = PROTOCOL_STATE_CHECKSUM_H;
            } else if(rx_frame.data_len <= PROTOCOL_MAX_DATA_LEN) {
                rx_data_index = 0;
                protocol_state = PROTOCOL_STATE_DATA;
            } else {
                /* 数据长度错误 */
                protocol_state = PROTOCOL_STATE_IDLE;
            }
            break;

        case PROTOCOL_STATE_DATA:
            rx_frame.data[rx_data_index++] = data;
            calc_checksum += data;
            if(rx_data_index >= rx_frame.data_len) {
                protocol_state = PROTOCOL_STATE_CHECKSUM_H;
            }
            break;

        case PROTOCOL_STATE_CHECKSUM_H:
            rx_checksum = (data << 8);
            protocol_state = PROTOCOL_STATE_CHECKSUM_L;
            break;

        case PROTOCOL_STATE_CHECKSUM_L:
            rx_checksum |= data;
            protocol_state = PROTOCOL_STATE_TAIL;
            break;

        case PROTOCOL_STATE_TAIL:
            if(data == PROTOCOL_FRAME_TAIL) {
                /* 校验和验证 */
                if(rx_checksum == calc_checksum) {
                    /* 处理接收到的帧 */
                    APP_PROTOCOL_HandleFrame(&rx_frame);
                } else {
                    /* 校验和错误 */
                    APP_PROTOCOL_SendError(rx_frame.func_code, ERR_CHECKSUM);
                }
            } else {
                /* 帧尾错误 */
                APP_PROTOCOL_SendError(rx_frame.func_code, ERR_FRAME_FORMAT);
            }
            protocol_state = PROTOCOL_STATE_IDLE;
            break;

        default:
            protocol_state = PROTOCOL_STATE_IDLE;
            break;
    }
}
