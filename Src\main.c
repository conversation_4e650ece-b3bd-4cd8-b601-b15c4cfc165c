/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  ** This notice applies to any and all portions of this file
  * that are not between comment pairs USER CODE BEGIN and
  * USER CODE END. Other portions of this file, whether
  * inserted by the user or by software development tools
  * are owned by their respective copyright owners.
  *
  * COPYRIGHT(c) 2025 STMicroelectronics
  *
  * Redistribution and use in source and binary forms, with or without modification,
  * are permitted provided that the following conditions are met:
  *   1. Redistributions of source code must retain the above copyright notice,
  *      this list of conditions and the following disclaimer.
  *   2. Redistributions in binary form must reproduce the above copyright notice,
  *      this list of conditions and the following disclaimer in the documentation
  *      and/or other materials provided with the distribution.
  *   3. Neither the name of STMicroelectronics nor the names of its contributors
  *      may be used to endorse or promote products derived from this software
  *      without specific prior written permission.
  *
  * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
  * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
  * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  ******************************************************************************
  */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32l4xx_hal.h"  // 确保包含这个头文件

/* USER CODE BEGIN Includes */
#include "bsp/bsp_led.h"
#include "bsp/bsp_uart.h"
#include "bsp/bsp_key.h"
#include "bsp/bsp_io.h"
#include "bsp/bsp_relay.h"
#include "app/app_protocol.h"
#include "service/srv_timer.h"
#include <stdio.h>
#include <string.h>
#include "app/app_display.h"
#include "app/app_load_test.h"
#include "app/app_adc.h"
#include "service/srv_system.h"
#include "drivers/drv_max31856.h"
#include "drivers/drv_ds18b20.h"
#include "drivers/drv_ads1115.h"
#include "drivers/drv_ina226.h"
/* USER CODE END Includes */

/* Private variables ---------------------------------------------------------*/
ADC_HandleTypeDef hadc1;

I2C_HandleTypeDef hi2c1;
I2C_HandleTypeDef hi2c2;

UART_HandleTypeDef hlpuart1;

SPI_HandleTypeDef hspi1;

// TIM_HandleTypeDef htim1;  // 暂时注释掉

// IWDG_HandleTypeDef hiwdg;  // 暂时注释掉

/* USER CODE BEGIN PV */
/* Private variables ---------------------------------------------------------*/

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_I2C2_Init(void);
static void MX_I2C1_Init(void);
static void MX_LPUART1_UART_Init(void);
static void MX_ADC1_Init(void);
static void MX_SPI1_Init(void);
// static void MX_TIM1_Init(void);  // 暂时注释掉
// static void MX_IWDG_Init(void);  // 暂时注释掉
// void HAL_TIM_MspPostInit(TIM_HandleTypeDef *htim);  // 暂时注释掉
//void HAL_TIM_MspPostInit(TIM_HandleTypeDef *htim);  // 添加这个函数声明

/* USER CODE BEGIN PFP */
void KeyScanTask(void);
void IOScanTask(void);
void IOStateChangeCallback(IO_Channel_TypeDef channel, IO_State_TypeDef state);
/* USER CODE END PFP */

/* USER CODE BEGIN 0 */
void KeyScanTask(void)
{
    BSP_KEY_Scan();
}

void IOScanTask(void)
{
    BSP_IO_Scan();
}

void IOStateChangeCallback(IO_Channel_TypeDef channel, IO_State_TypeDef state)
{
    uint8_t io_msg[32];
    sprintf((char*)io_msg, "IO%d State: %s\r\n", channel+1, state == IO_HIGH ? "HIGH" : "LOW");
    BSP_UART_SendData(io_msg, strlen((char*)io_msg));
}
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  *
  * @retval None
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration----------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C2_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_ADC1_Init();
  MX_SPI1_Init();
  // MX_TIM1_Init();  // 暂时注释掉
  // MX_IWDG_Init();  // 暂时注释掉
  /* USER CODE BEGIN 2 */
  /* 初始化服务模块 */
  SRV_TIMER_Init();
  SRV_SYSTEM_Init();  // 添加系统服务初始化
  
  /* 初始化BSP模块 */
  BSP_LED_Init();
  BSP_UART_Init(&hlpuart1);
  BSP_KEY_Init();
  BSP_IO_Init();
  BSP_RELAY_Init();
  
  /* 初始化应用模块 */
  APP_PROTOCOL_Init(&hlpuart1);
  
  /* 注册IO状态变化回调 */
  BSP_IO_RegisterCallback(IO_CHANNEL1, IOStateChangeCallback);
  BSP_IO_RegisterCallback(IO_CHANNEL2, IOStateChangeCallback);
  
  /* 创建定时任务 */
  int8_t key_scan_timer = SRV_TIMER_Create(10, KeyScanTask);
  int8_t io_scan_timer = SRV_TIMER_Create(20, IOScanTask);
  SRV_TIMER_Start(key_scan_timer);
  SRV_TIMER_Start(io_scan_timer);
  
  /* 设置系统运行指示灯1Hz闪烁 */
  BSP_LED_SetBlink(BSP_SYS_LED, 1000);
  
  /* 发送启动信息 */
  uint8_t start_msg[] = "Testboard System Started - Protocol Ready\r\n";
  BSP_UART_SendData(start_msg, sizeof(start_msg) - 1);
  
  /* 初始化传感器驱动 */
  if(DRV_MAX31856_Init(&hspi1) != HAL_OK) {
    uint8_t error_msg[] = "MAX31856 Init Failed\r\n";
    BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  }
  
  if(DRV_DS18B20_Init() != HAL_OK) {
    uint8_t error_msg[] = "DS18B20 Init Failed\r\n";
    BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  }
  
  if(DRV_ADS1115_Init(&hi2c1) != HAL_OK) {
    uint8_t error_msg[] = "ADS1115 Init Failed\r\n";
    BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  }
  
  if(DRV_INA226_Init(&hi2c1) != HAL_OK) {
    uint8_t error_msg[] = "INA226 Init Failed\r\n";
    BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  }
  
  /* 发送传感器初始化完成信息 */
  uint8_t sensor_msg[] = "Sensors Initialized - Ready for Measurement\r\n";
  BSP_UART_SendData(sensor_msg, sizeof(sensor_msg) - 1);
  
  /* 初始化内部ADC */
  if(APP_ADC_Init(&hadc1) != HAL_OK) {
    uint8_t error_msg[] = "Internal ADC Init Failed\r\n";
    BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  }
  
  /* 初始化显示模块 */
  if(APP_DISPLAY_Init(&hi2c1) != HAL_OK) {
    uint8_t error_msg[] = "OLED Display Init Failed\r\n";
    BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  }
  
  /* 初始化动态负载测试模块 */
  // if(APP_LOAD_TEST_Init(&htim1) != HAL_OK) {
  //   uint8_t error_msg[] = "Load Test Init Failed\r\n";
  //   BSP_UART_SendData(error_msg, sizeof(error_msg) - 1);
  // }
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* 任务调度 */
    SRV_TIMER_Process();
    SRV_SYSTEM_Process();  // 添加系统服务处理
    BSP_LED_Process();
    BSP_RELAY_Process();
    APP_PROTOCOL_Process();
    APP_ADC_Process();
    APP_DISPLAY_Process();
    // APP_LOAD_TEST_Process();  // 暂时注释掉
    
    /* 定期更新显示数据 */
    static uint32_t last_display_update = 0;
    if(HAL_GetTick() - last_display_update >= 1000) {  // 每秒更新一次
      last_display_update = HAL_GetTick();
      
      Display_Data_TypeDef display_data;
      memset(&display_data, 0, sizeof(display_data));
      strcpy(display_data.title, "Testboard v1.0");
      
      /* 更新按键状态 */
      for(int i = 0; i < 4; i++) {
        if(BSP_KEY_GetState((KEY_ID_TypeDef)i) == KEY_PRESSED) {
          display_data.key_status |= (1 << i);
        }
      }
      
      /* 更新IO状态 */
      for(int i = 0; i < 2; i++) {
        if(BSP_IO_GetState((IO_Channel_TypeDef)i) == IO_HIGH) {
          display_data.io_status |= (1 << i);
        }
      }
      
      /* 更新继电器状态 */
      for(int i = 0; i < 2; i++) {  // 只有2个继电器通道
        if(BSP_RELAY_GetState((RELAY_Channel_TypeDef)i) == RELAY_ON) {
          display_data.relay_status |= (1 << i);
        }
      }
      
      /* 读取传感器数据 */
      // DRV_MAX31856_ReadTemp((uint8_t)0, &display_data.temp_max31856_ch1);
      // DRV_DS18B20_ReadTemp((uint8_t)0, &display_data.temp_ds18b20_ch1);
      // DRV_ADS1115_ReadVoltage((uint8_t)0, &display_data.voltage_ads1115[0]);
      // APP_ADC_ReadVoltage((uint8_t)0, &display_data.voltage_internal[0]);
      // DRV_INA226_ReadVoltage(&display_data.ina226_voltage);
      // DRV_INA226_ReadCurrent(&display_data.ina226_current);
      // DRV_INA226_ReadPower(&display_data.ina226_power);
      
      /* 暂时设置一些测试数据 */
      display_data.temp_max31856_ch1 = 25.0f;
      display_data.temp_ds18b20_ch1 = 24.5f;
      display_data.voltage_ads1115[0] = 3.3f;
      display_data.voltage_internal[0] = 3.2f;
      display_data.ina226_voltage = 12.0f;
      display_data.ina226_current = 0.5f;
      display_data.ina226_power = 6.0f;
      
      APP_DISPLAY_UpdateData(&display_data);
    }
    
    /* 简单的按键测试（可选，协议模式下可以注释掉） */
    static KEY_State_TypeDef last_key_state[4] = {KEY_RELEASED};
    for(int i = 0; i < 4; i++) {
      KEY_State_TypeDef current_state = BSP_KEY_GetState((KEY_ID_TypeDef)i);
      if(current_state != last_key_state[i]) {
        if(current_state == KEY_PRESSED) {
          uint8_t key_msg[32];
          sprintf((char*)key_msg, "KEY%d Pressed\r\n", i+1);
          BSP_UART_SendData(key_msg, strlen((char*)key_msg));
          BSP_LED_Toggle(BSP_DEBUG_LED);
        }
        last_key_state[i] = current_state;
      }
    }
    
    /* 系统服务喂狗 */
    // SRV_SYSTEM_FeedWatchdog();  // 暂时注释掉，因为没有初始化看门狗 // 暂时注释掉，因为没有初始化看门狗dog  // 暂时注释掉，因为没有初始化看门狗();  // 暂时注释掉，因为没有初始化看门狗
    
    /* 短暂延时 */
    HAL_Delay(1);
  }
  /* USER CODE END 3 */

}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{

  RCC_OscInitTypeDef RCC_OscInitStruct;
  RCC_ClkInitTypeDef RCC_ClkInitStruct;
  RCC_PeriphCLKInitTypeDef PeriphClkInit;

    /**Initializes the CPU, AHB and APB busses clocks
    */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 1;
  RCC_OscInitStruct.PLL.PLLN = 20;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV7;
  RCC_OscInitStruct.PLL.PLLQ = RCC_PLLQ_DIV2;
  RCC_OscInitStruct.PLL.PLLR = RCC_PLLR_DIV2;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Initializes the CPU, AHB and APB busses clocks
    */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_LPUART1|RCC_PERIPHCLK_I2C1
                              |RCC_PERIPHCLK_I2C2|RCC_PERIPHCLK_ADC;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c2ClockSelection = RCC_I2C2CLKSOURCE_PCLK1;
  PeriphClkInit.AdcClockSelection = RCC_ADCCLKSOURCE_PLLSAI1;
  PeriphClkInit.PLLSAI1.PLLSAI1Source = RCC_PLLSOURCE_HSE;
  PeriphClkInit.PLLSAI1.PLLSAI1M = 1;
  PeriphClkInit.PLLSAI1.PLLSAI1N = 8;
  PeriphClkInit.PLLSAI1.PLLSAI1P = RCC_PLLP_DIV7;
  PeriphClkInit.PLLSAI1.PLLSAI1Q = RCC_PLLQ_DIV2;
  PeriphClkInit.PLLSAI1.PLLSAI1R = RCC_PLLR_DIV2;
  PeriphClkInit.PLLSAI1.PLLSAI1ClockOut = RCC_PLLSAI1_ADC1CLK;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure the main internal regulator output voltage
    */
  if (HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure the Systick interrupt time
    */
  HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq()/1000);

    /**Configure the Systick
    */
  HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK);

  /* SysTick_IRQn interrupt configuration */
  HAL_NVIC_SetPriority(SysTick_IRQn, 0, 0);
}

/* ADC1 init function */
static void MX_ADC1_Init(void)
{

  ADC_ChannelConfTypeDef sConfig;

    /**Common config
    */
  hadc1.Instance = ADC1;
  hadc1.Init.ClockPrescaler = ADC_CLOCK_ASYNC_DIV1;
  hadc1.Init.Resolution = ADC_RESOLUTION_12B;
  hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
  hadc1.Init.ScanConvMode = ADC_SCAN_DISABLE;
  hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
  hadc1.Init.LowPowerAutoWait = DISABLE;
  hadc1.Init.ContinuousConvMode = DISABLE;
  hadc1.Init.NbrOfConversion = 1;
  hadc1.Init.DiscontinuousConvMode = DISABLE;
  hadc1.Init.NbrOfDiscConversion = 1;
  hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
  hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
  hadc1.Init.DMAContinuousRequests = DISABLE;
  hadc1.Init.Overrun = ADC_OVR_DATA_PRESERVED;
  hadc1.Init.OversamplingMode = DISABLE;
  if (HAL_ADC_Init(&hadc1) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure Regular Channel
    */
  sConfig.Channel = ADC_CHANNEL_3;
  sConfig.Rank = ADC_REGULAR_RANK_1;
  sConfig.SamplingTime = ADC_SAMPLETIME_2CYCLES_5;
  sConfig.SingleDiff = ADC_SINGLE_ENDED;
  sConfig.OffsetNumber = ADC_OFFSET_NONE;
  sConfig.Offset = 0;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

}

/* I2C1 init function */
static void MX_I2C1_Init(void)
{

  hi2c1.Instance = I2C1;
  hi2c1.Init.Timing = 0x10909CEC;
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.OwnAddress2Masks = I2C_OA2_NOMASK;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure Analogue filter
    */
  if (HAL_I2CEx_ConfigAnalogFilter(&hi2c1, I2C_ANALOGFILTER_ENABLE) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure Digital filter
    */
  if (HAL_I2CEx_ConfigDigitalFilter(&hi2c1, 0) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

}

/* I2C2 init function */
static void MX_I2C2_Init(void)
{

  hi2c2.Instance = I2C2;
  hi2c2.Init.Timing = 0x10909CEC;
  hi2c2.Init.OwnAddress1 = 0;
  hi2c2.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c2.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c2.Init.OwnAddress2 = 0;
  hi2c2.Init.OwnAddress2Masks = I2C_OA2_NOMASK;
  hi2c2.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c2.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c2) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure Analogue filter
    */
  if (HAL_I2CEx_ConfigAnalogFilter(&hi2c2, I2C_ANALOGFILTER_ENABLE) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

    /**Configure Digital filter
    */
  if (HAL_I2CEx_ConfigDigitalFilter(&hi2c2, 0) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

}

/* LPUART1 init function */
static void MX_LPUART1_UART_Init(void)
{

  hlpuart1.Instance = LPUART1;
  hlpuart1.Init.BaudRate = 209700;
  hlpuart1.Init.WordLength = UART_WORDLENGTH_7B;
  hlpuart1.Init.StopBits = UART_STOPBITS_1;
  hlpuart1.Init.Parity = UART_PARITY_NONE;
  hlpuart1.Init.Mode = UART_MODE_TX_RX;
  hlpuart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  hlpuart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
  hlpuart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
  if (HAL_UART_Init(&hlpuart1) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }

}

/* SPI1 init function */
static void MX_SPI1_Init(void)
{
  /* SPI1 parameter configuration*/
  hspi1.Instance = SPI1;
  hspi1.Init.Mode = SPI_MODE_MASTER;
  hspi1.Init.Direction = SPI_DIRECTION_2LINES;
  hspi1.Init.DataSize = SPI_DATASIZE_4BIT;
  hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi1.Init.NSS = SPI_NSS_SOFT;
  hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_2;
  hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi1.Init.CRCPolynomial = 7;
  hspi1.Init.CRCLength = SPI_CRC_LENGTH_DATASIZE;
  hspi1.Init.NSSPMode = SPI_NSS_PULSE_ENABLE;
  if (HAL_SPI_Init(&hspi1) != HAL_OK)
  {
    _Error_Handler(__FILE__, __LINE__);
  }
}
/* 
/* IWDG init function */
/* 
static void MX_IWDG_Init(void)
{
  hiwdg.Instance = IWDG;
  hiwdg.Init.Prescaler = IWDG_PRESCALER_64;
  hiwdg.Init.Window = 4095;
  hiwdg.Init.Reload = 4095;
  if (HAL_IWDG_Init(&hiwdg) != HAL_OK)
  {
    Error_Handler();
  }
}
*/
/** Configure pins as
 * Analog
 * Input  
 * Output
 * EVENT_OUT
 * EXTI
 */
static void MX_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct;

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOH_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13|GPIO_PIN_8|GPIO_PIN_9, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0|GPIO_PIN_2|GPIO_PIN_5|GPIO_PIN_8
                          |GPIO_PIN_9, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_12, GPIO_PIN_RESET);

  /*Configure GPIO pins : PC13 PC8 PC9 */
  GPIO_InitStruct.Pin = GPIO_PIN_13|GPIO_PIN_8|GPIO_PIN_9;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

  /*Configure GPIO pins : PA1 PA2 PA3 */
  GPIO_InitStruct.Pin = GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pins : PC4 PC5 PC6 PC7 */
  GPIO_InitStruct.Pin = GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

  /*Configure GPIO pins : PB0 PB2 PB5 PB8 PB9 */
  GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_2|GPIO_PIN_5|GPIO_PIN_8|GPIO_PIN_9;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /*Configure GPIO pins : PB1 PB3 PB4 */
  GPIO_InitStruct.Pin = GPIO_PIN_1|GPIO_PIN_3|GPIO_PIN_4;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /*Configure GPIO pin : PA12 */
  GPIO_InitStruct.Pin = GPIO_PIN_12;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pin : PD2 */
  GPIO_InitStruct.Pin = GPIO_PIN_2;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void _Error_Handler(char *file, int line)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */

  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     tex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
