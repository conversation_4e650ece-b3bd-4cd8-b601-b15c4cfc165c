#ifndef __BSP_IO_H__
#define __BSP_IO_H__

#include "stm32l4xx_hal.h"

/* IO通道定义 */
typedef enum {
    IO_CHANNEL1 = 0,
    IO_CHANNEL2
} IO_Channel_TypeDef;

/* IO状态定义 */
typedef enum {
    IO_LOW = 0,
    IO_HIGH
} IO_State_TypeDef;

/* IO状态变化回调函数类型 */
typedef void (*IO_StateChangeCallback_TypeDef)(IO_Channel_TypeDef channel, IO_State_TypeDef state);

/* 函数声明 */
HAL_StatusTypeDef BSP_IO_Init(void);
void BSP_IO_Scan(void);
IO_State_TypeDef BSP_IO_GetState(IO_Channel_TypeDef channel);
void BSP_IO_RegisterCallback(IO_Channel_TypeDef channel, IO_StateChangeCallback_TypeDef callback);
void BSP_IO_SetDebounceThreshold(IO_Channel_TypeDef channel, uint8_t threshold);

#endif /* __BSP_IO_H__ */

