/**
 * @file    drv_max31856.c
 * @brief   MAX31856热电偶芯片驱动实现
 * <AUTHOR>
 * @date    2025-01-28
 */

#include "drivers/drv_max31856.h"

/* SPI句柄 */
static SPI_HandleTypeDef *max31856_hspi;

/* CS引脚定义 */
static const struct {
    GPIO_TypeDef *GPIOx;
    uint16_t GPIO_Pin;
} cs_pins[2] = {
    {GPIOB, GPIO_PIN_0},  // CHANNEL1
    {GPIOB, GPIO_PIN_2}   // CHANNEL2
};

/**
 * @brief  CS引脚控制
 * @param  channel: MAX31856通道
 * @param  state: 引脚状态
 * @retval None
 */
static void MAX31856_CS_Control(MAX31856_Channel_TypeDef channel, GPIO_PinState state)
{
    if(channel < 2) {
        HAL_GPIO_WritePin(cs_pins[channel].GPIOx, cs_pins[channel].GPIO_Pin, state);
    }
}

/**
 * @brief  写寄存器
 * @param  channel: MAX31856通道
 * @param  reg_addr: 寄存器地址
 * @param  data: 写入数据
 * @retval HAL状态
 */
static HAL_StatusTypeDef MAX31856_WriteRegister(MAX31856_Channel_TypeDef channel, uint8_t reg_addr, uint8_t data)
{
    uint8_t tx_data[2];
    HAL_StatusTypeDef status;
    
    tx_data[0] = reg_addr | 0x80;  // 写操作，最高位置1
    tx_data[1] = data;
    
    MAX31856_CS_Control(channel, GPIO_PIN_RESET);
    status = HAL_SPI_Transmit(max31856_hspi, tx_data, 2, 100);
    MAX31856_CS_Control(channel, GPIO_PIN_SET);
    
    return status;
}

/**
 * @brief  读寄存器
 * @param  channel: MAX31856通道
 * @param  reg_addr: 寄存器地址
 * @param  data: 读取数据指针
 * @retval HAL状态
 */
static HAL_StatusTypeDef MAX31856_ReadRegister(MAX31856_Channel_TypeDef channel, uint8_t reg_addr, uint8_t *data)
{
    uint8_t tx_data = reg_addr & 0x7F;  // 读操作，最高位置0
    HAL_StatusTypeDef status;
    
    MAX31856_CS_Control(channel, GPIO_PIN_RESET);
    status = HAL_SPI_Transmit(max31856_hspi, &tx_data, 1, 100);
    if(status == HAL_OK) {
        status = HAL_SPI_Receive(max31856_hspi, data, 1, 100);
    }
    MAX31856_CS_Control(channel, GPIO_PIN_SET);
    
    return status;
}

/**
 * @brief  读多个寄存器
 * @param  channel: MAX31856通道
 * @param  reg_addr: 起始寄存器地址
 * @param  data: 读取数据指针
 * @param  len: 读取长度
 * @retval HAL状态
 */
static HAL_StatusTypeDef MAX31856_ReadMultipleRegisters(MAX31856_Channel_TypeDef channel, uint8_t reg_addr, uint8_t *data, uint8_t len)
{
    uint8_t tx_data = reg_addr & 0x7F;  // 读操作，最高位置0
    HAL_StatusTypeDef status;
    
    MAX31856_CS_Control(channel, GPIO_PIN_RESET);
    status = HAL_SPI_Transmit(max31856_hspi, &tx_data, 1, 100);
    if(status == HAL_OK) {
        status = HAL_SPI_Receive(max31856_hspi, data, len, 100);
    }
    MAX31856_CS_Control(channel, GPIO_PIN_SET);
    
    return status;
}

/**
 * @brief  MAX31856初始化
 * @param  hspi: SPI句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_MAX31856_Init(SPI_HandleTypeDef *hspi)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    max31856_hspi = hspi;
    
    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    /* 配置CS引脚 */
    GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    
    /* 配置FAULT引脚 */
    GPIO_InitStruct.Pin = GPIO_PIN_4;  // MAX_FAULT1
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    GPIO_InitStruct.Pin = GPIO_PIN_3;  // MAX_FAULT2
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    /* 配置DRDY引脚 */
    GPIO_InitStruct.Pin = GPIO_PIN_5;  // MAX_DRDY1
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    GPIO_InitStruct.Pin = GPIO_PIN_1;  // MAX_DRDY2
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    
    /* 初始化CS引脚为高电平 */
    MAX31856_CS_Control(MAX31856_CHANNEL1, GPIO_PIN_SET);
    MAX31856_CS_Control(MAX31856_CHANNEL2, GPIO_PIN_SET);
    
    HAL_Delay(100);  // 等待芯片稳定
    
    /* 配置两个通道为K型热电偶 */
    DRV_MAX31856_Config(MAX31856_CHANNEL1, MAX31856_TC_TYPE_K);
    DRV_MAX31856_Config(MAX31856_CHANNEL2, MAX31856_TC_TYPE_K);
    
    return HAL_OK;
}

/**
 * @brief  配置MAX31856
 * @param  channel: MAX31856通道
 * @param  tc_type: 热电偶类型
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_MAX31856_Config(MAX31856_Channel_TypeDef channel, MAX31856_TCType_TypeDef tc_type)
{
    HAL_StatusTypeDef status;
    
    /* 配置CR0寄存器：自动转换模式，50Hz滤波 */
    status = MAX31856_WriteRegister(channel, MAX31856_REG_CR0, 0x80);
    if(status != HAL_OK) return status;
    
    /* 配置CR1寄存器：设置热电偶类型，平均1次采样 */
    status = MAX31856_WriteRegister(channel, MAX31856_REG_CR1, tc_type << 4);
    if(status != HAL_OK) return status;
    
    /* 配置故障屏蔽寄存器：不屏蔽任何故障 */
    status = MAX31856_WriteRegister(channel, MAX31856_REG_MASK, 0x00);
    
    return status;
}

/**
 * @brief  读取热电偶温度
 * @param  channel: MAX31856通道
 * @param  temperature: 温度值指针
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_MAX31856_ReadTemp(MAX31856_Channel_TypeDef channel, float *temperature)
{
    uint8_t temp_data[3];
    HAL_StatusTypeDef status;
    int32_t temp_raw;
    
    /* 读取线性化温度寄存器 */
    status = MAX31856_ReadMultipleRegisters(channel, MAX31856_REG_LTCBH, temp_data, 3);
    if(status != HAL_OK) {
        return status;
    }
    
    /* 组合24位温度数据 */
    temp_raw = ((int32_t)temp_data[0] << 16) | ((int32_t)temp_data[1] << 8) | temp_data[2];
    
    /* 检查符号位并扩展 */
    if(temp_raw & 0x800000) {
        temp_raw |= 0xFF000000;  // 符号扩展
    }
    
    /* 转换为摄氏度，LSB = 0.0078125°C */
    *temperature = (float)temp_raw * 0.0078125f / 256.0f;
    
    return HAL_OK;
}

/**
 * @brief  读取冷端补偿温度
 * @param  channel: MAX31856通道
 * @param  temperature: 温度值指针
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_MAX31856_ReadColdJunctionTemp(MAX31856_Channel_TypeDef channel, float *temperature)
{
    uint8_t temp_data[2];
    HAL_StatusTypeDef status;
    int16_t temp_raw;
    
    /* 读取冷端温度寄存器 */
    status = MAX31856_ReadMultipleRegisters(channel, MAX31856_REG_CJTH, temp_data, 2);
    if(status != HAL_OK) {
        return status;
    }
    
    /* 组合16位温度数据 */
    temp_raw = ((int16_t)temp_data[0] << 8) | temp_data[1];
    
    /* 转换为摄氏度，LSB = 0.015625°C */
    *temperature = (float)temp_raw * 0.015625f / 256.0f;
    
    return HAL_OK;
}

/**
 * @brief  读取故障状态
 * @param  channel: MAX31856通道
 * @retval 故障状态字节
 */
uint8_t DRV_MAX31856_ReadFault(MAX31856_Channel_TypeDef channel)
{
    uint8_t fault_status = 0;
    
    MAX31856_ReadRegister(channel, MAX31856_REG_SR, &fault_status);
    
    return fault_status;
}

/**
 * @brief  清除故障状态
 * @param  channel: MAX31856通道
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_MAX31856_ClearFault(MAX31856_Channel_TypeDef channel)
{
    uint8_t cr0_value;
    HAL_StatusTypeDef status;
    
    /* 读取CR0寄存器 */
    status = MAX31856_ReadRegister(channel, MAX31856_REG_CR0, &cr0_value);
    if(status != HAL_OK) return status;
    
    /* 设置故障清除位 */
    cr0_value |= 0x02;
    status = MAX31856_WriteRegister(channel, MAX31856_REG_CR0, cr0_value);
    if(status != HAL_OK) return status;
    
    /* 清除故障清除位 */
    cr0_value &= ~0x02;
    status = MAX31856_WriteRegister(channel, MAX31856_REG_CR0, cr0_value);
    
    return status;
}