/**
 * @file    app_display.c
 * @brief   显示应用层实现
 * <AUTHOR>
 * @date    2025-01-29
 */

#include "app/app_display.h"
#include <string.h>
#include <stdio.h>

/* 当前显示页面 */
static Display_Page_TypeDef current_page = DISPLAY_PAGE_MAIN;

/* 显示数据 */
static Display_Data_TypeDef display_data;

/* 更新标志 */
static uint8_t update_flag = 1;

/* 页面切换计数器 */
static uint32_t page_switch_counter = 0;

/**
 * @brief  显示模块初始化
 * @param  hi2c: I2C句柄
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_DISPLAY_Init(I2C_HandleTypeDef *hi2c)
{
    /* 初始化OLED驱动 */
    if(DRV_OLED_Init(hi2c) != HAL_OK) {
        return HAL_ERROR;
    }
    
    /* 初始化显示数据 */
    memset(&display_data, 0, sizeof(display_data));
    strcpy(display_data.title, "Testboard v1.0");
    
    /* 显示启动信息 */
    APP_DISPLAY_ShowRunningStatus();
    
    return HAL_OK;
}

/**
 * @brief  设置显示页面
 * @param  page: 页面类型
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_DISPLAY_SetPage(Display_Page_TypeDef page)
{
    if(page >= DISPLAY_PAGE_MAX) {
        return HAL_ERROR;
    }
    
    current_page = page;
    update_flag = 1;
    
    return HAL_OK;
}

/**
 * @brief  更新显示数据
 * @param  data: 显示数据指针
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_DISPLAY_UpdateData(Display_Data_TypeDef *data)
{
    if(data == NULL) {
        return HAL_ERROR;
    }
    
    memcpy(&display_data, data, sizeof(Display_Data_TypeDef));
    update_flag = 1;
    
    return HAL_OK;
}

/**
 * @brief  显示消息
 * @param  message: 消息字符串
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_DISPLAY_ShowMessage(const char *message)
{
    DRV_OLED_Clear();
    DRV_OLED_DrawString(0, 0, "MESSAGE:", OLED_FONT_6x8);
    DRV_OLED_DrawString(0, 16, message, OLED_FONT_6x8);
    DRV_OLED_Update();
    
    return HAL_OK;
}

/**
 * @brief  显示运行状态
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef APP_DISPLAY_ShowRunningStatus(void)
{
    DRV_OLED_Clear();
    DRV_OLED_DrawString(10, 10, "Testboard", OLED_FONT_6x8);
    DRV_OLED_DrawString(20, 25, "System", OLED_FONT_6x8);
    DRV_OLED_DrawString(15, 40, "Running", OLED_FONT_6x8);
    DRV_OLED_Update();
    
    return HAL_OK;
}

/**
 * @brief  显示主页面
 * @param  None
 * @retval None
 */
static void DisplayMainPage(void)
{
    DRV_OLED_Clear();
    
    /* 标题 */
    DRV_OLED_DrawString(0, 0, display_data.title, OLED_FONT_6x8);
    
    /* 系统状态 */
    char status_str[32];
    sprintf(status_str, "K:%02X I:%02X R:%02X", 
            display_data.key_status, 
            display_data.io_status, 
            display_data.relay_status);
    DRV_OLED_DrawString(0, 16, status_str, OLED_FONT_6x8);
    
    /* 温度信息 */
    DRV_OLED_DrawString(0, 32, "TEMP:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(36, 32, display_data.temp_max31856_ch1, 1, OLED_FONT_6x8);
    DRV_OLED_DrawString(72, 32, "C", OLED_FONT_6x8);
    
    /* 电压信息 */
    DRV_OLED_DrawString(0, 48, "ADC:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(30, 48, display_data.voltage_ads1115[0], 2, OLED_FONT_6x8);
    DRV_OLED_DrawString(72, 48, "V", OLED_FONT_6x8);
}

/**
 * @brief  显示传感器页面
 * @param  None
 * @retval None
 */
static void DisplaySensorPage(void)
{
    DRV_OLED_Clear();
    
    /* 标题 */
    DRV_OLED_DrawString(0, 0, "SENSORS", OLED_FONT_6x8);
    
    /* 温度传感器 */
    DRV_OLED_DrawString(0, 12, "T1:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(20, 12, display_data.temp_max31856_ch1, 1, OLED_FONT_6x8);
    DRV_OLED_DrawString(0, 24, "T2:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(20, 24, display_data.temp_ds18b20_ch1, 1, OLED_FONT_6x8);
    
    /* ADC */
    DRV_OLED_DrawString(0, 36, "A1:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(20, 36, display_data.voltage_ads1115[0], 2, OLED_FONT_6x8);
    DRV_OLED_DrawString(0, 48, "A2:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(20, 48, display_data.voltage_internal[0], 2, OLED_FONT_6x8);
}

/**
 * @brief  显示状态页面
 * @param  None
 * @retval None
 */
static void DisplayStatusPage(void)
{
    DRV_OLED_Clear();
    
    /* 标题 */
    DRV_OLED_DrawString(0, 0, "STATUS", OLED_FONT_6x8);
    
    /* 按键状态 */
    char key_str[16];
    sprintf(key_str, "KEY: %02X", display_data.key_status);
    DRV_OLED_DrawString(0, 12, key_str, OLED_FONT_6x8);
    
    /* IO状态 */
    char io_str[16];
    sprintf(io_str, "IO:  %02X", display_data.io_status);
    DRV_OLED_DrawString(0, 24, io_str, OLED_FONT_6x8);
    
    /* 继电器状态 */
    char relay_str[16];
    sprintf(relay_str, "REL: %02X", display_data.relay_status);
    DRV_OLED_DrawString(0, 36, relay_str, OLED_FONT_6x8);
    
    /* 电流信息 */
    DRV_OLED_DrawString(0, 48, "I:", OLED_FONT_6x8);
    DRV_OLED_DrawFloat(12, 48, display_data.ina226_current, 3, OLED_FONT_6x8);
    DRV_OLED_DrawString(60, 48, "A", OLED_FONT_6x8);
}

/**
 * @brief  显示处理任务
 * @param  None
 * @retval None
 */
void APP_DISPLAY_Process(void)
{
    static uint32_t last_update_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    /* 每500ms更新一次显示 */
    if(current_time - last_update_time >= 500) {
        last_update_time = current_time;
        
        /* 自动切换页面（每5秒切换一次） */
        page_switch_counter++;
        if(page_switch_counter >= 10) {  // 10 * 500ms = 5s
            page_switch_counter = 0;
            current_page = (Display_Page_TypeDef)((current_page + 1) % DISPLAY_PAGE_MAX);
            update_flag = 1;
        }
        
        /* 更新显示内容 */
        if(update_flag) {
            update_flag = 0;
            
            switch(current_page) {
                case DISPLAY_PAGE_MAIN:
                    DisplayMainPage();
                    break;
                    
                case DISPLAY_PAGE_SENSOR:
                    DisplaySensorPage();
                    break;
                    
                case DISPLAY_PAGE_STATUS:
                    DisplayStatusPage();
                    break;
                    
                default:
                    break;
            }
            
            DRV_OLED_Update();
        }
    }
}