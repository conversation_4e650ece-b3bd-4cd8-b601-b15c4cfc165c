#include "bsp/bsp_led.h"

/* LED配置结构体 */
typedef struct {
    GPIO_TypeDef* GPIOx;
    uint16_t GPIO_Pin;
    LED_State_TypeDef state;
    uint32_t blink_period;
    uint32_t last_toggle;
} LED_Config_TypeDef;

/* LED配置表 */
static LED_Config_TypeDef led_config[2] = {
    {GPIOC, GPIO_PIN_13, LED_OFF, 0, 0},  // SYS_LED
    {GPIOC, GPIO_PIN_8,  LED_OFF, 0, 0}   // DEBUG_LED
};

HAL_StatusTypeDef BSP_LED_Init(void)
{
    return HAL_OK;
}

void BSP_LED_On(LED_TypeDef led)
{
    if(led < 2) {
        HAL_GPIO_WritePin(led_config[led].GPIOx, led_config[led].GPIO_Pin, GPIO_PIN_SET);
        led_config[led].state = LED_ON;
    }
}

void BSP_LED_Off(LED_TypeDef led)
{
    if(led < 2) {
        HAL_GPIO_WritePin(led_config[led].GPIOx, led_config[led].GPIO_Pin, GPIO_PIN_RESET);
        led_config[led].state = LED_OFF;
    }
}

void BSP_LED_Toggle(LED_TypeDef led)
{
    if(led < 2) {
        HAL_GPIO_TogglePin(led_config[led].GPIOx, led_config[led].GPIO_Pin);
    }
}

void BSP_LED_SetBlink(LED_TypeDef led, uint32_t period_ms)
{
    if(led < 2) {
        led_config[led].state = LED_BLINK;
        led_config[led].blink_period = period_ms;
        led_config[led].last_toggle = HAL_GetTick();
    }
}

void BSP_LED_Process(void)
{
    for(int i = 0; i < 2; i++) {
        if(led_config[i].state == LED_BLINK) {
            if(HAL_GetTick() - led_config[i].last_toggle >= led_config[i].blink_period) {
                led_config[i].last_toggle = HAL_GetTick();
                HAL_GPIO_TogglePin(led_config[i].GPIOx, led_config[i].GPIO_Pin);
            }
        }
    }
}




