Configuration	testboard
STM32CubeMX 	4.24.0
Date	07/18/2025
MCU	STM32L431RCTx



PERIPHERALS	MODES	FUNCTIONS	PINS
ADC1	IN3 Single-ended	ADC1_IN3	PC2
ADC1	IN4 Single-ended	ADC1_IN4	PC3
I2C1	I2C	I2C1_SCL	PB6
I2C1	I2C	I2C1_SDA	PB7
I2C2	I2C	I2C2_SCL	PB10
I2C2	I2C	I2C2_SDA	PB11
LPUART1	Asynchronous	LPUART1_RX	PC0
LPUART1	Asynchronous	LPUART1_TX	PC1
RCC	Crystal/Ceramic Resonator	RCC_OSC_IN	PH0-OSC_IN (PH0)
RCC	Crystal/Ceramic Resonator	RCC_OSC_OUT	PH1-OSC_OUT (PH1)
RCC	Crystal/Ceramic Resonator	RCC_OSC32_IN	PC14-OSC32_IN (PC14)
RCC	Crystal/Ceramic Resonator	RCC_OSC32_OUT	PC15-OSC32_OUT (PC15)
SPI1	Full-Duplex Master	SPI1_MISO	PA6
SPI1	Full-Duplex Master	SPI1_MOSI	PA7
SPI1	Full-Duplex Master	SPI1_SCK	PA5
SYS	SysTick	SYS_VS_Systick	VP_SYS_VS_Systick



Pin Nb	PINs	FUNCTIONs	LABELs
2	PC13	GPIO_Output	
3	PC14-OSC32_IN (PC14)	RCC_OSC32_IN	
4	PC15-OSC32_OUT (PC15)	RCC_OSC32_OUT	
5	PH0-OSC_IN (PH0)	RCC_OSC_IN	
6	PH1-OSC_OUT (PH1)	RCC_OSC_OUT	
8	PC0	LPUART1_RX	
9	PC1	LPUART1_TX	
10	PC2	ADC1_IN3	
11	PC3	ADC1_IN4	
15	PA1	GPIO_Input	
16	PA2	GPIO_Input	
17	PA3	GPIO_Input	
21	PA5	SPI1_SCK	
22	PA6	SPI1_MISO	
23	PA7	SPI1_MOSI	
24	PC4	GPIO_Input	
25	PC5	GPIO_Input	
26	PB0	GPIO_Output	
27	PB1	GPIO_Input	
28	PB2	GPIO_Output	
29	PB10	I2C2_SCL	
30	PB11	I2C2_SDA	
37	PC6	GPIO_Input	
38	PC7	GPIO_Input	
39	PC8	GPIO_Output	
40	PC9	GPIO_Output	
45	PA12	GPIO_Output	
54	PD2	GPIO_Input	
55	PB3 (JTDO-TRACESWO)	GPIO_Input	
56	PB4 (NJTRST)	GPIO_Input	
57	PB5	GPIO_Output	
58	PB6	I2C1_SCL	
59	PB7	I2C1_SDA	
61	PB8	GPIO_Output	
62	PB9	GPIO_Output	



SOFTWARE PROJECT

Project Settings : 
Project Name : testboard
Project Folder : G:\work\AHD_TEST\testboard\testboard
Toolchain / IDE : MDK-ARM V5
Firmware Package Name and Version : STM32Cube FW_L4 V1.11.0


Code Generation Settings : 
STM32Cube Firmware Library Package : Copy all used libraries into the project folder
Generate peripheral initialization as a pair of '.c/.h' files per peripheral : No
Backup previously generated files when re-generating : No
Delete previously generated files when not re-generated : Yes
Set all free pins as analog (to optimize the power consumption) : No


Toolchains Settings : 
Compiler Optimizations : Balanced Size/Speed






