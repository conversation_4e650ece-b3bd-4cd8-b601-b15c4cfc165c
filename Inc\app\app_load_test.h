#ifndef __APP_LOAD_TEST_H__
#define __APP_LOAD_TEST_H__

#include "stm32l4xx_hal.h"
#include "stm32l4xx_hal_tim.h"

/* 负载测试模式定义 */
typedef enum {
    LOAD_MODE_OFF = 0,      // 关闭
    LOAD_MODE_CONSTANT,     // 恒定负载
    LOAD_MODE_PULSE,        // 脉冲负载
    LOAD_MODE_RAMP,         // 斜坡负载
    LOAD_MODE_SINE          // 正弦负载
} Load_Mode_TypeDef;

/* 负载测试参数结构体 */
typedef struct {
    Load_Mode_TypeDef mode;     // 测试模式
    uint16_t duty;              // 占空比 (0-1000)
    uint16_t frequency;         // 频率 (Hz)
    uint16_t amplitude;         // 幅度
    uint16_t offset;            // 偏移
    uint16_t duration;          // 持续时间 (秒)
} Load_Param_TypeDef;

/* 负载测试状态结构体 */
typedef struct {
    uint8_t is_running;         // 是否运行中
    Load_Mode_TypeDef mode;     // 当前模式
    uint16_t current_duty;      // 当前占空比
    uint32_t elapsed_time;      // 已运行时间
} LoadTest_Status_TypeDef;

/* 负载测试配置结构体 */
typedef struct {
    Load_Mode_TypeDef mode;
    uint16_t duty;
    uint16_t frequency;
    uint32_t duration;
} LoadTest_Config_TypeDef;

/* PWM相关定义 */
#define LOAD_PWM_CHANNEL    TIM_CHANNEL_1
#define PWM_PERIOD          1000

/* 函数声明 */
HAL_StatusTypeDef APP_LOAD_TEST_Init(TIM_HandleTypeDef *htim);
HAL_StatusTypeDef APP_LOAD_TEST_SetMode(Load_Mode_TypeDef mode);
HAL_StatusTypeDef APP_LOAD_TEST_SetParams(Load_Param_TypeDef *params);
HAL_StatusTypeDef APP_LOAD_TEST_Start(LoadTest_Config_TypeDef *config);
HAL_StatusTypeDef APP_LOAD_TEST_Stop(void);
LoadTest_Status_TypeDef APP_LOAD_TEST_GetStatus(void);
void APP_LOAD_TEST_Process(void);

#endif /* __APP_LOAD_TEST_H__ */


