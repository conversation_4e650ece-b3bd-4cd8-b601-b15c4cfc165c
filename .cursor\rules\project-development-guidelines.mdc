---
description: 
globs: 
alwaysApply: false
---
# 项目开发规范

本项目开发需严格遵循以下规范，以确保代码质量、项目进度和团队协作效率。

## 1. 代码结构与模块化
- **严格遵循模块划分**：所有新代码必须放置在 [项目计划.md](mdc:docs/项目计划.md) 中定义的 `/Inc` 和 `/Src` 目录下的相应模块（如 `bsp/`、`drivers/`、`app/`、`service/`）中。
- **文件命名规范**：头文件 `.h` 和源文件 `.c` 应与模块名保持一致，例如 `bsp_led.h` 和 `bsp_led.c`。
- **禁止随意修改HAL库文件**：基于STM32CubeMX生成的HAL库文件，除CubeMX自动生成部分，原则上禁止手动修改。所有定制功能和驱动应通过外设句柄在应用层调用或通过回调函数实现。
- **遵循详细设计**：代码实现必须与 [详细设计.md](mdc:docs/详细设计.md) 中的接口定义、数据结构和功能描述保持一致。

## 2. 编码规范
- **注释规范**：关键函数、复杂逻辑块、全局变量和宏定义必须添加清晰的注释，说明其功能、参数、返回值和注意事项。
- **变量命名**：采用有意义的英文命名，遵循小驼峰命名法（`camelCase`）或下划线命名法（`snake_case`），根据项目现有风格保持一致。
- **函数命名**：遵循模块前缀命名约定，如 `BSP_LED_Init()`、`DRV_MAX31856_ReadTemp()`。
- **错误处理**：所有外部接口调用和关键操作必须考虑错误处理机制，使用协议中定义的错误码进行上报。
- **资源管理**：确保内存、外设等资源正确初始化和释放，避免内存泄漏或资源冲突。

## 3. 通信协议规范
- **严格遵守协议定义**：所有与串口通信相关的代码（包括发送和接收）必须严格按照 [通信协议.md](mdc:docs/通信协议.md) 中定义的帧格式、功能类型、端口号和控制信息进行实现。
- **数据解析与封装**：协议解析器和数据上报模块应健壮，能够处理异常数据和错误帧。
- **立即生效原则**：所有通过协议配置的参数，应在接收到并解析成功后立即生效。

## 4. 文档管理与更新
- **项目入口**：[README.md](mdc:docs/README.md) 是项目的总览和导航入口，应保持最新。
- **需求同步**：任何功能需求变更必须首先更新 [项目需求.md](mdc:docs/项目需求.md)。
- **协议同步**：通信协议的任何修改必须同步更新 [通信协议.md](mdc:docs/通信协议.md)。
- **计划同步**：项目阶段、模块划分、优先级等调整必须更新 [项目计划.md](mdc:docs/项目计划.md)。
- **设计同步**：模块内部设计、接口变更、数据结构调整必须更新 [详细设计.md](mdc:docs/详细设计.md)。
- **工作日志**：开发人员每日或每周更新 [工作日志.md](mdc:docs/工作日志.md)，记录开发进度、遇到的问题、解决方案和遗留问题。
- **测试计划**：未来需要创建并维护 [测试计划.md](mdc:docs/测试计划.md) ，详细说明测试范围、用例和结果。

## 5. 版本控制与提交
- **小步提交**：频繁提交，每次提交只包含一个独立、完整的功能或问题修复。
- **提交信息**：提交信息应清晰、简洁、有意义，说明本次提交的目的和内容。
- **分支管理**：遵循团队定义的分支策略（例如，`main` 主分支、`dev` 开发分支、功能分支等）。

## 6. 测试与验收
- **单元测试**：每个模块开发完成后，应进行单元测试，确保其功能正确性。
- **集成测试**：多个模块集成后，进行集成测试，验证模块间协作是否正常。
- **系统测试**：在项目接近完成时，根据 [项目需求.md](mdc:docs/项目需求.md) 进行全面的系统测试，确保满足所有功能和性能指标。
- **验收标准**：以 [项目需求.md](mdc:docs/项目需求.md) 和 [通信协议.md](mdc:docs/通信协议.md) 为主要验收依据。

## 7. 风险管理
- **及时上报**：开发过程中遇到的任何技术难题、进度延误或潜在风险，应及时在 [工作日志.md](mdc:docs/工作日志.md) 中记录并向团队上报。 

## 8. 其他
- 确保开发环境与 [README.md](mdc:docs/README.md) 中描述的一致。
- 持续学习和研究相关芯片手册，解决未明确的控制信息参数。

