/**
 * @file    drv_ina226.h
 * @brief   INA226电流监测芯片驱动头文件
 * <AUTHOR>
 * @date    2025-01-29
 */

#ifndef __DRV_INA226_H
#define __DRV_INA226_H

#include "main.h"

/* INA226 I2C地址 */
#define INA226_I2C_ADDR         0x40  // A1=0, A0=0

/* INA226寄存器地址 */
#define INA226_REG_CONFIG       0x00  // 配置寄存器
#define INA226_REG_SHUNT_V      0x01  // 分流电压寄存器
#define INA226_REG_BUS_V        0x02  // 总线电压寄存器
#define INA226_REG_POWER        0x03  // 功率寄存器
#define INA226_REG_CURRENT      0x04  // 电流寄存器
#define INA226_REG_CALIBRATION  0x05  // 校准寄存器
#define INA226_REG_MASK_ENABLE  0x06  // 屏蔽/使能寄存器
#define INA226_REG_ALERT_LIMIT  0x07  // 报警限制寄存器
#define INA226_REG_MANUF_ID     0xFE  // 制造商ID
#define INA226_REG_DIE_ID       0xFF  // 芯片ID

/* INA226配置位定义 */
#define INA226_CONFIG_RESET     0x8000  // 复位位
#define INA226_CONFIG_AVG_1     0x0000  // 平均1次
#define INA226_CONFIG_AVG_4     0x0200  // 平均4次
#define INA226_CONFIG_AVG_16    0x0400  // 平均16次
#define INA226_CONFIG_AVG_64    0x0600  // 平均64次
#define INA226_CONFIG_AVG_128   0x0800  // 平均128次
#define INA226_CONFIG_AVG_256   0x0A00  // 平均256次
#define INA226_CONFIG_AVG_512   0x0C00  // 平均512次
#define INA226_CONFIG_AVG_1024  0x0E00  // 平均1024次

#define INA226_CONFIG_VBUSCT_140US   0x0000  // 总线电压转换时间140us
#define INA226_CONFIG_VBUSCT_204US   0x0040  // 总线电压转换时间204us
#define INA226_CONFIG_VBUSCT_332US   0x0080  // 总线电压转换时间332us
#define INA226_CONFIG_VBUSCT_588US   0x00C0  // 总线电压转换时间588us
#define INA226_CONFIG_VBUSCT_1100US  0x0100  // 总线电压转换时间1.1ms
#define INA226_CONFIG_VBUSCT_2116US  0x0140  // 总线电压转换时间2.116ms
#define INA226_CONFIG_VBUSCT_4156US  0x0180  // 总线电压转换时间4.156ms
#define INA226_CONFIG_VBUSCT_8244US  0x01C0  // 总线电压转换时间8.244ms

#define INA226_CONFIG_VSHCT_140US    0x0000  // 分流电压转换时间140us
#define INA226_CONFIG_VSHCT_204US    0x0008  // 分流电压转换时间204us
#define INA226_CONFIG_VSHCT_332US    0x0010  // 分流电压转换时间332us
#define INA226_CONFIG_VSHCT_588US    0x0018  // 分流电压转换时间588us
#define INA226_CONFIG_VSHCT_1100US   0x0020  // 分流电压转换时间1.1ms
#define INA226_CONFIG_VSHCT_2116US   0x0028  // 分流电压转换时间2.116ms
#define INA226_CONFIG_VSHCT_4156US   0x0030  // 分流电压转换时间4.156ms
#define INA226_CONFIG_VSHCT_8244US   0x0038  // 分流电压转换时间8.244ms

#define INA226_CONFIG_MODE_POWER_DOWN    0x0000  // 掉电模式
#define INA226_CONFIG_MODE_SHUNT_TRIG    0x0001  // 分流电压触发
#define INA226_CONFIG_MODE_BUS_TRIG      0x0002  // 总线电压触发
#define INA226_CONFIG_MODE_SHUNT_BUS_TRIG 0x0003  // 分流和总线电压触发
#define INA226_CONFIG_MODE_SHUNT_CONT    0x0005  // 分流电压连续
#define INA226_CONFIG_MODE_BUS_CONT      0x0006  // 总线电压连续
#define INA226_CONFIG_MODE_SHUNT_BUS_CONT 0x0007  // 分流和总线电压连续

/* 函数声明 */
HAL_StatusTypeDef DRV_INA226_Init(I2C_HandleTypeDef *hi2c);
HAL_StatusTypeDef DRV_INA226_SetCalibration(float shunt_resistance, float max_current);
HAL_StatusTypeDef DRV_INA226_ReadVoltage(float *voltage);
HAL_StatusTypeDef DRV_INA226_ReadCurrent(float *current);
HAL_StatusTypeDef DRV_INA226_ReadPower(float *power);
HAL_StatusTypeDef DRV_INA226_ReadShuntVoltage(float *shunt_voltage);
uint8_t DRV_INA226_IsReady(void);

#endif /* __DRV_INA226_H */
