# 多功能测试板项目

## 项目概述
本项目是基于STM32L431RCT6的多功能测试板，主要用于研发测试硬件或自动化测试。测试板集成了多种功能接口，包括内置ADC、外置ADC、显示屏、串口、按键、指示灯、数字信号输入检测、继电器控制输出、热电偶测温、动态负载测试等功能。

## 硬件平台
- MCU: STM32L431RCT6
- 外部晶振: 8MHz
- RTC晶振: 32.768KHz

## 功能模块
- 按键检测 (4路)
- 外部输入IO检测 (2路)
- 继电器控制输出 (2路)
- 热电偶检测 (MAX31856MUD, 2路)
- 温度传感器 (DS18B20, 2路)
- 外部ADC (ADS1115, 4通道)
- 电流监测 (INA226AIDGSR)
- 动态负载测试 (UCC27517DBVR)
- 内部ADC采集 (2通道)
- OLED显示
- 串口通信 (115200波特率)
- 系统指示灯

## 文档导航
- [项目需求](项目需求.md) - 详细的项目需求说明
- [通信协议](通信协议.md) - 串口通信协议规范
- [项目计划](项目计划.md) - 开发计划和模块划分
- [详细设计](详细设计.md) - 模块级别的详细设计文档
- [工作日志](工作日志.md) - 开发进度和问题记录
- [测试计划](测试计划.md) - 测试策略和用例

## 开发环境
- 开发工具: STM32CubeMX + 编译器
- 固件库: STM32 HAL库
- 调试工具: 串口调试助手、示波器等

## 目录结构
```
testboard/
├── Inc/             - 头文件目录
├── Src/             - 源文件目录
├── docs/            - 项目文档
└── .mxproject       - STM32CubeMX项目文件
└── testboard.ioc    - STM32CubeMX配置文件
```

## 使用说明
待补充

## 开发团队
待补充

## 版本历史
- v0.1 - 初始版本，基础框架搭建 