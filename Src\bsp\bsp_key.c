#include "bsp/bsp_key.h"

/* 按键配置结构体 */
typedef struct {
    GPIO_TypeDef* GPIOx;
    uint16_t GPIO_Pin;
    KEY_State_TypeDef state;
    uint32_t debounce_time;
    uint32_t last_change;
} KEY_Config_TypeDef;

/* 按键配置表 */
static KEY_Config_TypeDef key_config[4] = {
    {GPIOA, GPIO_PIN_1, KEY_RELEASED, 50, 0},  // KEY1
    {GPIOA, GPIO_PIN_0, KEY_RELEASED, 50, 0},  // KEY2  
    {GPIOC, GPIO_PIN_6, KEY_RELEASED, 50, 0},  // KEY3
    {GPIOC, GPIO_PIN_7, KEY_RELEASED, 50, 0}   // KEY4
};

HAL_StatusTypeDef BSP_KEY_Init(void)
{
    return HAL_OK;
}

void BSP_KEY_Scan(void)
{
    for(int i = 0; i < 4; i++) {
        GPIO_PinState pin_state = HAL_GPIO_ReadPin(key_config[i].GPIOx, key_config[i].GPIO_Pin);
        KEY_State_TypeDef new_state = (pin_state == GPIO_PIN_RESET) ? KEY_PRESSED : KEY_RELEASED;
        
        if(new_state != key_config[i].state) {
            if(HAL_GetTick() - key_config[i].last_change >= key_config[i].debounce_time) {
                key_config[i].state = new_state;
                key_config[i].last_change = HAL_GetTick();
            }
        }
    }
}

KEY_State_TypeDef BSP_KEY_GetState(KEY_ID_TypeDef key_id)
{
    if(key_id < 4) {
        return key_config[key_id].state;
    }
    return KEY_RELEASED;
}




