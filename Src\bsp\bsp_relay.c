#include "bsp/bsp_relay.h"

/* 继电器配置结构体 */
typedef struct {
    GPIO_TypeDef* GPIOx;
    uint16_t GPIO_Pin;
    RELAY_State_TypeDef state;
} RELAY_Config_TypeDef;

/* 继电器配置表 */
static RELAY_Config_TypeDef relay_config[2] = {
    {GPIOB, GPIO_PIN_0, RELAY_OFF},  // RELAY_CHANNEL1
    {GPIOB, GPIO_PIN_2, RELAY_OFF}   // RELAY_CHANNEL2
};

HAL_StatusTypeDef BSP_RELAY_Init(void)
{
    return HAL_OK;
}

void BSP_RELAY_On(RELAY_Channel_TypeDef channel)
{
    if(channel < 2) {
        HAL_GPIO_WritePin(relay_config[channel].GPIOx, relay_config[channel].GPIO_Pin, GPIO_PIN_SET);
        relay_config[channel].state = RELAY_ON;
    }
}

void BSP_RELAY_Off(RELAY_Channel_TypeDef channel)
{
    if(channel < 2) {
        HAL_GPIO_WritePin(relay_config[channel].GPIOx, relay_config[channel].GPIO_Pin, GPIO_PIN_RESET);
        relay_config[channel].state = RELAY_OFF;
    }
}

void BSP_RELAY_Toggle(RELAY_Channel_TypeDef channel)
{
    if(channel < 2) {
        if(relay_config[channel].state == RELAY_ON) {
            BSP_RELAY_Off(channel);
        } else {
            BSP_RELAY_On(channel);
        }
    }
}

RELAY_State_TypeDef BSP_RELAY_GetState(RELAY_Channel_TypeDef channel)
{
    if(channel < 2) {
        return relay_config[channel].state;
    }
    return RELAY_OFF;
}

void BSP_RELAY_Process(void)
{
    // 继电器处理逻辑（如果需要的话）
}




