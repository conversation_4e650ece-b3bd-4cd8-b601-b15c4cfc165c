--cpu=Cortex-M4.fp.sp --branchpatch=sdcomp-29491-629360
"testboard\startup_stm32l431xx.o"
"testboard\main.o"
"testboard\stm32l4xx_it.o"
"testboard\stm32l4xx_hal_msp.o"
"testboard\stm32l4xx_hal_adc.o"
"testboard\stm32l4xx_hal_adc_ex.o"
"testboard\stm32l4xx_hal.o"
"testboard\stm32l4xx_hal_i2c.o"
"testboard\stm32l4xx_hal_i2c_ex.o"
"testboard\stm32l4xx_hal_rcc.o"
"testboard\stm32l4xx_hal_rcc_ex.o"
"testboard\stm32l4xx_hal_flash.o"
"testboard\stm32l4xx_hal_flash_ex.o"
"testboard\stm32l4xx_hal_flash_ramfunc.o"
"testboard\stm32l4xx_hal_gpio.o"
"testboard\stm32l4xx_hal_dma.o"
"testboard\stm32l4xx_hal_dma_ex.o"
"testboard\stm32l4xx_hal_pwr.o"
"testboard\stm32l4xx_hal_pwr_ex.o"
"testboard\stm32l4xx_hal_cortex.o"
"testboard\stm32l4xx_hal_uart.o"
"testboard\stm32l4xx_hal_uart_ex.o"
"testboard\stm32l4xx_hal_spi.o"
"testboard\stm32l4xx_hal_spi_ex.o"
"testboard\stm32l4xx_hal_tim.o"
"testboard\stm32l4xx_hal_tim_ex.o"
"testboard\system_stm32l4xx.o"
--strict --scatter "testboard\testboard.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "testboard.map" -o testboard\testboard.axf