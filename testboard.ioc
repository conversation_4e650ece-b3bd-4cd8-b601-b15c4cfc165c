#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_3
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,NbrOfConversionFlag,master
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
KeepUserPlacement=false
Mcu.CPN=STM32L431RCT6
Mcu.Family=STM32L4
Mcu.IP0=ADC1
Mcu.IP1=I2C1
Mcu.IP2=I2C2
Mcu.IP3=LPUART1
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SPI1
Mcu.IP7=SYS
Mcu.IPNb=8
Mcu.Name=STM32L431R(B-C)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC13
Mcu.Pin1=PC14-OSC32_IN (PC14)
Mcu.Pin10=PA2
Mcu.Pin11=PA3
Mcu.Pin12=PA5
Mcu.Pin13=PA6
Mcu.Pin14=PA7
Mcu.Pin15=PC4
Mcu.Pin16=PC5
Mcu.Pin17=PB0
Mcu.Pin18=PB1
Mcu.Pin19=PB2
Mcu.Pin2=PC15-OSC32_OUT (PC15)
Mcu.Pin20=PB10
Mcu.Pin21=PB11
Mcu.Pin22=PC6
Mcu.Pin23=PC7
Mcu.Pin24=PC8
Mcu.Pin25=PC9
Mcu.Pin26=PA12
Mcu.Pin27=PD2
Mcu.Pin28=PB3 (JTDO-TRACESWO)
Mcu.Pin29=PB4 (NJTRST)
Mcu.Pin3=PH0-OSC_IN (PH0)
Mcu.Pin30=PB5
Mcu.Pin31=PB6
Mcu.Pin32=PB7
Mcu.Pin33=PB8
Mcu.Pin34=PB9
Mcu.Pin35=VP_SYS_VS_Systick
Mcu.Pin4=PH1-OSC_OUT (PH1)
Mcu.Pin5=PC0
Mcu.Pin6=PC1
Mcu.Pin7=PC2
Mcu.Pin8=PC3
Mcu.Pin9=PA1
Mcu.PinsNb=36
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32L431RCTx
MxCube.Version=4.24.0
MxDb.Version=DB.4.0.240
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA1.Locked=true
PA1.Signal=GPIO_Input
PA12.Locked=true
PA12.Signal=GPIO_Output
PA2.Locked=true
PA2.Signal=GPIO_Input
PA3.Locked=true
PA3.Signal=GPIO_Input
PA5.Locked=true
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Locked=true
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Locked=true
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PB0.Locked=true
PB0.Signal=GPIO_Output
PB1.Locked=true
PB1.Signal=GPIO_Input
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB2.Locked=true
PB2.Signal=GPIO_Output
PB3\ (JTDO-TRACESWO).Locked=true
PB3\ (JTDO-TRACESWO).Signal=GPIO_Input
PB4\ (NJTRST).Locked=true
PB4\ (NJTRST).Signal=GPIO_Input
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.Locked=true
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Locked=true
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PB8.Locked=true
PB8.Signal=GPIO_Output
PB9.Locked=true
PB9.Signal=GPIO_Output
PC0.Locked=true
PC0.Mode=Asynchronous
PC0.Signal=LPUART1_RX
PC1.Locked=true
PC1.Mode=Asynchronous
PC1.Signal=LPUART1_TX
PC13.Locked=true
PC13.Signal=GPIO_Output
PC14-OSC32_IN\ (PC14).Mode=LSE-External-Oscillator
PC14-OSC32_IN\ (PC14).Signal=RCC_OSC32_IN
PC15-OSC32_OUT\ (PC15).Mode=LSE-External-Oscillator
PC15-OSC32_OUT\ (PC15).Signal=RCC_OSC32_OUT
PC2.Locked=true
PC2.Signal=ADCx_IN3
PC3.Locked=true
PC3.Signal=ADCx_IN4
PC4.Locked=true
PC4.Signal=GPIO_Input
PC5.Locked=true
PC5.Signal=GPIO_Input
PC6.Locked=true
PC6.Signal=GPIO_Input
PC7.Locked=true
PC7.Signal=GPIO_Input
PC8.Locked=true
PC8.Signal=GPIO_Output
PC9.Locked=true
PC9.Signal=GPIO_Output
PD2.Locked=true
PD2.Signal=GPIO_Input
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=2
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32L431RCTx
ProjectManager.FirmwarePackage=STM32Cube FW_L4 V1.11.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=testboard.ioc
ProjectManager.ProjectName=testboard
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-true,3-MX_I2C2_Init-I2C2-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_LPUART1_UART_Init-LPUART1-false-HAL-true,6-MX_ADC1_Init-ADC1-false-HAL-true,7-MX_SPI1_Init-SPI1-false-HAL-true
RCC.ADCFreq_Value=32000000
RCC.AHBFreq_Value=80000000
RCC.APB1Freq_Value=80000000
RCC.APB1TimFreq_Value=80000000
RCC.APB2Freq_Value=80000000
RCC.APB2TimFreq_Value=80000000
RCC.CortexFreq_Value=80000000
RCC.FCLKCortexFreq_Value=80000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=80000000
RCC.HSE_VALUE=8000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=80000000
RCC.I2C2Freq_Value=80000000
RCC.I2C3Freq_Value=80000000
RCC.IPParameters=ADCFreq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSI_VALUE,MCO1PinFreq_Value,MSI_VALUE,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSAI1PoutputFreq_Value,PLLSAI1QoutputFreq_Value,PLLSAI1RoutputFreq_Value,PLLSourceVirtual,PWRFreq_Value,RNGFreq_Value,SAI1Freq_Value,SDMMCFreq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAI1OutputFreq_Value
RCC.LPTIM1Freq_Value=80000000
RCC.LPTIM2Freq_Value=80000000
RCC.LPUART1Freq_Value=80000000
RCC.LSCOPinFreq_Value=32000
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=80000000
RCC.MSI_VALUE=4000000
RCC.PLLN=20
RCC.PLLPoutputFreq_Value=22857142.85714286
RCC.PLLQoutputFreq_Value=80000000
RCC.PLLRCLKFreq_Value=80000000
RCC.PLLSAI1PoutputFreq_Value=9142857.142857144
RCC.PLLSAI1QoutputFreq_Value=32000000
RCC.PLLSAI1RoutputFreq_Value=32000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWRFreq_Value=80000000
RCC.RNGFreq_Value=32000000
RCC.SAI1Freq_Value=9142857.142857144
RCC.SDMMCFreq_Value=32000000
RCC.SWPMI1Freq_Value=80000000
RCC.SYSCLKFreq_VALUE=80000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.USART1Freq_Value=80000000
RCC.USART2Freq_Value=80000000
RCC.USART3Freq_Value=80000000
RCC.VCOInputFreq_Value=8000000
RCC.VCOOutputFreq_Value=160000000
RCC.VCOSAI1OutputFreq_Value=64000000
SH.ADCx_IN3.0=ADC1_IN3,IN3-Single-Ended
SH.ADCx_IN3.ConfNb=1
SH.ADCx_IN4.0=ADC1_IN4,IN4-Single-Ended
SH.ADCx_IN4.ConfNb=1
SPI1.CalculateBaudRate=40.0 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=testboard
