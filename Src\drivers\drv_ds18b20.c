/**
 * @file    drv_ds18b20.c
 * @brief   DS18B20数字温度传感器驱动实现
 * <AUTHOR>
 * @date    2025-01-28
 */

#include "drivers/drv_ds18b20.h"

/* DS18B20引脚定义 */
static const struct {
    GPIO_TypeDef *GPIOx;
    uint16_t GPIO_Pin;
} ds18b20_pins[2] = {
    {GPIOA, GPIO_PIN_2},  // CHANNEL1
    {GPIOA, GPIO_PIN_4}   // CHANNEL2
};

/**
 * @brief  设置引脚为输出模式
 * @param  channel: DS18B20通道
 * @retval None
 */
static void DS18B20_SetPinOutput(DS18B20_Channel_TypeDef channel)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    GPIO_InitStruct.Pin = ds18b20_pins[channel].GPIO_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(ds18b20_pins[channel].GPIOx, &GPIO_InitStruct);
}

/**
 * @brief  设置引脚为输入模式
 * @param  channel: DS18B20通道
 * @retval None
 */
static void DS18B20_SetPinInput(DS18B20_Channel_TypeDef channel)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    GPIO_InitStruct.Pin = ds18b20_pins[channel].GPIO_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(ds18b20_pins[channel].GPIOx, &GPIO_InitStruct);
}

/**
 * @brief  写引脚电平
 * @param  channel: DS18B20通道
 * @param  state: 引脚状态
 * @retval None
 */
static void DS18B20_WritePin(DS18B20_Channel_TypeDef channel, GPIO_PinState state)
{
    HAL_GPIO_WritePin(ds18b20_pins[channel].GPIOx, ds18b20_pins[channel].GPIO_Pin, state);
}

/**
 * @brief  读引脚电平
 * @param  channel: DS18B20通道
 * @retval 引脚状态
 */
static GPIO_PinState DS18B20_ReadPin(DS18B20_Channel_TypeDef channel)
{
    return HAL_GPIO_ReadPin(ds18b20_pins[channel].GPIOx, ds18b20_pins[channel].GPIO_Pin);
}

/**
 * @brief  微秒延时
 * @param  us: 延时时间（微秒）
 * @retval None
 */
static void DS18B20_DelayUs(uint32_t us)
{
    uint32_t start = DWT->CYCCNT;
    uint32_t cycles = us * (SystemCoreClock / 1000000);
    while((DWT->CYCCNT - start) < cycles);
}

/**
 * @brief  复位脉冲
 * @param  channel: DS18B20通道
 * @retval 1-设备存在，0-设备不存在
 */
static uint8_t DS18B20_Reset(DS18B20_Channel_TypeDef channel)
{
    uint8_t presence = 0;
    
    DS18B20_SetPinOutput(channel);
    DS18B20_WritePin(channel, GPIO_PIN_RESET);
    DS18B20_DelayUs(480);  // 复位脉冲，至少480us
    
    DS18B20_SetPinInput(channel);
    DS18B20_DelayUs(70);   // 等待15-60us后检测存在脉冲
    
    if(DS18B20_ReadPin(channel) == GPIO_PIN_RESET) {
        presence = 1;  // 检测到存在脉冲
    }
    
    DS18B20_DelayUs(410);  // 等待存在脉冲结束
    
    return presence;
}

/**
 * @brief  写一个位
 * @param  channel: DS18B20通道
 * @param  bit: 位值
 * @retval None
 */
static void DS18B20_WriteBit(DS18B20_Channel_TypeDef channel, uint8_t bit)
{
    DS18B20_SetPinOutput(channel);
    DS18B20_WritePin(channel, GPIO_PIN_RESET);
    
    if(bit) {
        DS18B20_DelayUs(10);  // 写1：拉低10us后释放
        DS18B20_SetPinInput(channel);
        DS18B20_DelayUs(55);  // 总时隙60us
    } else {
        DS18B20_DelayUs(60);  // 写0：拉低60us
        DS18B20_SetPinInput(channel);
        DS18B20_DelayUs(5);   // 恢复时间
    }
}

/**
 * @brief  读一个位
 * @param  channel: DS18B20通道
 * @retval 位值
 */
static uint8_t DS18B20_ReadBit(DS18B20_Channel_TypeDef channel)
{
    uint8_t bit = 0;
    
    DS18B20_SetPinOutput(channel);
    DS18B20_WritePin(channel, GPIO_PIN_RESET);
    DS18B20_DelayUs(3);    // 启动读时隙
    
    DS18B20_SetPinInput(channel);
    DS18B20_DelayUs(10);   // 等待数据稳定
    
    if(DS18B20_ReadPin(channel) == GPIO_PIN_SET) {
        bit = 1;
    }
    
    DS18B20_DelayUs(50);   // 完成读时隙
    
    return bit;
}

/**
 * @brief  写一个字节
 * @param  channel: DS18B20通道
 * @param  data: 字节数据
 * @retval None
 */
static void DS18B20_WriteByte(DS18B20_Channel_TypeDef channel, uint8_t data)
{
    for(uint8_t i = 0; i < 8; i++) {
        DS18B20_WriteBit(channel, (data >> i) & 0x01);
    }
}

/**
 * @brief  读一个字节
 * @param  channel: DS18B20通道
 * @retval 字节数据
 */
static uint8_t DS18B20_ReadByte(DS18B20_Channel_TypeDef channel)
{
    uint8_t data = 0;
    
    for(uint8_t i = 0; i < 8; i++) {
        if(DS18B20_ReadBit(channel)) {
            data |= (1 << i);
        }
    }
    
    return data;
}

/**
 * @brief  DS18B20初始化
 * @param  None
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_DS18B20_Init(void)
{
    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOA_CLK_ENABLE();
    
    /* 使能DWT计数器用于微秒延时 */
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
    
    /* 初始化引脚为输入模式 */
    DS18B20_SetPinInput(DS18B20_CHANNEL1);
    DS18B20_SetPinInput(DS18B20_CHANNEL2);
    
    /* 检测设备是否存在 */
    if(!DRV_DS18B20_IsPresent(DS18B20_CHANNEL1) && !DRV_DS18B20_IsPresent(DS18B20_CHANNEL2)) {
        return HAL_ERROR;  // 没有检测到任何设备
    }
    
    /* 设置默认分辨率为12位 */
    DRV_DS18B20_SetResolution(DS18B20_CHANNEL1, DS18B20_RESOLUTION_12BIT);
    DRV_DS18B20_SetResolution(DS18B20_CHANNEL2, DS18B20_RESOLUTION_12BIT);
    
    return HAL_OK;
}

/**
 * @brief  启动温度转换
 * @param  channel: DS18B20通道
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_DS18B20_StartConversion(DS18B20_Channel_TypeDef channel)
{
    if(!DS18B20_Reset(channel)) {
        return HAL_ERROR;  // 设备不存在
    }
    
    DS18B20_WriteByte(channel, DS18B20_CMD_SKIP_ROM);
    DS18B20_WriteByte(channel, DS18B20_CMD_CONVERT_T);
    
    return HAL_OK;
}

/**
 * @brief  读取温度
 * @param  channel: DS18B20通道
 * @param  temperature: 温度值指针
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_DS18B20_ReadTemp(DS18B20_Channel_TypeDef channel, float *temperature)
{
    uint8_t temp_data[9];
    int16_t temp_raw;
    
    if(!DS18B20_Reset(channel)) {
        return HAL_ERROR;  // 设备不存在
    }
    
    DS18B20_WriteByte(channel, DS18B20_CMD_SKIP_ROM);
    DS18B20_WriteByte(channel, DS18B20_CMD_READ_SCRATCHPAD);
    
    /* 读取9字节数据 */
    for(uint8_t i = 0; i < 9; i++) {
        temp_data[i] = DS18B20_ReadByte(channel);
    }
    
    /* 简单的CRC校验（可选） */
    // TODO: 添加CRC校验
    
    /* 组合温度数据 */
    temp_raw = (temp_data[1] << 8) | temp_data[0];
    
    /* 转换为摄氏度 */
    *temperature = (float)temp_raw * 0.0625f;
    
    return HAL_OK;
}

/**
 * @brief  设置分辨率
 * @param  channel: DS18B20通道
 * @param  resolution: 分辨率
 * @retval HAL状态
 */
HAL_StatusTypeDef DRV_DS18B20_SetResolution(DS18B20_Channel_TypeDef channel, DS18B20_Resolution_TypeDef resolution)
{
    if(!DS18B20_Reset(channel)) {
        return HAL_ERROR;  // 设备不存在
    }
    
    DS18B20_WriteByte(channel, DS18B20_CMD_SKIP_ROM);
    DS18B20_WriteByte(channel, DS18B20_CMD_WRITE_SCRATCHPAD);
    DS18B20_WriteByte(channel, 0x00);  // TH寄存器
    DS18B20_WriteByte(channel, 0x00);  // TL寄存器
    DS18B20_WriteByte(channel, resolution);  // 配置寄存器
    
    /* 复制到EEPROM */
    if(!DS18B20_Reset(channel)) {
        return HAL_ERROR;
    }
    
    DS18B20_WriteByte(channel, DS18B20_CMD_SKIP_ROM);
    DS18B20_WriteByte(channel, DS18B20_CMD_COPY_SCRATCHPAD);
    
    return HAL_OK;
}

/**
 * @brief  检测设备是否存在
 * @param  channel: DS18B20通道
 * @retval 1-存在，0-不存在
 */
uint8_t DRV_DS18B20_IsPresent(DS18B20_Channel_TypeDef channel)
{
    return DS18B20_Reset(channel);
}